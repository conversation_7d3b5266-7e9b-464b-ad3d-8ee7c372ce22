<?php

$domain_config = get_resource_config('domain');
$allow_origin_str = $domain_config['domain']['allow_origin'];
$allow_origin_list = explode(",", $allow_origin_str);
$current_domain = $domain_config['domain']['pur_domain'];
return [
    'admin'        => ['<EMAIL>'],
    'login'        => [
        'login'     => $domain_config['domain']['login_domain'] . '/login',
        'logout'    => $domain_config['domain']['login_domain'] . '/logout?redirect=' . $current_domain,
        'check'     => $domain_config['domain']['login_domain'] . '/api/checklogin',
        'search'    => $domain_config['domain']['login_domain'] . '/api/search',
        'dashboard' => $domain_config['domain']['login_domain'] . '/dashboard',
    ],

    //允许跨域站点
    'ALLOW_ORIGIN' => $allow_origin_list,

    // 'supplier_domain' => $domain_config['domain']['supplier_domain'],
    "cookieDomain" => $domain_config['domain']['cookieDomain'], //cookie 域

    "domain" => $domain_config['domain']['domain'],

    'api_domain' => $domain_config['domain']['api_domain'], // API

    'search_domain' => $domain_config['domain']['search_url'],

    'goods_server' => $domain_config['domain']['goods_server'],

    'cloud_domain' => $domain_config['domain']['cloud_domain'],


    "order_domain" => $domain_config['domain']['new_order_domain'], // 销售系统域名

    "pur_domain" => $domain_config['domain']['pur_domain'], // 采购系统域名

    "file_domain" => $domain_config['domain']['file_domain'], // 文件服务域名

    "frq_domain" => $domain_config['domain']['frq_domain'], // 询报价系统域名

    'sc_domain'         => $domain_config['domain']['sc_domain'],  //供应链域名
    'sc_2_domain'       => $domain_config['domain']['sc_2_domain']??"",//供应链2.0域名

    // 权限系统
    'perm_url'          => $domain_config['domain']['perm_url'],
    //老基石
    'footstone_url'     => $domain_config['domain']['footstone_url'],
    'footstone_new_url' => $domain_config['domain']['footstone_new_url'],
    // 获取用户权限接口
    'perm_api'          => $domain_config['domain']['perm_api'],

    'perm_id' => env('PERM_ID', 50),

    'qc_url' => $domain_config['domain']['qc_domain'],

    'wms_url' => $domain_config['domain']['wms_domain'],

    'audit_center_url'              => $domain_config['domain']['audit_center_domain'],

    // 获取用户许可权限接口
    'check_access_api'              => $domain_config['domain']['check_access_api'],


    //上传图片接口地址
    'UploadUrl'                     => $domain_config['upload']['upload_url'],
    'UploadKey'                     => $domain_config['upload']['upload_key'],

    //供应商系统接口地址
    'SupplierUrl'                   => $domain_config['domain']['supplier_domain'],

    // crm系统地址
    'crm_domain'                    => $domain_config['domain']['crm_new_domain'],
    //消息接口
    'message_domain'                => $domain_config['domain']['message_domain'] ?? "http://192.168.1.252:16543",

    // 审核流程id
    "flow"                          => [
        'pur_purchase_flow_id'           => get_resource_config_section('app', 'pur')['pur_purchase_flow_id'],
        'pur_payment_flow_id'            => get_resource_config_section('app', 'pur')['pur_payment_flow_id'],
        'pur_order_service_flow_id'      => get_resource_config_section('app', 'pur')['pur_order_service_flow_id'],
        'pur_return_flow_id'             => get_resource_config_section('app', 'pur')['pur_return_flow_id'],
        'pur_pur_plan_flow_id'           => get_resource_config_section('app', 'pur')['pur_pur_plan_flow_id'],
        'pur_transfer_warehouse_flow_id' => get_resource_config_section('app', 'pur')['pur_transfer_warehouse_flow_id'],
        //        'pur_sale_order_unbind_flow_id' => get_resource_config_section('app', 'pur')['pur_sale_order_unbind_flow_id'],
    ],
    'SUPPLIER_LOGIN_URL'            => get_resource_config_section('app', 'pur')['supplier_login_url'],
    'SUPPLIER_URL'                  => get_resource_config_section('app', 'pur')['supplier_url'],
    'GYL_SCM_URL'                   => get_resource_config_section('app', 'pur')['gyl_scm_url'] ?? "http://192.168.1.235:6777/ormrpc/services/WSExternalInterfaceCallFacade?wsdl",
    'JD_GET_JD_REGION_DATA_API_URL' => get_resource_config_section('app', 'pur')['JD_GET_JD_REGION_DATA_API_URL'],
    'JD_GET_DELIVERY_DATA_API_URL'  => get_resource_config_section('app', 'pur')['JD_GET_DELIVERY_DATA_API_URL'],


    'SUPPLIER_CONFIG' => explode(',', get_resource_config_section('app', 'pur')['supplier_config']),

];
