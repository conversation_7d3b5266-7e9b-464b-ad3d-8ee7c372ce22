<?php

namespace App\Http\Controllers;


use App\Enum\PurCommonEnum;
use App\Exceptions\InvalidRequestException;
use App\Exports\PurContractHkExcelExport;
use App\Exports\PurContractUsExcelExport;
use App\Exports\PurContractExport;
use App\Exports\SupplierReconciliationExport;
use App\Exports\UnFinishedPurOrderExport;
use App\Http\Caches\RateCache;
use App\Http\Models\CmsUserDepartmentModel;
use App\Http\Models\CmsUserInfoModel;
use App\Http\Models\ConsignmentOrderModel;
use App\Http\Models\ConsignmentStockInModel;
use App\Http\Models\PaymentItemsModel;
use App\Http\Models\PaymentModel;
use App\Http\Models\PurchaseContractModel;
use App\Http\Models\PurchaseItemsModel;
use App\Http\Models\PurchaseOrderModel;
use App\Http\Models\StockInItemModel;
use App\Http\Models\StockInModel;
use App\Http\Models\Wms\StockInPrecheckModel;
use App\Http\Services\Api\Audit\AuditPurOrderService;
use App\Http\Services\Api\PurContractService;
use App\Http\Services\Api\PurOrderApiService;
use App\Http\Services\ApproveService;
use App\Http\Services\CmsUserService;
use App\Http\Services\CommonService;
use App\Http\Services\FrqService;
use App\Http\Services\GoodsService;
use App\Http\Services\MenuService;
use App\Http\Services\PermService;
use App\Http\Services\PurchasePlanService;
use App\Http\Services\PurOrderService;
use App\Http\Services\PurPdfService;
use App\Http\Services\SignCompanyService;
use App\Http\Services\SupplierDeliveryService;
use App\Http\Services\SupplierService;
use Illuminate\Http\Request;
use App\Http\Services\PurchaseUserService;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\App;
use App\Http\Services\ReturnMaterialService;
use Maatwebsite\Excel\Facades\Excel;

class PurDemandController extends Controller
{

    public function purDemand(Request $request)
    {
        $menu_list = MenuService::getMenu();
        $frqService = new FrqService;
        $luopan = $frqService->getStatusNums();

        $purchaseUserService = new PurchaseUserService;
        $allPurUser = $purchaseUserService->getPurchaseUsers();
        return view('purDemand.purDemand', ['menu_list' => $menu_list, "statusNums" => $luopan, "allPurUser" => $allPurUser,]);
    }

    public function purDeleteDemand(Request $request)
    {
        $menu_list = MenuService::getMenu();
        $frqService = new FrqService;
        $luopan = $frqService->getStatusNums();

        $purchaseUserService = new PurchaseUserService;
        $allPurUser = $purchaseUserService->getPurchaseUsers();
        return view('purDemand.purDeleteDemand', ['menu_list' => $menu_list, "statusNums" => $luopan, "allPurUser" => $allPurUser]);
    }

    public function multiImport(Request $request)
    {
        $menu_list = MenuService::getMenu();

        return view('purDemand.multiImport', ['menu_list' => $menu_list]);
    }

    public function multiImportStockIn(Request $request)
    {
        $menu_list = MenuService::getMenu();

        return view('purDemand.multiImportStockIn', ['menu_list' => $menu_list]);
    }

    public function multiImportStockInShipment(Request $request)
    {
        $menu_list = MenuService::getMenu();

        return view('purDemand.multiImportStockInShipment', ['menu_list' => $menu_list]);
    }

    public function multiImportStockInShipmentFromScm(Request $request)
    {
        $menu_list = MenuService::getMenu();

        return view('purDemand.multiImportStockInShipmentFromScm', ['menu_list' => $menu_list]);
    }

    public function purAdd(Request $request)
    {
        $menu_list = MenuService::getMenu();
        $rate = (new RateCache)->getRate();
        $produceCateParentList = PurOrderService::getProduceCateList(0);

        $allRate = (new RateCache)->getAllRate();


        //        $currencyList = [];
        //        foreach(config("field.CurrencyEn") as $currency_id => $currenName){
        //            $_rate = \Arr::get($allRate,config("field.CurrencyEn")[$currency_id],0);
        //            if($_rate<=0){
        //                continue;
        //            }
        //            $currencyList[$currency_id]["id"] = $currency_id;
        //            $currencyList[$currency_id]["code"] = $currenName;
        //            $currencyList[$currency_id]["exchange_rate"] = $_rate;
        //            $currencyList[$currency_id]["symbol"] = \Arr::get(config("field.currency_sign"),$currency_id,"");
        //            $currencyList[$currency_id]["currencyCn"] = \Arr::get(config("field.Currency"),$currency_id,"");
        //        }
        $currencyList = PurchasePlanService::getCurrencyList();
        $user_info = CmsUserService::getCmsUserInfoById($request->user->userId);

        $currencyRateAndTaxList = CommonService::getComRate();
        //dd($currencyRateAndTaxList);
        return view('purDemand.purAdd', [
            'menu_list'                    => $menu_list,
            'user_email'                   => $user_info['email'] ?? "",
            'produceCateParentList'        => $produceCateParentList,
            //            "rate"                  => $rate,
            "currencyList"                 => $currencyList,
            "currencyRateAndTaxList"       => json_encode($currencyRateAndTaxList),
            "supplier_ids"                 => implode(",", [
                "11516",
                "1988",
                "2364",
                "12081",
                "4765",
                "1940",
                "1935",
                "803",
                "1985",
                "1995",
                "1939",
                "8136",
                "5684"
            ]),
            "is_auto_fill_number"          => PurOrderService::checkIsAutoFillNumber($request->user->userId),             //联营一部判断
            "is_default_no_qc"             => PurOrderService::checkIsDefaultNoQc($request->user->userId),                //是否默认不需要质检判断
            "is_huayun_gy_department_user" => PurOrderApiService::checkIsHuaYunGYPosition($user_info['department_id']),   //是否默认不需要质检判断
        ]);
    }

    public function pdf(Request $request)
    {
        $id = (int)$request->input('id', '0');
        if (empty($id)) {
            return $this->setError("id不存在");
        }
        $purPdfService = new PurPdfService();
        $purPdfResponseInfo = $purPdfService->getPurDemandPdfResponseData($id);
        $view = 'purDemand.pdf';
        $signViewPath = resource_path("views/purDemand/pur_contract_export/{$purPdfResponseInfo['orderInfo']['sign_company_id']}_CN.blade.php");
        //        dd($purPdfInfo);
        if (file_exists($signViewPath)&&$request->input("is_old",0)==0) {
            $view = "purDemand.pur_contract_export.{$purPdfResponseInfo['orderInfo']['sign_company_id']}_CN";
        }
        $html = view($view, ['pdfInfo' => $purPdfResponseInfo])->render();
        if ($request->input('debug') == 1) {
            return $html;
        }
        $pdf = App::make('dompdf.wrapper');
        $name = 'service_' . time() . '.pdf';
        $pdf->loadHTML($html)->setPaper('a4', 'portrait') //横列 ,  竖列 portrait
        ->setOptions(['enable_font_subsetting' => true])->setWarnings(false);
        return $pdf->stream($name);
    }

    public function pdfHk(Request $request)
    {
        $id = (int)$request->input('id');
        if (empty($id)) {
            return $this->setError("id不存在");
        }
        $purPdfService = new PurPdfService();
        $purPdfResponseInfo = $purPdfService->getPurDemandPdfResponseData($id);
        //        $purPdfResponseInfo['orderInfo']['status']=2;
        $view = 'purDemand.pdfHk';
        $signViewPath = resource_path("views/purDemand/pur_contract_export/{$purPdfResponseInfo['orderInfo']['sign_company_id']}_HK.blade.php");
        //        dd($purPdfInfo);
        if (file_exists($signViewPath)&&$request->input("is_old",0)==0) {
            $view = "purDemand.pur_contract_export.{$purPdfResponseInfo['orderInfo']['sign_company_id']}_HK";
        }
        $html = view($view, ['pdfInfo' => $purPdfResponseInfo])->render();
        if ($request->input('debug') == 1) {
            return $html;
        }
        $pdf = App::make('dompdf.wrapper');
        $name = 'service_' . time() . '.pdf';
        $pdf->loadHTML($html)->setPaper('a4', 'portrait') //横列 ,  竖列 portrait
        ->setOptions(['enable_font_subsetting' => true])->setWarnings(false);
        return $pdf->stream($name);
    }


    public function pdfHuaYun(Request $request)
    {
        $id = (int)$request->input('id');
        if (empty($id)) {
            return $this->setError("id不存在");
        }
        try {
            $purPdfInfo = PurOrderService::getPurDemandPdfData($id);
            $purPdfResponseInfo = PurOrderService::buildPdfResponseData($purPdfInfo);
        } catch (\Throwable $throwable) {
            echo $throwable->getMessage();
            return;
        }
        $html = view('purDemand.pdf', ['pdfInfo' => $purPdfResponseInfo])->render();
        if ($request->input('debug') == 1) {
            return $html;
        }
        $pdf = App::make('dompdf.wrapper');
        $name = 'service_' . time() . '.pdf';
        $pdf->loadHTML($html)->setPaper('a4', 'portrait') //横列 ,  竖列 portrait
        ->setOptions(['enable_font_subsetting' => true])->setWarnings(false);
        return $pdf->stream($name);
    }

    public function pdfYueFeng(Request $request)
    {
        $id = (int)$request->input('id');
        if (empty($id)) {
            return $this->setError("id不存在");
        }
        try {
            $purPdfInfo = PurOrderService::getPurDemandPdfData($id);
            $purPdfResponseInfo = PurOrderService::buildPdfResponseData($purPdfInfo);
        } catch (\Throwable $throwable) {
            echo $throwable->getMessage();
            return;
        }


        $html = view('purDemand.pdf', ['pdfInfo' => $purPdfResponseInfo])->render();
        if ($request->input('debug') == 1) {
            return $html;
        }
        $pdf = App::make('dompdf.wrapper');
        $name = 'service_' . time() . '.pdf';
        $pdf->loadHTML($html)->setPaper('a4', 'portrait') //横列 ,  竖列 portrait
        ->setOptions(['enable_font_subsetting' => true])->setWarnings(false);
        return $pdf->stream($name);
    }

    public function pdfGPShuZi(Request $request)
    {
        $id = (int)$request->input('id');
        if (empty($id)) {
            return $this->setError("id不存在");
        }
        try {
            $purPdfInfo = PurOrderService::getPurDemandPdfData($id);
            $purPdfResponseInfo = PurOrderService::buildPdfResponseData($purPdfInfo);
        } catch (\Throwable $throwable) {
            echo $throwable->getMessage();
            return;
        }
        $html = view('purDemand.pdf', ['pdfInfo' => $purPdfResponseInfo])->render();
        if ($request->input('debug') == 1) {
            return $html;
        }
        $pdf = App::make('dompdf.wrapper');
        $name = 'service_' . time() . '.pdf';
        $pdf->loadHTML($html)->setPaper('a4', 'portrait') //横列 ,  竖列 portrait
        ->setOptions(['enable_font_subsetting' => true])->setWarnings(false);
        return $pdf->stream($name);
    }

    public function pdfUs(Request $request)
    {
        $id = (int)$request->input('id');
        if (empty($id)) {
            return $this->setError("id不存在");
        }

        try {
            // 优化后的PDF数据获取
            $pdfService = new PurPdfService();
            $purPdfResponseInfo = $pdfService->getPurDemandPdfResponseData($id);

            $view = 'purDemand.pdfUs';
            $signViewPath = resource_path("views/purDemand/pur_contract_export/{$purPdfResponseInfo['orderInfo']['sign_company_id']}_US.blade.php");
            if (file_exists($signViewPath)&&$request->input("is_old",0)==0) {
                $view = "purDemand.pur_contract_export.{$purPdfResponseInfo['orderInfo']['sign_company_id']}_US";
            }
            $html = view($view, ['pdfInfo' => $purPdfResponseInfo])->render();
            if ($request->input('debug') == 1) {
                return $html;
            }

            $pdf = App::make('dompdf.wrapper');
            $name = 'service_' . time() . '.pdf';
            $pdf->loadHTML($html)->setPaper('a4', 'portrait')->setOptions(['enable_font_subsetting' => true])->setWarnings(false);
            return $pdf->stream($name);
        } catch (\Throwable $throwable) {
            throw $throwable;
        }
    }

    public function pdfExcel(Request $request)
    {
        $id = (int)$request->input('id', '0');
        if (empty($id)) {
            return $this->setError("id不存在");
        }
        try {
            // 优化后的PDF数据获取
            $pdfService = new PurPdfService(true);
            $purPdfResponseInfo = $pdfService->getPurDemandPdfResponseData($id);
            $signViewPath = resource_path("views/purDemand/pur_contract_export/{$purPdfResponseInfo['orderInfo']['sign_company_id']}_CN.blade.php");
            if (file_exists($signViewPath)&&$request->input("is_old",0)==0) {
                return Excel::download(new PurContractUsExcelExport($purPdfResponseInfo,"CN"), "pur_contract_excel" . '.xlsx');
            }
            if (in_array($purPdfResponseInfo['orderInfo']['company_id'], PurCommonEnum::$HUAYUN_COMPANY)) {
                PurContractExport::exportHuaYun($purPdfResponseInfo);
            } else {
                PurContractExport::exportCn($purPdfResponseInfo);
            }
        } catch (\Throwable $throwable) {
            echo $throwable->getTraceAsString();
            return;
        }
    }

    public function pdfExcelHuaYun(Request $request)
    {
        $id = (int)$request->input('id', '0');
        if (empty($id)) {
            return $this->setError("id不存在");
        }
        try {
            $purPdfInfo = PurOrderService::getPurDemandPdfData($id);
            $purPdfResponseInfo = PurOrderService::buildPdfResponseData($purPdfInfo);
            PurContractExport::exportHuaYun($purPdfResponseInfo);
        } catch (\Throwable $throwable) {
            echo $throwable->getMessage();
            return;
        }
    }

    public function pdfExcelYueFeng(Request $request)
    {
        $id = (int)$request->input('id', '0');
        if (empty($id)) {
            return $this->setError("id不存在");
        }
        try {
            $purPdfInfo = PurOrderService::getPurDemandPdfData($id);
            $purPdfResponseInfo = PurOrderService::buildPdfResponseData($purPdfInfo);
            PurContractExport::exportExcelYueFeng($purPdfResponseInfo);
        } catch (\Throwable $throwable) {
            echo $throwable->getMessage();
            return;
        }
    }

    public function pdfExcelGPShuZi(Request $request)
    {
        $id = (int)$request->input('id', '0');
        if (empty($id)) {
            return $this->setError("id不存在");
        }
        try {
            $purPdfInfo = PurOrderService::getPurDemandPdfData($id);
            $purPdfResponseInfo = PurOrderService::buildPdfResponseData($purPdfInfo);
            PurContractExport::exportExcelGPShuZi($purPdfResponseInfo);
        } catch (\Throwable $throwable) {
            echo $throwable->getMessage();
            return;
        }
    }

    public function pdfExcelHk(Request $request)
    {
        $id = (int)$request->input('id', '0');
        if (empty($id)) {
            return $this->setError("id不存在");
        }
        try {
            // 优化后的PDF数据获取
            $pdfService = new PurPdfService(true);
            $purPdfResponseInfo = $pdfService->getPurDemandPdfResponseData($id);
            $signViewPath = resource_path("views/purDemand/pur_contract_export/{$purPdfResponseInfo['orderInfo']['sign_company_id']}_HK.blade.php");
            if (file_exists($signViewPath)&&$request->input("is_old",0)==0) {
                $purPdfResponseInfo['is_excel'] = 1;
                return Excel::download(new PurContractHkExcelExport($purPdfResponseInfo,"HK"), "pur_contract_excel" . '.xlsx');
            }else{
                //如果是深茂的合同
                PurContractExport::exportHk($purPdfResponseInfo);
            }
        } catch (\Throwable $throwable) {
            echo $throwable->getMessage();
            return;
        }
    }

    public function pdfExcelUs(Request $request)
    {
        $id = (int)$request->input('id', '0');
        if (empty($id)) {
            return $this->setError("id不存在");
        }
        try {
            // 优化后的PDF数据获取
            $pdfService = new PurPdfService(true);
            $purPdfResponseInfo = $pdfService->getPurDemandPdfResponseData($id);
            $purPdfResponseInfo['is_excel'] = 1;

            //如果是深茂的合同
//            $purPdfResponseInfo['orderInfo']['sign_company_id'] = 105;
            $signViewPath = resource_path("views/purDemand/pur_contract_export/{$purPdfResponseInfo['orderInfo']['sign_company_id']}_US.blade.php");
            //            dd($signViewPath,$purPdfInfo,file_exists($signViewPath));
            if (file_exists($signViewPath)&&$request->input("is_old",0)==0) {

                return Excel::download(new PurContractUsExcelExport($purPdfResponseInfo), "pur_contract_excel" . '.xlsx');
            }
            //            var_dump($signViewPath);
            PurContractExport::exportEn($purPdfResponseInfo);
        } catch (\Throwable $throwable) {
            dd((string)$throwable);
            //            throw $throwable;
            echo $throwable->getMessage();
            return;
        }
    }

    public function purOrder(Request $request)
    {
        $menu_list = MenuService::getMenu();
        $luopan = (new PurOrderService)->getPurLuoPan();
        return view('purDemand.purOrder', ['menu_list' => $menu_list, "luopan" => $luopan]);
    }

    public function purOrderDetail(Request $request)
    {
        $menu_list = MenuService::getMenu();
        $pur_id = $request->input("id", 0);
        if (!$pur_id) {
            die("参数错误");
        }
        $pruOrderInfo = PurchaseOrderModel::getPurOrderInfo($pur_id);
        if (!$pruOrderInfo) {
            die("没找到该条采购单信息");
        }

        // 计算美金转人民币汇率
        $usdToCnyRate = 0;
        if (!empty($pruOrderInfo['all_exchange_rate'])) {
            $allExchangeRate = json_decode($pruOrderInfo['all_exchange_rate'], true);
            if (is_array($allExchangeRate)) {
                // 直接获取美金兑人民币汇率（币种ID为2）
                $usdToCnyRate = $allExchangeRate[2] ?? 0;
            }
        }

        // 如果汇率计算失败，使用默认汇率
        if ($usdToCnyRate <= 0) {
            $usdToCnyRate = $pruOrderInfo['exchange_rate'] ?? 7.1;
        }

        $supplier_id = $pruOrderInfo["supplier_id"];
        $sup = \App\Http\Services\SupplierService::getSupplierBySupplierIds([$supplier_id]);
        if (empty($sup)) {
            die("没找到供应商信息");
        }
        $sup = current($sup);

        //发货通知单 关联单号
        $stockInSn = StockInModel::getAllStockInSnByPurId($pruOrderInfo["purchase_id"]);

        //付款申请单 关联单号
        $payment = PaymentModel::getAllStockInSnByPurId($pruOrderInfo["purchase_id"]);
        //终端公司信息
        $custInfo = PurOrderService::buildResponsePurOrderEndCustData($pruOrderInfo['purchase_id']);
        //合同列表
        $purContractListData = PurchaseContractModel::getContractListByPurchaseId($pruOrderInfo["purchase_id"]);
        $purContractList = PurContractService::buildResponsePurContractListData($purContractListData);

        // 检查合同是否可能失效
        $pruOrderInfo['contract_may_invalid'] = 0; // 默认合同有效
        $signedContracts = PurContractService::getSignedElectronicContractsByPurIds([$pruOrderInfo["purchase_id"]]);
        //        dd($pruOrderInfo["purchase_id"]);
        if (isset($signedContracts[$pruOrderInfo["purchase_id"]])) {
            $contract = $signedContracts[$pruOrderInfo["purchase_id"]];
            // 如果采购单的update_time大于合同的签约时间，则标记为可能失效
            //            dd($pruOrderInfo['model_change_time'] , $contract['sign_time']);
            if (!empty($contract['sign_time']) && $pruOrderInfo['model_change_time'] > $contract['sign_time']) {
                $pruOrderInfo['contract_may_invalid'] = 1;
            }
        }
        //采购单总金额
        $purOrderTotalAmount = PurchaseItemsModel::getTotalAmount($pur_id, $pruOrderInfo["currency"]);
        $purItemsIds = PurchaseItemsModel::getPurderItemsIds($pur_id);
        $paymentAmount["amount_paid"] = 0.00;
        $paymentAmount["pay_amount_ing"] = 0.00;
        $paymentAmount["no_amount_paid"] = 0.00;
        $paymentAmount["pay_amount_refund"] = 0.00;
        if (!empty($purItemsIds)) {
            $paymentAmount["amount_paid"] = PaymentItemsModel::getAmountPaidFromPur($purItemsIds);//已付金额
            $paymentAmount["amount_paid"] = decimal_number_format($paymentAmount["amount_paid"]);
            $paymentAmount["pay_amount_ing"] = PaymentItemsModel::getPayAmountIngFromPur($purItemsIds);//付款中金额
            $paymentAmount["pay_amount_ing"] = decimal_number_format($paymentAmount["pay_amount_ing"]);
            $paymentAmount["no_amount_paid"] = $purOrderTotalAmount - $paymentAmount["amount_paid"] - $paymentAmount["pay_amount_ing"];//未付金额
            $paymentAmount["no_amount_paid"] = decimal_number_format($paymentAmount["no_amount_paid"]);
            $paymentAmount["pay_amount_refund"] = PaymentItemsModel::getPayAmountRefundFromPur($purItemsIds);//付款退款金额
            $paymentAmount["pay_amount_refund"] = decimal_number_format($paymentAmount["pay_amount_refund"]);
        }

        //获取采购单税率


        //银行信息
        $supplierReceipts = SupplierService::getSupplierReceipts($pruOrderInfo["supplier_id"]);
        //供应商信息 包含了退货信息
        $supInfo = SupplierService::getSupplierBySupplierId($pruOrderInfo["supplier_id"]);
        //供应商联系人
        $supContact = SupplierService::getSupplierContactByPurchaseUid($pruOrderInfo["supplier_id"], $pruOrderInfo["purchase_uid"]);
        //供应商联系人查找信息
        $defaultSupContact = [];
        foreach ($supContact as $v) {
            if ($pruOrderInfo["supplier_contact_id"] == $v["contact_id"]) {
                $defaultSupContact = $v;
            }
        }


        $supInfo['is_qc'] = (int)SupplierService::checkSupplierIsQc($supInfo["supplier_group"], $supInfo["supplier_code"]);

        //型号明细tab页面 罗盘
        $itemsListLuoPan = PurchaseItemsModel::getPurOrderDetailItemListLuoPan($pur_id);
        //查询采购员的部门
        $cmsUserDepInfo = CmsUserInfoModel::getUserInfo($pruOrderInfo["purchase_uid"]);
        //员工上一级部门
        $parentDep = CmsUserDepartmentModel::getParentDepName($cmsUserDepInfo["department_id"]);

        $auditInfo = AuditPurOrderService::getAuditList([$pur_id])[$pur_id] ?? [];
        $isShowWeight = 0;
        if (in_array(strtolower($supInfo["supplier_code"]), GoodsService::NEED_WEIGHT_SUPPLIER_CODE)) {
            $isShowWeight = 1;
        }
        $pur_orderinfo_one_key_edit = PermService::hasPerm("pur_orderinfo_one_key_edit");

        return view('purDemand.purOrderDetail', [
            'menu_list'                  => $menu_list,
            "pruOrderInfo"               => $pruOrderInfo,
            "stockInSn"                  => $stockInSn,
            "payment"                    => $payment,
            "custInfo"                   => $custInfo,
            "sup"                        => $sup,
            "paymentAmount"              => $paymentAmount,
            "itemsListLuoPan"            => $itemsListLuoPan,
            "cmsUserDepInfo"             => $cmsUserDepInfo,
            "purContractList"            => $purContractList,
            "parentDep"                  => $parentDep,
            "auditInfo"                  => $auditInfo,
            "supInfo"                    => $supInfo,
            "supContact"                 => $supContact,
            "supplierReceipts"           => $supplierReceipts,
            "defaultSupContact"          => $defaultSupContact,
            "pur_orderinfo_one_key_edit" => $pur_orderinfo_one_key_edit,
            "is_bjg"                     => $pruOrderInfo['sign_company_id'] == SignCompanyService::BEIJIGUANG_ID ? 1 : 0,
            "placeOfOriginList"          => SupplierDeliveryService::getPlaceOfOriginList(),
            "is_show_weight"             => $isShowWeight,
            "usdToCnyRate"               => $usdToCnyRate,
            //            "placeOfOriginList"=>[]
        ]);
    }

    public function addSendOrder(Request $request)
    {
        $menu_list = MenuService::getMenu();
        $purchase_id = $request->input("id", 0);
        if (!$purchase_id) {
            return back()->withInput();
        }
        $supplierDeliveryService = new SupplierDeliveryService;
        $shippingList = $supplierDeliveryService->getShippingList();
        $info = PurchaseOrderModel::getPurOrderInfo($purchase_id);
        return view('purDemand.addSendOrder', ['menu_list' => $menu_list, "info" => $info, "shippingList" => $shippingList]);
    }

    public function stockPurchase(Request $request)
    {
        $menu_list = MenuService::getMenu();

        return view('purDemand.stockPurchase', ['menu_list' => $menu_list]);
    }

    public function stockPurchaseDetail(Request $request)
    {
        $menu_list = MenuService::getMenu();
        $stock_in_id = $request->input("stock_in_id", 0);
        if (empty($stock_in_id)) {
            die("参数错误");
        }
        $supplierDeliveryService = new SupplierDeliveryService;
        $info = $supplierDeliveryService->getStockInById(intval($stock_in_id));
        $shippingList = $supplierDeliveryService->getShippingList();
        //        (new SupplierDeliveryService)->syncInQtyToPurOrderItem([581,582]);
        $itemsList = StockInItemModel::getAllById($stock_in_id);
        $is_qc = 0;
        foreach ($itemsList as $item) {
            if ($item["is_qc"] == 1) {
                $is_qc = 1;
                break;
            }
        }
        return view('purDemand.stockPurchaseDetail', ['menu_list' => $menu_list, "info" => $info, "shippingList" => $shippingList, "is_qc" => $is_qc]);
    }

    /*
     *生成采购单并发货
     */
    public function importPurOrderAndStockIn(Request $request)
    {
        $menu_list = MenuService::getMenu();
        return view('purDemand.importPurOrderAndStockIn', ['menu_list' => $menu_list]);
    }

    /*
     *生成采购单并发货
     */
    public function importStockInSyncKingDee(Request $request)
    {
        $menu_list = MenuService::getMenu();
        return view('purDemand.importStockInSyncKingDee', ['menu_list' => $menu_list]);
    }


    public function purOrderItemList(Request $request)
    {
        $menu_list = MenuService::getMenu();
        return view('purDemand.purOrderItemList', ['menu_list' => $menu_list]);
    }

    public function purOrderItemListForPm(Request $request)
    {
        $menu_list = MenuService::getMenu();
        return view('purDemand.purOrderItemListForPm', ['menu_list' => $menu_list]);
    }

    public function spestockApproceList(Request $request)
    {
        $menu_list = MenuService::getMenu();
        return view('purDemand.spestockApproceList', ['menu_list' => $menu_list]);
    }

    public function purSaleorderUnbind(Request $request)
    {
        $menu_list = MenuService::getMenu();
        return view('purDemand.purSaleorderUnbind', ['menu_list' => $menu_list]);
    }

    public function batchImportCoos(Request $request)
    {
        $menu_list = MenuService::getMenu();
        return view('purDemand.batchImportCoos', ['menu_list' => $menu_list]);
    }

    public function precheckItems(Request $request)
    {
        $menu_list = MenuService::getMenu();

        $isCanShowEncryptData = checkPerm(StockInPrecheckModel::IS_CAN_SHOW_ENCRYPT_DATA);
        $isCanShowEncryptData = $isCanShowEncryptData ? 1 : 0;


        return view('purDemand.precheckItems', ['menu_list' => $menu_list, "isCanShowEncryptData" => $isCanShowEncryptData]);
    }

    public function consignmentApplyOrder(Request $request)
    {
        $menu_list = MenuService::getMenu();
        return view('consignment.consignmentApplyOrder', ['menu_list' => $menu_list]);
    }

    public function consignmentApplyOrderDetail(Request $request)
    {
        $menu_list = MenuService::getMenu();
        $id = $request->input("id", 0);
        if (empty($id)) {
            return redirect("/web/purDemand/consignmentApplyOrder");
        }
        $consignmentOrder = ConsignmentOrderModel::getOneById($id);
        if (empty($consignmentOrder)) {
            return redirect("/web/purDemand/consignmentApplyOrder");
        }
        //        $consignmentOrder[]

        $consignmentOrder["consignment_status_format"] = Arr::get(ConsignmentOrderModel::$STATUS, $consignmentOrder["consignment_status"], "");
        $consignmentOrder["shipping_status_format"] = Arr::get(ConsignmentOrderModel::$SHIPPING_STATUS, $consignmentOrder["shipping_status"], "");
        $consignmentOrder["stock_in_status_format"] = Arr::get(ConsignmentOrderModel::$STOCK_IN_STATUS, $consignmentOrder["stock_in_status"], "");

        $consignmentStockIn = ConsignmentStockInModel::getListByConsignmentId($id);
        $stockInIdArr = Arr::pluck($consignmentStockIn, "stock_in_id");
        $stockInIdArr = array_filter_unique($stockInIdArr);
        $StockInList = StockInModel::getStockInList($stockInIdArr);
        foreach ($StockInList as $k => $item) {
            $StockInList[$k]["status_format"] = arrayGet(StockInModel::$STATUS, $item["status"]);
        }
        $supInfo = SupplierService::getSupplierBySupplierId($consignmentOrder["supplier_id"]);
        //供应商性质：1代理商,2现货商,3方案商,4原厂,5分销平台,6代工厂,7混合分销,0是其它
        if (!empty($supInfo)) {
            $consignmentOrder["supplier_group"] = $supInfo["supplier_group"];
            $consignmentOrder["supplier_group_name"] = $supInfo["supplier_group_name"];
        }
        //        dd($StockInList);

        return view('consignment.consignmentApplyOrderDetail', ['menu_list' => $menu_list, "consignmentOrder" => $consignmentOrder, "stockInList" => $StockInList]);
    }

    public function consignmentRecall(Request $request)
    {
        $menu_list = MenuService::getMenu();
        $data['menu_list'] = MenuService::getMenu();
        $data['status'] = config('field.ReturnMaterialStatus');
        return view('consignment.consignmentRecall', $data);
    }

    public function consignmentRecallDetail(Request $request)
    {
        // $data['menu_list'] = MenuService::getMenu();
        // $rmaId = $request->get('id');
        // if (!$rmaId) {
        //     return 'rma_id不能为空';
        // }
        // $data['return_material'] = ReturnMaterialService::getReturnMaterial($rmaId);
        // $data['rma_reason'] = config('field.ReturnMaterialReason');
        // return view('consignment.consignmentRecallDetail',$data);

        $menu_list = MenuService::getMenu();
        return view('consignment.consignmentRecallDetail', ['menu_list' => $menu_list]);
    }

    //导出未完成的采购单
    public function exportUnfinishedPurOrder(Request $request)
    {
        $departmentId = $request->input("department_id");
        $purchaseUid = $request->input("purchase_uid");
        return Excel::download(new UnFinishedPurOrderExport($departmentId, $purchaseUid), "未完成采购明细列表(部门ID{$departmentId},采购员id{$purchaseUid}).xlsx");
    }

    /**
     * 通过合同ID查看PDF合同
     * @param Request $request
     * @return mixed
     */
    public function contractPdf(Request $request)
    {
        $contractId = (int)$request->input('contract_id', '0');
        $isFromXinlian = (int)$request->input('is_from_xinlian', 0);
        if ($isFromXinlian) {
            //芯链
            request()->offsetSet("user", (object)["userId" => "1000", "email" => "<EMAIL>", "name" => "admin"]);
        }
        if (empty($contractId)) {
            return $this->setError("合同ID不存在");
        }
        // 获取合同信息
        $contractInfo = PurchaseContractModel::getContractInfoByContractId($contractId);
        if (empty($contractInfo)) {
            return $this->setError("合同信息不存在");
        }

        $purchaseId = $contractInfo['purchase_id'] ?? 0;
        if (empty($purchaseId)) {
            return $this->setError("采购单ID不存在");
        }

        $isExistsOldContract = 0;
        $lastContractInfo = [];
        if ($isFromXinlian) {
            //芯链 判断存在已签署+有效+电子签的合同
            $lastContractInfo = PurchaseContractModel::getLastContractInfoByPurchaseId($purchaseId);
            if (!empty($lastContractInfo)) {
                $isExistsOldContract = 1;
            }
        }

        // 获取采购单PDF导出数据
        $purPdfInfo = PurOrderService::getPurDemandPdfData($purchaseId);
        $purPdfResponseInfo = PurOrderService::buildPdfResponseData($purPdfInfo);

        // 用合同参数快照覆盖PDF数据（如型号等字段）
        if (!empty($contractInfo['content_snapshot'])) {
            $contentSnapshot = json_decode($contractInfo['content_snapshot'], true);
            if (!empty($contentSnapshot['item_list'])) {
                $purPdfResponseInfo['itemList'] = $contentSnapshot['item_list'];
            }
            //如果是已签署合同，添加签署人信息
            if ($contractInfo['sign_status'] == PurchaseContractModel::SIGN_STATUS_SIGNED && !empty($contentSnapshot['signer_info'])) {
                $purPdfResponseInfo['contractSignerInfo'] = $contentSnapshot['signer_info'];
            }
        }
        $purPdfResponseInfo['contractInfo'] = $contractInfo;

        if ($isFromXinlian) {
            //云心系统获取合同接口
            return $this->setSuccessData(['pdfInfo' => $purPdfResponseInfo, "isExistsOldContract" => $isExistsOldContract, "oldContractSn" => $lastContractInfo["contract_sn"] ?? ""]);
        }
        //                    dd($purPdfResponseInfo);
        // 渲染模板生成PDF
        if ($purPdfResponseInfo['orderInfo']['companyId'] == PurCommonEnum::LiexXinCompanyId) {
            $html = view('contract_export.contract', ['pdfInfo' => $purPdfResponseInfo])->render();
        } else {
            // 渲染模板生成PDF
            $html = view('contract_export.contractHK', ['pdfInfo' => $purPdfResponseInfo])->render();
        }

        if ($request->input('debug') == 1) {
            return $html;
        }

        $pdf = App::make('dompdf.wrapper');
        $name = 'contract_' . $contractId . '_' . time() . '.pdf';
        $pdf->loadHTML($html)->setPaper('a4', 'portrait')->setOptions(['enable_font_subsetting' => true])->setWarnings(false);

        return $pdf->stream($name);
    }


}
