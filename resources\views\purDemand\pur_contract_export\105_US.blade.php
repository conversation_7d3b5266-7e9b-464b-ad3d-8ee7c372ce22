<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>Purchase Order Contract</title>
    <style type="text/css" media="screen">
        * {
            font-family: "simsun";
            font-weight: normal;
            font-size: 12px;
            margin: 0px;
            padding: 0px;
        }

        b {
            font-family: "simsunb";
        }

        .tc {
            text-align: center;
        }

        .tl {
            text-align: left;
        }

        .pdfx {
            width: 750px;
            margin: 0 auto;
            background: #fff;
            padding: 10px;
        }

        table {
            border-collapse: collapse;
            width: 750px;
        }

        .custom-table td {

            padding: 3px;
            text-align: center;
        }

    </style>
</head>

<body>
<div class="pdfx">
    <table>
        <tr>
            <td colspan="8" style="text-align: center;">
                <h2 class="tc"><b style="font-size: 30px;">{{$pdfInfo['sign_company_info']['com_name_en']??""}}</b></h2>
            </td>
        </tr>
        <tr>
            <td colspan="8" style="text-align: center;">
                <h2 class="tc">{{$pdfInfo['sign_company_info']['com_addr_en']??""}}</h2>
            </td>
        </tr>
        <tr>
            <td colspan="8" style="text-align: center;">
                <h2 class="tc">{{$pdfInfo['sign_company_info']['contact_phone']??""}}</h2>
            </td>
        </tr>
        <tr>
            <td colspan="8" style="text-align: center;">
                <h2 class="tc" style="margin-bottom: 15px;"><b style="font-size: 25px;">PURCHASE ORDER</b></h2>
            </td>
        </tr>
        <tr>
            <td colspan="8">
                <p style="text-align: right;margin-right: 200px;margin-bottom: 15px;"><b>Date：{{ $pdfInfo['orderInfo']['date'] }}</b></p>
            </td>
        </tr>
    </table>
    <table style="margin-bottom: 20px;width: 100%">
        <tr>
            <td colspan="4" style="width: 375px"><b>Vendor</b></td>
            <td colspan="4" style="width: 375px"><b>Ship/Bill To:</b></td>
        </tr>
        <tr>
            <td colspan="4">Company name：{{$pdfInfo['supInfo']['supplier_name']??""}}</td>
            <td colspan="4">Company name：{{$pdfInfo['sign_company_info']['com_name_en']??""}}</td>
        </tr>
        <tr>
            <td colspan="4">Address：{{$pdfInfo['supInfo']['supplier_address']??""}}</td>
            <td colspan="4">Address：{{$pdfInfo['sign_company_info']['com_addr_en']??""}}</td>
        </tr>
        <tr>
            <td colspan="4">ATTN：{{ $pdfInfo['orderInfo']['partyBContactName'] }}</td>
            <td colspan="4">ATTN: {{$pdfInfo['sign_company_info']['contact_en']??""}}</td>
        </tr>
        <tr>
            <td colspan="4">EMAIL: {{ $pdfInfo['orderInfo']['partyBEmail'] }}</td>
            <td colspan="4"></td>
        </tr>
        <tr>
            <td colspan="4">TEL: {{ $pdfInfo['orderInfo']['partyBTel'] }}</td>
            <td colspan="4">TEL: {{$pdfInfo['sign_company_info']['contact_phone']??""}}</td>
        </tr>
    </table>
    <table class="custom-table" border="1">
        <tr>
            <td>Booking No.</td>
            <td>Part No.</td>
            <td>MFG</td>
            <td>QTY</td>
            <td>D/C</td>
            <td>Unit Price</td>
            <td>Extend Price</td>
            <td>Remark</td>
        </tr>
        @foreach ($pdfInfo['itemList'] as $itemInfo)
            <tr>
                <td>{{ $itemInfo['warehouseReceiptSn']??"" }}</td>
                <td>{{ $itemInfo['goodsName']??"" }}</td>
                <td>{{ $itemInfo['brandName']??"" }}</td>
                <td>{{ $itemInfo['purchaseQty']??"" }}</td>
                <td>{{ $itemInfo['dateCode']??"" }}</td>
                <td>{{ $itemInfo['unitPrice']??"" }}</td>
                <td>{{ $itemInfo['price']??"" }}</td>
                <td>{{ $itemInfo['order_remark']??"" }}</td>
            </tr>
        @endforeach
        <tr>
            <td colspan="6">Total({{$pdfInfo['orderInfo']['currency_code']}}):</td>
            <td>{{ $pdfInfo['itemPriceTotal'] }}</td>
            <td></td>
        </tr>
    </table>
    <table>
        <tr>
            <td colspan="8">
                <p style="margin-top: 20px;"><b>Terms and Conditions:</b></p>
            </td>
        </tr>
        <tr>
            <td colspan="8">
                <p>
                    1.Payment terms: TT in advance<br/>
                    2.Terms of Delivery: FOB<br/>
                    3. Courier Account：UPS ACCOUNT# J45A82<br/>
                    4. All parts must be NEW, UNUSED and in Original Manufacturer Packaging.<br/>
                    5. Lables/packaging must be from original manufacturer in original condition<br/>
                    without any cross outs, remarkings or strikethroughs.<br/>
                    6. We reserve the right to reject your shipment if the delivery date doesn't match
                    the above mentioned.<br/>
                    7.The warranty period shall be twelve (12) months commencing upon the customer's<br/>
                    receipt of the goods.Vendor shall be obligated to process returns and effect full reimbursement
                    within five (5) business days upon verification of quality non-conformities<br/>
                    <span style="color: red">8.Pls mark the Booking No. on the packing list and outbox.</span><br/>
                </p>
            </td>
        </tr>
    </table>

    <table style="margin-top: 30px;">
        <tr>
            <td colspan="4" style="width: 375px">Vendor：<br/>{{$pdfInfo['supInfo']['supplier_name']??""}}</td>
            <td colspan="4" style="width: 375px">Buyer：<br/>{{$pdfInfo['sign_company_info']['com_name_en']??""}}</td>
        </tr>
        <tr>
            <td colspan="4" style="width: 375px"><p style="margin-top: 20px;">________________________</p></td>
            <td colspan="4" style="width: 375px"><p style="margin-top: 20px;">________________________</p></td>
        </tr>
        <tr>
            <td colspan="4" style="width: 375px">Authorized Signature</td>
            <td colspan="4" style="width: 375px">
                Authorized Signature
                @if ($pdfInfo['orderInfo']['status'] >= 2)
                    @if(isset($pdfInfo['sign_company_info']['seal_image']) && !empty($pdfInfo['sign_company_info']['seal_image'])&&empty($pdfInfo['is_excel']))
                        <div style="position: relative;">
                            <img src="{{$pdfInfo['sign_company_info']['seal_image']}}" style="position: absolute; top: -130px; right: 200px; width: 100px; height: 100px; z-index: -1;">
                        </div>
                    @endif
                @endif
            </td>
        </tr>
    </table>
</div>
</body>

</html>
