<?php

namespace App\Exports;

use App\Enum\PurCommonEnum;
use PhpOffice\PhpSpreadsheet\IOFactory;

class PurContractExport
{
    public static function export($data, $type)
    {
        if ($type == 'cn') {
            self::exportCn($data);
        } elseif ($type == 'hk') {
            self::exportHk($data);
        } elseif ($type == 'en') {
            self::exportEn($data);
        }
    }

    public static function exportCn($data)
    {
        // 只有订单状态 >= 2 才添加公章
        $orderStatus = $data['orderInfo']['status'] ?? 0;
        $templateSuffix = $orderStatus >= 2 ? '' : '_no_seal';
        $templateFile = base_path() . "/public/excel/pur_contract_export/cn{$templateSuffix}.xlsx";
        $reader = IOFactory::createReader("Xlsx");
        $spreadsheet = $reader->load($templateFile);
        $objActSheet = $spreadsheet->getActiveSheet();
        $pdfInfo = $data;
        $cellRelationData = [
            "B2" => $pdfInfo['orderInfo']['partyACompanyName'],
            "F4" => $pdfInfo['orderInfo']['purchaseNum'],
            "K4" => $pdfInfo['orderInfo']['date'],
            "F5" => $pdfInfo['orderInfo']['partyACompanyName'],
            "F6" => $pdfInfo['orderInfo']['partyAContactName'],
            "F8" => " {$pdfInfo['orderInfo']['partyATel']}",
            "F9" => " {$pdfInfo['orderInfo']['partyAFax']}",
            "K5" => $pdfInfo['orderInfo']['partyBCompanyName'],
            "K6" => $pdfInfo['orderInfo']['partyBContactName'],
            "K7" => $pdfInfo['orderInfo']['partyBAddress'],
            "K8" => " {$pdfInfo['orderInfo']['partyBTel']}",
//            "K11" => $pdfInfo['currencyType'],
        ];
        $cellNum = 12;
        $itemCount = count($pdfInfo['itemList']);
        if ($itemCount > 1) {
            $objActSheet->insertNewRowBefore(12, $itemCount - 1);
        }

        foreach ($pdfInfo['itemList'] as $itemInfo) {
            $objActSheet->mergeCells("C{$cellNum}:F{$cellNum}");
            $cellRelationData["B{$cellNum}"] = $itemInfo['index'];
            $cellRelationData["C{$cellNum}"] = $itemInfo['goodsName'];
            $cellRelationData["G{$cellNum}"] = $itemInfo['brandName'];
            $cellRelationData["H{$cellNum}"] = $itemInfo['purchaseQty'];
            $cellRelationData["I{$cellNum}"] = $itemInfo['dateCode'];
            $cellRelationData["J{$cellNum}"] = $itemInfo['unitPrice'];
            $cellRelationData["K{$cellNum}"] = $itemInfo['price'];
            $cellRelationData["L{$cellNum}"] = $itemInfo['estimatDeliveryTime'];
            $cellRelationData["M{$cellNum}"] = $itemInfo['warehouseReceiptSn'];
            $cellRelationData["N{$cellNum}"] = $itemInfo['frqRemark'];
            $cellNum++;
        }
        $cellRelationData["M" . (13 + $itemCount - 1)] = "合计：" . $pdfInfo['itemPriceTotal'];
        $cellRelationData["F" . (16 + $itemCount - 1)] = "{$pdfInfo['orderInfo']['payName']}";
        $cellRelationData["B" . (18 + $itemCount - 1)] = "四、以上价格为含税{$pdfInfo['currencyType']}价格，提供{$pdfInfo['orderInfo']['tax']}%增值税专用发票。";

        $cellRelationData["F" . (35 + $itemCount - 1)] = $pdfInfo['orderInfo']['partyACompanyName'];
        $cellRelationData["K" . (35 + $itemCount - 1)] = $pdfInfo['orderInfo']['partyBCompanyName'];
        $cellRelationData["K" . (36 + $itemCount - 1)] = $pdfInfo['orderInfo']['partyBContactName'];
        $cellRelationData["F" . (36 + $itemCount - 1)] = $pdfInfo['orderInfo']['partyAContactName'];

        foreach ($cellRelationData as $key => $cellData) {
            $objActSheet->setCellValue($key, (string)$cellData);
        }
        $outputFileName = "{$data['orderInfo']['purchaseNum']}.xlsx";
        //保存文件
        header("Content-Type: application/force-download");
        header("Content-Type: application/octet-stream");
        header("Content-Type: application/download");
        header('Content-Disposition:inline;filename="' . $outputFileName . '"');
        header("Content-Transfer-Encoding: binary");
        header("Expires: Mon, 26 Jul 1997 05:00:00 GMT");
        header("Last-Modified: " . gmdate("D, d M Y H:i:s") . " GMT");
        header("Cache-Control: must-revalidate, post-check=0, pre-check=0");
        header("Pragma: no-cache");
        $writer = IOFactory::createWriter($spreadsheet, 'Xlsx');
        $writer->save("php://output");
    }

    public static function exportHuaYun($data)
    {
        // 只有订单状态 >= 2 才添加公章
        $orderStatus = $data['orderInfo']['status'] ?? 0;
        $templateSuffix = $orderStatus >= 2 ? '' : '_no_seal';
        $templateFile = base_path() . "/public/excel/pur_contract_export/huayun{$templateSuffix}.xlsx";
        $reader = IOFactory::createReader("Xlsx");
        $spreadsheet = $reader->load($templateFile);
        $objActSheet = $spreadsheet->getActiveSheet();
        $pdfInfo = $data;
        $cellRelationData = [
            "B2" => $pdfInfo['orderInfo']['partyACompanyName'],
            "F4" => $pdfInfo['orderInfo']['purchaseNum'],
            "K4" => $pdfInfo['orderInfo']['date'],
            "F5" => $pdfInfo['orderInfo']['partyACompanyName'],
            "F6" => $pdfInfo['orderInfo']['partyAContactName'],
            "F8" => " {$pdfInfo['orderInfo']['partyATel']}",
            "F9" => " {$pdfInfo['orderInfo']['partyAFax']}",
            "K5" => $pdfInfo['orderInfo']['partyBCompanyName'],
            "K6" => $pdfInfo['orderInfo']['partyBContactName'],
            "K7" => $pdfInfo['orderInfo']['partyBAddress'],
            "K8" => " {$pdfInfo['orderInfo']['partyBTel']}",
//            "K11" => $pdfInfo['currencyType'],
        ];
        $cellNum = 12;
        $itemCount = count($pdfInfo['itemList']);
        if ($itemCount > 1) {
            $objActSheet->insertNewRowBefore(12, $itemCount - 1);
        }

        foreach ($pdfInfo['itemList'] as $itemInfo) {
            $objActSheet->mergeCells("C{$cellNum}:F{$cellNum}");
            $cellRelationData["B{$cellNum}"] = $itemInfo['index'];
            $cellRelationData["C{$cellNum}"] = $itemInfo['goodsName'];
            $cellRelationData["G{$cellNum}"] = $itemInfo['brandName'];
            $cellRelationData["H{$cellNum}"] = $itemInfo['purchaseQty'];
            $cellRelationData["I{$cellNum}"] = $itemInfo['dateCode'];
            $cellRelationData["J{$cellNum}"] = $itemInfo['unitPrice'];
            $cellRelationData["K{$cellNum}"] = $itemInfo['price'];
            $cellRelationData["L{$cellNum}"] = $itemInfo['estimatDeliveryTime'];
            $cellRelationData["M{$cellNum}"] = $itemInfo['warehouseReceiptSn'];
            $cellRelationData["N{$cellNum}"] = $itemInfo['frqRemark'];
            $cellNum++;
        }
        $cellRelationData["M" . (13 + $itemCount - 1)] = "合计：" . $pdfInfo['itemPriceTotal'];
        $cellRelationData["F" . (16 + $itemCount - 1)] = "{$pdfInfo['orderInfo']['payName']}";
        $cellRelationData["B" . (18 + $itemCount - 1)] = "四、以上价格为含税{$pdfInfo['currencyType']}价格，提供{$pdfInfo['orderInfo']['tax']}%增值税专用发票。";

        $cellRelationData["F" . (35 + $itemCount - 1)] = $pdfInfo['orderInfo']['partyACompanyName'];
        $cellRelationData["K" . (35 + $itemCount - 1)] = $pdfInfo['orderInfo']['partyBCompanyName'];
        $cellRelationData["K" . (36 + $itemCount - 1)] = $pdfInfo['orderInfo']['partyBContactName'];
        $cellRelationData["F" . (36 + $itemCount - 1)] = $pdfInfo['orderInfo']['partyAContactName'];

        foreach ($cellRelationData as $key => $cellData) {
            $objActSheet->setCellValue($key, (string)$cellData);
        }
        $outputFileName = "{$data['orderInfo']['purchaseNum']}.xlsx";
        //保存文件
        header("Content-Type: application/force-download");
        header("Content-Type: application/octet-stream");
        header("Content-Type: application/download");
        header('Content-Disposition:inline;filename="' . $outputFileName . '"');
        header("Content-Transfer-Encoding: binary");
        header("Expires: Mon, 26 Jul 1997 05:00:00 GMT");
        header("Last-Modified: " . gmdate("D, d M Y H:i:s") . " GMT");
        header("Cache-Control: must-revalidate, post-check=0, pre-check=0");
        header("Pragma: no-cache");
        $writer = IOFactory::createWriter($spreadsheet, 'Xlsx');
        $writer->save("php://output");
    }

    public static function exportExcelYueFeng($data)
    {
        self::exportHuaYun($data);
    }

    public static function exportExcelGPShuZi($data)
    {
        self::exportHuaYun($data);
    }

    public static function exportEn($data)
    {
        // 只有订单状态 >= 2 才添加公章
        $orderStatus = $data['orderInfo']['status'] ?? 0;
        $templateSuffix = $orderStatus >= 2 ? '' : '_no_seal';
        $templateFile = base_path() . "/public/excel/pur_contract_export/en{$templateSuffix}.xlsx";
        $reader = IOFactory::createReader("Xlsx");
        $spreadsheet = $reader->load($templateFile);
        $objActSheet = $spreadsheet->getActiveSheet();
        $pdfInfo = $data;
        $cellRelationData = [
            "D5"  => $pdfInfo['orderInfo']['date'],
            "K5"  => $pdfInfo['orderInfo']['purchaseNum'],
            "K8"  => $pdfInfo['orderInfo']['payNameEn'],
            "D8"  => $pdfInfo['purchaseUserInfo']['name'],
            "D9"  => $pdfInfo['orderInfo']['partyBCompanyName'],
            "D10" => $pdfInfo['orderInfo']['partyBAddress'],
            "D12" => $pdfInfo['orderInfo']['partyBTel'],
            "D13" => $pdfInfo['orderInfo']['partyBFax'],
            "D14" => $pdfInfo['orderInfo']['partyBContactName'],
        ];
        $itemCount = count($pdfInfo['itemList']);
        $cellNum = 16;
        if ($itemCount > 1) {
            $objActSheet->insertNewRowBefore(16, $itemCount - 1);
        }
        foreach ($pdfInfo['itemList'] as $itemInfo) {
            $objActSheet->mergeCells("B{$cellNum}:D{$cellNum}");
            $objActSheet->mergeCells("E{$cellNum}:F{$cellNum}");
            $objActSheet->mergeCells("G{$cellNum}:H{$cellNum}");
            $objActSheet->mergeCells("K{$cellNum}:L{$cellNum}");
            $objActSheet->mergeCells("M{$cellNum}:N{$cellNum}");
            $objActSheet->mergeCells("O{$cellNum}:P{$cellNum}");
            $cellRelationData["B{$cellNum}"] = $itemInfo['goodsName'];
            $cellRelationData["E{$cellNum}"] = $itemInfo['brandName'];
            $cellRelationData["G{$cellNum}"] = $itemInfo['dateCode'];
            $cellRelationData["I{$cellNum}"] = $itemInfo['warehouseReceiptSn'];
            $cellRelationData["J{$cellNum}"] = $itemInfo['estimatDeliveryTime'];
            $cellRelationData["K{$cellNum}"] = $itemInfo['purchaseQty'];
            $cellRelationData["M{$cellNum}"] = $itemInfo['unitPrice'];
            $cellRelationData["O{$cellNum}"] = $itemInfo['price'];
            $cellNum++;
        }
        $cellRelationData["O" . (17 + $itemCount - 1)] = $pdfInfo['itemPriceTotal'];
        $cellRelationData["B" . (36 + $itemCount - 1)] = $pdfInfo['orderInfo']['partyBCompanyName'];

        foreach ($cellRelationData as $key => $cellData) {
            $objActSheet->setCellValue($key, (string)$cellData);
        }
        //在原有模板的基础上创建新的模板
        $objWriter = IOFactory::createWriter($spreadsheet, 'Xlsx');
        $outputFileName = "{$data['orderInfo']['purchaseNum']}.xlsx";
        //保存文件
        header("Content-Type: application/force-download");
        header("Content-Type: application/octet-stream");
        header("Content-Type: application/download");
        header('Content-Disposition:inline;filename="' . $outputFileName . '"');
        header("Content-Transfer-Encoding: binary");
        header("Expires: Mon, 26 Jul 1997 05:00:00 GMT");
        header("Last-Modified: " . gmdate("D, d M Y H:i:s") . " GMT");
        header("Cache-Control: must-revalidate, post-check=0, pre-check=0");
        header("Pragma: no-cache");
        $objWriter->save("php://output");
    }

    public static function exportHk($data)
    {
        // 只有订单状态 >= 2 才添加公章
        $orderStatus = $data['orderInfo']['status'] ?? 0;
        $templateSuffix = $orderStatus >= 2 ? '' : '_no_seal';
        $templateFile = base_path() . "/public/excel/pur_contract_export/hk{$templateSuffix}.xlsx";
        $reader = IOFactory::createReader("Xlsx");
        $spreadsheet = $reader->load($templateFile);
        $objActSheet = $spreadsheet->getActiveSheet();
        $pdfInfo = $data;
        $cellRelationData = [
            "F4" => $pdfInfo['orderInfo']['purchaseNum'],
            "K4" => $pdfInfo['orderInfo']['date'],
            "F5" => $pdfInfo['orderInfo']['partyACompanyName'],
            "F6" => $pdfInfo['orderInfo']['partyAContactName'],
            "F8" => " {$pdfInfo['orderInfo']['partyATel']}",
//            "F9" => " {$pdfInfo['orderInfo']['partyAFax']}",
            "K5" => $pdfInfo['orderInfo']['partyBCompanyName'],
            "K6" => $pdfInfo['orderInfo']['partyBContactName'],
            "K7" => $pdfInfo['orderInfo']['partyBAddress'],
            "K8" => " {$pdfInfo['orderInfo']['partyBTel']}",
            "K9" => " {$pdfInfo['currencyType']}",
        ];

        $cellNum = 11;
        $itemCount = count($pdfInfo['itemList']);
        if ($itemCount > 1) {
            $objActSheet->insertNewRowBefore(11, $itemCount - 1);
        }

        foreach ($pdfInfo['itemList'] as $itemInfo) {
            $objActSheet->mergeCells("C{$cellNum}:F{$cellNum}");
            $cellRelationData["B{$cellNum}"] = $itemInfo['index'];
            $cellRelationData["C{$cellNum}"] = $itemInfo['goodsName'];
            $cellRelationData["G{$cellNum}"] = $itemInfo['brandName'];
            $cellRelationData["H{$cellNum}"] = $itemInfo['purchaseQty'];
            $cellRelationData["I{$cellNum}"] = $itemInfo['dateCode'];
            $cellRelationData["J{$cellNum}"] = $itemInfo['unitPrice'];
            $cellRelationData["K{$cellNum}"] = $itemInfo['price'];
            $cellRelationData["L{$cellNum}"] = $itemInfo['estimatDeliveryTime'];
            $cellRelationData["M{$cellNum}"] = $itemInfo['warehouseReceiptSn'];
            $cellRelationData["N{$cellNum}"] = $itemInfo['frqRemark'];
            $cellNum++;
        }
        $cellRelationData["B" . (12 + $itemCount - 1)] = " 合计({$pdfInfo['currencyType']})";
        $cellRelationData["F" . (12 + $itemCount - 1)] = $pdfInfo['itemPriceTotal'];
        $cellRelationData["F" . (16 + $itemCount - 1)] = "{$pdfInfo['orderInfo']['payName']}";
        $cellRelationData["F" . (35 + $itemCount - 1)] = $pdfInfo['orderInfo']['partyAContactName'];

        $cellRelationData["K" . (34 + $itemCount - 1)] = $pdfInfo['orderInfo']['partyBCompanyName'];
        $cellRelationData["K" . (35 + $itemCount - 1)] = $pdfInfo['orderInfo']['partyBContactName'];
        if ($pdfInfo['sign_company_info']['sign_com_id']!=PurCommonEnum::CurrencyUSD){
            $cellRelationData['B2'] = $pdfInfo['sign_company_info']['com_name_hk'];
            $cellRelationData['F5'] = $pdfInfo['sign_company_info']['com_name'];
            $cellRelationData['F7'] = $pdfInfo['sign_company_info']['com_addr'];
            $cellRelationData['F8'] = $pdfInfo['sign_company_info']['contact_phone'];
            $cellRelationData['B'.(14 + $itemCount - 1)] = "一、交货地点：".$pdfInfo['sign_company_info']['com_addr'];
            $cellRelationData['B'.(15 + $itemCount - 1)] = "       发票邮寄地址：".$pdfInfo['sign_company_info']['com_addr'];
            $cellRelationData["F" . (34 + $itemCount - 1)] = $pdfInfo['sign_company_info']['com_name'];
        }
        foreach ($cellRelationData as $key => $cellData) {
            $objActSheet->setCellValue($key, (string)$cellData);
        }
        $outputFileName = "{$data['orderInfo']['purchaseNum']}.xlsx";
        //保存文件
        header("Content-Type: application/force-download");
        header("Content-Type: application/octet-stream");
        header("Content-Type: application/download");
        header('Content-Disposition:inline;filename="' . $outputFileName . '"');
        header("Content-Transfer-Encoding: binary");
        header("Expires: Mon, 26 Jul 1997 05:00:00 GMT");
        header("Last-Modified: " . gmdate("D, d M Y H:i:s") . " GMT");
        header("Cache-Control: must-revalidate, post-check=0, pre-check=0");
        header("Pragma: no-cache");
        $writer = IOFactory::createWriter($spreadsheet, 'Xlsx');
        $writer->save("php://output");
    }


}
