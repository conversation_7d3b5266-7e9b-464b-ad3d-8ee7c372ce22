<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Style;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Worksheet\Drawing;

class PurContractUsExcelExport  implements FromView, WithStyles, WithColumnWidths, WithEvents
{
    private $pdfInfo;
    private $lang = 'US';

    public function __construct($pdfInfo,$lang='US')
    {
        $this->pdfInfo = $pdfInfo;
        $this->lang = $lang;
    }

    public function view(): View
    {
        $purPdfInfo = $this->pdfInfo;
        $signViewPath = resource_path("views/purDemand/pur_contract_export/{$purPdfInfo['sign_company_info']['sign_com_id']}_{$this->lang}.blade.php");
        $view = "purDemand.pur_contract_export.{$purPdfInfo['sign_company_info']['sign_com_id']}_{$this->lang}";
        if (!file_exists($signViewPath)) {
            throw new \Exception("{$purPdfInfo['sign_company_info']['com_name']} 签约公司模板不存在");
        }
        return view($view, [
            "pdfInfo"=>$purPdfInfo
        ]);
    }

    public function columnWidths(): array
    {
        return [
            'A' => 10,
            'B' => 10,
            'C' => 10,
            'D' => 10,
            'E' => 10,
            'F' => 10,
            'G' => 10,
            'H' => 10,
        ];
    }

    public function defaultStyles(Style $defaultStyle)
    {
        return $defaultStyle->getFont()->setSize(8);
    }

    public function styles(Worksheet $sheet)
    {
        // 获取当前工作表中有数据的最高行
        $highestRow = $sheet->getHighestRow();

        // 动态生成要应用样式的范围
        $range = "A1:H{$highestRow}";

        return [
            $range => [
                'font' => ['size' => 10],
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                        'color' => ['argb' => 'FF000000'], // 黑色边框
                    ],
                ],
            ],
        ];
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                // 明细垂直居中
                $items_last_line = count($this->pdfInfo['itemList']) + 14; // 最后行 = 明细数量 + 明细前固定行数
                $event->sheet->getDelegate()->getStyle('A9:H' . $items_last_line)->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);

                // 添加公章图片
                $this->addCompanySeal($event, $items_last_line);
            }
        ];
    }

    /**
     * 添加公司公章图片
     */
    private function addCompanySeal(AfterSheet $event, int $items_last_line): void
    {
        // 只有订单状态 >= 2 才添加公章
        $orderStatus = $this->pdfInfo['orderInfo']['status'] ?? 0;
        if ($orderStatus < 2) {
            return;
        }

        // 检查是否有公章图片路径
        $sealImagePath = $this->pdfInfo['sign_company_info']['seal_image'] ?? '';

        if (empty($sealImagePath) || !file_exists($sealImagePath)) {
            return;
        }

        // 计算公章位置 - 参考ContractExport的逻辑
        $stamp_last_line = $items_last_line + 5; // 印章图片位置

        // 创建图片对象
        $drawing = new Drawing();
        $drawing->setName('Company Seal');
        $drawing->setDescription('Company Seal');
        $drawing->setCoordinates('F' . $stamp_last_line); // 设置在C列
        $drawing->setPath($sealImagePath);
        $drawing->setWidth(150);
        $drawing->setHeight(150);
        $drawing->setWorksheet($event->sheet->getDelegate());

        // 根据明细数量调整行高，避免页末的电子印章只显示部分
        $total_line = count($this->pdfInfo['itemList']) + 60; // 总行数
        $order_item_num = count($this->pdfInfo['itemList']);

        for ($i = 0; $i <= 5; $i++) {
            // 3 + $i * 26 || 4 + $i * 26 到该行时，需要设置行高
            if ($order_item_num == (3 + $i * 26)) {
                $adjust_line = $total_line - 6;
                $event->sheet->getDelegate()->getRowDimension($adjust_line)->setRowHeight(55);
                break;
            }
            if ($order_item_num == (4 + $i * 26)) {
                $adjust_line = $total_line - 6;
                $event->sheet->getDelegate()->getRowDimension($adjust_line)->setRowHeight(40);
                break;
            }
        }
    }


}
