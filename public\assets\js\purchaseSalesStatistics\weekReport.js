layui.use(['form', 'table', 'admin', 'laydate', 'index', 'xmSelect'], function () {
    var form = layui.form;
    var table = layui.table;
    var admin = layui.admin;
    var laydate = layui.laydate;
    var xmSelect = layui.xmSelect;
    var index = layui.index;
    var pur_department_ids = '';//采购部门

    layui.form.render();
    //模糊匹配
    mhpp(".lxbox");

    window.IndexController = {
        init: function () {
            this.created(this).render(this).handleBind(this);
        },
        created: function () {
            var  currentYearInit = new Date().getFullYear();
            var weekDayinit = getWeekDateRange(currentYearInit, 1);
            $("#create_time").val(weekDayinit["startDate"] + "~" + weekDayinit["endDate"])
            //采购部门
            pur_department_ids = xmSelect.render({
                el: '#department_option',
                name: 'purchase_group_id',
                //autoRow: true,
                filterable: true,
                direction: 'down',
                model: {
                    label: {
                        type: 'text'
                    }
                },
                tree: {
                    show: true,
                    showFolderIcon: true,
                    showLine: true,
                    indent: 20,
                    expandedKeys: true,
                },
                size: 'mini',
                toolbar: {
                    show: true,
                    list: ['ALL', 'CLEAR']
                },
                height: '250',
                data: function () {
                    return createUserDepartmentList;

                }
            })
            return this;
        },
        render: function () {

            table.render({
                elem: '#list',
                url: '/api/purchaseSalesStatistics/weekReport',
                page: true,
                // toolbar: '#toolbar',
                where:{
                    order_time:$("#create_time").val()
                },
                size: 'sm',
                height: 445,
                cols: [[
                    // { type: 'checkbox', fixed: true, width: 45, align: 'center' },
                    { type: 'numbers', title: '序号', width: 50, align: 'center' },
                    { field: 'purchase_dept', title: '部门', width: 100 },
                    { field: 'purchase_group', title: '组别', width: 100 },
                    { field: 'model_count', title: '型号数', width: 80 },
                    { field: 'order_count', title: '订单数量', width: 80 },
                    { field: 'supplier_count', title: '成交供应商数', width: 120 },
                    { field: 'purchase_amount', title: '采购额', width: 140 },
                    { field: 'sales_amount', title: '销售额', width: 140 },
                    { field: 'gross_profit_amount', title: '毛利', width: 140 },
                    { field: 'gross_margin', title: '毛利率', width: 120 },
                    { field: 'mtd_model_count', title: 'MTD-型号数', width: 120 },
                    { field: 'mtd_order_count', title: 'MTD-订单数', width: 120 },
                    { field: 'mtd_order_count_ratio', title: 'MTD-订单数同比去年', width: 150 },
                    { field: 'mtd_supplier_count', title: 'MTD-供应商数', width: 150 },

                    { field: 'mtd_supplier_count_ratio', title: 'MTD-供应商数同比去年', width: 160 },
                    { field: 'mtd_sales_amount', title: 'MTD-销售额', width: 120 },
                    { field: 'mtd_purchase_amount', title: 'MTD-采购额', width: 120 },
                    { field: 'mtd_gross_profit_amount', title: 'MTD-毛利', width: 120 },

                ]],
                parseData: LayUiTableParseData,
                done: function (res, curr, count) {

                }
            });

            //监听头工具栏事件
            table.on('toolbar(list)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id);
                var data = checkStatus.data;
                switch (obj.event) {
                    case 'export':
                        var obj_ = form.val("ListForm")
                        Request('/api/purchaseSalesStatistics/exportList', 'GET', obj_, function (res) {
                            if (res.code == 0) {
                                window.open(res.data)
                            } else {
                                layer.open({
                                    title: '提示',
                                    content: res.msg
                                });
                            }
                        });


                        break;
                }
            });



            //查询
            form.on('submit(formDemo)', function (data) {
                table.reload('list', {
                    page: {
                        curr: 1
                    },
                    where: data.field
                });
            });

            return this;
        },
        handleBind: function () {
            // 监听select选择
            form.on('select(week)', function (data) {
                var  currentYear = new Date().getFullYear();
                var weekDay = getWeekDateRange(currentYear, data.value);
                $("#create_time").val(weekDay["startDate"] + "~" + weekDay["endDate"])
            });


            return this;
        }
    }
    /**
    * 根据年份和周数计算当周起止日期
    * @param {number} year - 年份（如2025）
    * @param {number} week - ISO周数（1~53）
    * @returns {Object} { startDate, endDate }
    */
    function getWeekDateRange(year, week) {
        // 1. 计算当年1月1日
        const janFirst = new Date(year, 0, 1);
        const janFirstDay = janFirst.getDay() || 7; // 转换为ISO星期（1=周一，7=周日）

        // 2. 计算当年第一个周四（ISO周定义关键点）
        const firstThursday = new Date(janFirst);
        firstThursday.setDate(janFirst.getDate() + (4 - janFirstDay + 7) % 7);

        // 3. 计算目标周的周一
        const weekStart = new Date(firstThursday);
        weekStart.setDate(firstThursday.getDate() + (week - 1) * 7 - 3); // 回退到周一

        // 4. 计算当周周日
        const weekEnd = new Date(weekStart);
        weekEnd.setDate(weekStart.getDate() + 6);

        return {
            startDate: formatDate(weekStart),
            endDate: formatDate(weekEnd),
            startObj: weekStart,
            endObj: weekEnd
        };
    }

    // 日期格式化辅助函数
    function formatDate(date) {
        return date.toISOString().split('T')[0]; // 返回YYYY-MM-DD格式
    }
    IndexController.init();

});