<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Style;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class PurContractCnExcelExport  implements FromView, WithStyles, WithColumnWidths, WithEvents
{
    private $pdfInfo;
    private $lang = 'US';

    public function __construct($pdfInfo,$lang='US')
    {
        $this->pdfInfo = $pdfInfo;
        $this->lang = $lang;
    }

    public function view(): View
    {
        $purPdfInfo = $this->pdfInfo;
        $signViewPath = resource_path("views/purDemand/pur_contract_export/{$purPdfInfo['sign_company_info']['sign_com_id']}_{$this->lang}.blade.php");
        $view = "purDemand.pur_contract_export.{$purPdfInfo['sign_company_info']['sign_com_id']}_{$this->lang}";
        if (!file_exists($signViewPath)) {
            throw new \Exception("{$purPdfInfo['sign_company_info']['com_name']} 签约公司模板不存在");
        }
        return view($view, [
            "pdfInfo"=>$purPdfInfo
        ]);
    }

    public function columnWidths(): array
    {
        return [
            'A' => 10,
            'B' => 10,
            'C' => 10,
            'D' => 10,
            'E' => 10,
            'F' => 10,
            'G' => 10,
            'H' => 10,
            'I' => 10,
            'J' => 10,
            'K' => 10,
            'L' => 10,
            'M' => 10,
            'N' => 10,
            'O' => 10,
        ];
    }

    public function defaultStyles(Style $defaultStyle)
    {
        return $defaultStyle->getFont()->setSize(8);
    }

    public function styles(Worksheet $sheet)
    {
        // 获取当前工作表中有数据的最高行
        $highestRow = $sheet->getHighestRow();

        // 动态生成要应用样式的范围
        $range = "A1:O{$highestRow}";

        return [
            $range => [
                'font' => ['size' => 10],
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                        'color' => ['argb' => 'FF000000'], // 黑色边框
                    ],
                ],
            ],
        ];
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                // 明细垂直居中
                $items_last_line = count($this->pdfInfo['itemList']) + 14; // 最后行 = 明细数量 + 明细前固定行数
                $event->sheet->getDelegate()->getStyle('A9:H' . $items_last_line)->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
            }
        ];
    }


}
