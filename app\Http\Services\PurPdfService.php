<?php

namespace App\Http\Services;

use App\Exceptions\InvalidRequestException;
use App\Http\Models\PurchaseContractModel;
use App\Http\Models\PurchaseOrderModel;
use App\Http\Models\PurchaseItemsModel;
use App\Http\Utils\Currency;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use GuzzleHttp\Client;

/**
 * 采购PDF服务类
 * 优化PDF数据获取和公章处理
 */
class PurPdfService extends BaseService
{
    const SEAL_CACHE_DURATION = 24 * 60; // 24小时缓存
    const SEAL_STORAGE_PATH = 'seals/';

    protected $isGetSignCompany = true;
    protected $isExcel = true;

    public function __construct($isExcel = false, $isGetSignCompany = true)
    {
        $this->isExcel = $isExcel;
        $this->isGetSignCompany = $isGetSignCompany;
    }

    /**
     * 获取采购单PDF响应数据（优化版本）
     * @throws InvalidRequestException
     */
    public function getPurDemandPdfResponseData(int $purchaseId): array
    {
        // 获取基础PDF数据
        $purPdfInfo = $this->getPurDemandPdfData($purchaseId);
        // 构建响应数据
        $purPdfResponseInfo = $this->buildPdfResponseData($purPdfInfo);
        return $purPdfResponseInfo;
    }

    /**
     * 获取采购单PDF基础数据（优化版本）
     */
    public function getPurDemandPdfData($id)
    {
        $orderInfo = PurchaseOrderModel::getPurOrderInfo($id);
        //只有订单信息必须存在
        if (empty($orderInfo)) {
            throw  new InvalidRequestException("订单信息不存在");
        }
        $itemList = PurchaseItemsModel::getAllItemsListByPurId($id);
        $contractList = PurchaseContractModel::getContractListByPurchaseId($id);
        //供应商信息 包含了退货信息
        $supInfo = SupplierService::getSupplierBySupplierId($orderInfo['supplier_id']);
        //采购员地址
        $purchaseUserInfo = CmsUserService::getCmsUserInfoById($orderInfo['purchase_uid']);
        $signCompanyMap = SignCompanyService::getSignCompanyMap(100);
        $signCompanyInfo = $signCompanyMap[$orderInfo["sign_company_id"]] ?? [];
        // 处理公章图片
        $signCompanyInfo['seal_image'] = $this->getCompanySealPath($orderInfo['sign_company_id'], $signCompanyInfo['com_chop_src']);
        $signCompanyInfo['seal_image_base64'] = $this->getCompanySealBase64($signCompanyInfo['seal_image']);
        if (!$this->isExcel) {
            $signCompanyInfo['seal_image'] = $signCompanyInfo['seal_image_base64'];
        }
        return [
            'orderInfo'         => $orderInfo,
            'purchaseUserInfo'  => $purchaseUserInfo,
            'supInfo'           => $supInfo,
            'itemList'          => $itemList,
            'contractList'      => $contractList,
            "sign_company_info" => $signCompanyInfo,
        ];
    }

    private function getCompanySealBase64($filePath)
    {
        if (empty($filePath) || !file_exists($filePath)) {
            return "";
        }

        return "data:image/png;base64," . base64_encode(file_get_contents($filePath));
    }

    /**
     * 构建PDF响应数据（优化版本）
     */
    private function buildPdfResponseData($data)
    {
        $responseData = [];
        //采购单详情
        $orderInfo = $data['orderInfo'];
        //供应商详情
        $supInfo = $data['supInfo'];
        //公司名
        //EZKEY ELECTRONICS CO.,  LIMITED
        $companyAddressArr = [
            1 => [
                'address' => '广东省深圳市龙岗区坂田街道清理路1号宝能科技园南区12栋11楼',
                'tel'     => '86-755-82560956',
            ],
            2 => [
                'address' => '香港九龙观塘成业街27号日昇中心9楼903C室',
                'tel'     => '0852-35908493',
            ]
        ];
        $creatTime = date("Y-m-d", strtotime($orderInfo["create_time"]));
        $responseData['orderInfo'] = [
            'purchaseNum'       => $orderInfo['purchase_sn'],                                        //采购编号
            'sign_company_id'   => $orderInfo['sign_company_id'],                                    //采购编号
            'company_id'        => $orderInfo['company_id'],                                         //采购编号
            'date'              => $creatTime,                                                       //日期
            'partyACompanyName' => $orderInfo['company_name'],                                       //采购组织
            'status'            => $orderInfo['status'],                                             //订单状态
            'partyAAddress'     => $companyAddressArr[$orderInfo['company_id']]['address'] ?? '',    //甲方地址
            'partyATel'         => $companyAddressArr[$orderInfo['company_id']]['tel'] ?? '',        //甲方电话
            'partyAFax'         => $companyAddressArr[$orderInfo['company_id']]['tel'] ?? '',        //甲方传真
            'partyAContactName' => $orderInfo['purchase_name'],                                      //甲方联系人
            'companyId'         => $orderInfo['company_id'],                                         //采购单组织
            'partyBCompanyName' => $orderInfo['supplier_name'],                                      //采购组织
            'partyBAddress'     => $supInfo['supplier_address'] ?? '',                               //甲方地址,//甲方地址
            'remark'            => $orderInfo['remark'],                                             //备注
            "currency_code"     => \Arr::get(config("field.currency_code"), $orderInfo['currency'], ""),
            'tax'               => (($data['itemList'][0]['tax_rate'] ?? 0) - 1) * 100,
        ];

        //付款方式
        $payName = PurchaseOrderModel::$PAY_TYPE[$orderInfo['pay_id']];
        if ($orderInfo['pay_id'] == PurchaseOrderModel::PAY_TYPE_ADVANCE_CHARGE) {
            if ($orderInfo['first_pay_type'] == PurchaseOrderModel::FIRST_PAY_TYPE_RATIO) {
                $payName .= "预付比率" . ($orderInfo['first_pay_amount'] * 100) . "%";
            } elseif ($orderInfo['first_pay_type'] == PurchaseOrderModel::FIRST_PAY_TYPE_NUMBER) {
                $payName .= "订金" . price_format($orderInfo['first_pay_amount'], $data['orderInfo']['currency'], '2');
            }
        }
        if ($orderInfo['pay_id'] == PurchaseOrderModel::PAY_TYPE_CASH_ON_DELIVERY || $orderInfo['pay_id'] == PurchaseOrderModel::PAY_TYPE_PAYMENT_TERM) {
            $payName .= " {$orderInfo['supp_pay_days']}天";
        }

        $responseData['orderInfo']['payName'] = $payName;
        //付款方式(英文)
        $payName = PurchaseOrderModel::$PAY_TYPE_EN[$orderInfo['pay_id']];
        if ($orderInfo['pay_id'] == PurchaseOrderModel::PAY_TYPE_ADVANCE_CHARGE) {
            if ($orderInfo['first_pay_type'] == PurchaseOrderModel::FIRST_PAY_TYPE_RATIO) {
                $payName .= " " . ($orderInfo['first_pay_amount'] * 100) . "%";
            } elseif ($orderInfo['first_pay_type'] == PurchaseOrderModel::FIRST_PAY_TYPE_NUMBER) {
                $payName .= "Deposit " . price_format($orderInfo['first_pay_amount'], $data['orderInfo']['currency'], '2');
            }
        }
        if ($orderInfo['pay_id'] == PurchaseOrderModel::PAY_TYPE_CASH_ON_DELIVERY || $orderInfo['pay_id'] == PurchaseOrderModel::PAY_TYPE_PAYMENT_TERM) {
            $payName .= " {$orderInfo['supp_pay_days']} days";
        }
        $responseData['orderInfo']['payNameEn'] = $payName;


        //供应商联系人
        $supContact = SupplierService::getSupplierContactByPurchaseUid($orderInfo["supplier_id"], $orderInfo["purchase_uid"]);
        //供应商联系人查找信息
        $contactInfo = $supContact[0] ?? [];
        foreach ($supContact as $v) {
            if ($orderInfo["supplier_contact_id"] == $v["contact_id"]) {
                $contactInfo = $v;
            }
        }
        $responseData['orderInfo']['partyBContactName'] = $contactInfo['supplier_consignee'] ?? '';
        $responseData['orderInfo']['partyBEmail'] = $contactInfo['supplier_email'] ?? '';
        $responseData['orderInfo']['partyBTel'] = $contactInfo['supplier_mobile'] ?? '';
        $responseData['orderInfo']['partyBFax'] = $contactInfo['supplier_fax'] ?? '';
        if (empty($responseData['orderInfo']['partyBTel'])) {
            $responseData['orderInfo']['partyBTel'] = $contactInfo['supplier_telephone'] ?? '';
        }
        //采购详情数据处理
        $responseData['itemList'] = [];
        $itemDataIndex = 1;
        $itemPriceTotal = 0;
        foreach ($data['itemList'] as $itemKey => $item) {
            $responseData['itemList'][$itemKey] = [
                'index'               => $itemDataIndex,       //序号
                'goodsName'           => $item['goods_name'],  //物料名称
                'brandName'           => $item['brand_name'],  //品牌名称
                'purchaseQty'         => $item['purchase_qty'],//数量
                'dateCode'            => $item['date_code'],   //批次
                'estimatDeliveryTime' => $item['estimat_delivery_time'] == 0 ? "" : date('Y-m-d', $item['estimat_delivery_time']),
                'frqRemark'           => truncStr($item['order_remark'], 50),//下单备注
                'warehouseReceiptSn'  => $item['warehouse_receipt_sn'],
                'order_remark'        => $item['order_remark'],
            ];
            //去除frqRemark中的空格和换行
            $responseData['itemList'][$itemKey]['frqRemark'] = preg_replace("/\s+/", "", $responseData['itemList'][$itemKey]['frqRemark']);
            if ($data['orderInfo']['currency'] == 1) {//人民币
                $unitPrice = $item['price_in_tax'];
            } elseif ($data['orderInfo']['currency'] == 2) {//美元
                $unitPrice = $item['price_without_tax'];
            } else {
                $unitPrice = $item['price_without_tax'];
            }
            $responseData['itemList'][$itemKey]['unitPrice'] = price_format($unitPrice, '', 6, '');
            $responseData['itemList'][$itemKey]['price'] = price_format($unitPrice * $item['purchase_qty'], '', 2, '');
            $itemPriceTotal += price_format($unitPrice * $item['purchase_qty'], "", 2, "");
            $itemDataIndex++;
        }
        //采购详情合同列表
        $responseData['contractList'] = $data['contractList'];
        //采购详情总金额
        $responseData['itemPriceTotal'] = price_format($itemPriceTotal, $data['orderInfo']['currency'], 2);
        //采购人民币类别
        $responseData['currencyType'] = Currency::getName($data['orderInfo']['currency']) ?? "未知";
        //        var_dump($data['orderInfo'],$responseData['itemPriceTotal']);die;
        //采购员信息
        $responseData['purchaseUserInfo'] = $data['purchaseUserInfo'];
        $responseData['sign_company_info'] = $data['sign_company_info'];
        $responseData['supInfo'] = $data['supInfo'];
        return $responseData;
    }

    /**
     * 获取公司公章本地路径
     */
    private function getCompanySealPath(int $signCompanyId, $url): ?string
    {
        $path = base_path() . "/public/" . self::SEAL_STORAGE_PATH . "company_{$signCompanyId}" . '.png';
        if ($path && Storage::disk('public')->exists($path)) {
            // 检查文件是否是今天创建的，如果不是则重新下载
            $fileTime = Storage::disk('public')->lastModified($path);
            dd($fileTime);
            if ($fileTime > strtotime('today')) {
                return $path;
            }
        }
        // 下载并缓存公章
        return $this->downloadAndCacheSeal($signCompanyId, $url);
    }

    /**
     * 下载并缓存公章
     */
    private function downloadAndCacheSeal(int $signCompanyId, $url): ?string
    {
        try {
            // 获取公章URL（这里需要根据实际业务逻辑获取）
            $sealUrl = $url;
            if (!$sealUrl) {
                return null;
            }

            $client = new Client(['timeout' => 30]);
            $response = $client->get($sealUrl);
            if ($response->getStatusCode() !== 200) {
                Log::warning("下载公章失败: HTTP {$response->getStatusCode()}", [
                    'company_id' => $signCompanyId,
                    'url'        => $sealUrl
                ]);
                return null;
            }

            // 生成文件名
            $fileName = base_path() . "/public/" . self::SEAL_STORAGE_PATH . "company_{$signCompanyId}" . '.png';
            // 保存文件
            $result = @file_put_contents($fileName, $response->getBody()->getContents());

            Log::info("公章下载成功", [
                'company_id' => $signCompanyId,
                'file_path'  => $fileName
            ]);
            return $fileName;
        } catch (\Exception $e) {
            Log::error("下载公章异常", [
                'company_id' => $signCompanyId,
                'error'      => $e->getMessage()
            ]);
            return null;
        }
    }
}
