<?php

namespace App\Console\Commands;


use App\Http\Models\EndCustInfoModel;
use App\Http\Services\EndCustInfoService;
use Illuminate\Console\Command;

class UpdateEndCustInfoEntityListStatusCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:update_entity_list_status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '修改终端公司实体名单状态';


    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        echo '修改状态开始';
        $endCustInfoModel = new EndCustInfoModel();
        $companyArr = $endCustInfoModel->select('end_cust_cn','end_cust_id')->get()->toArray();

        $endCustInfoService = new EndCustInfoService();
        foreach ($companyArr as $value){
            try{
                $status = $endCustInfoService->getCompanyEntityListStatusFromCompanyName(
                    ['company_name_cn'=>$value['end_cust_cn']]
                )['result'];
                $endCustInfoModel->where('end_cust_id',$value['end_cust_id'])->update(['entity_list_status'=>$status]);
            }catch (\Exception $exception){
                echo $exception->getMessage();
                continue;
            }
        }
    }
}
