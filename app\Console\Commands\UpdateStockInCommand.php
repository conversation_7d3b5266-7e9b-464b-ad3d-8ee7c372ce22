<?php

namespace App\Console\Commands;


use App\Exceptions\InvalidRequestException;
use App\Exceptions\PurException;
use App\Http\IdSender\IdSender;
use App\Http\Models\PurchaseItemsModel;
use App\Http\Models\PurchaseOrderModel;
use App\Http\Models\PurchasePlanModel;
use App\Http\Models\ScmBrandModel;
use App\Http\Models\ScmOrderModel;
use App\Http\Models\ScmOrderTemporaryModel;
use App\Http\Models\StockInItemModel;
use App\Http\Models\StockInModel;
use App\Http\Services\PurOrderService;
use App\Http\Services\SupplierDeliveryService;
use App\Http\Services\Sync\ScmOrderSyncService;
use App\Http\Services\Sync\StockInSyncService;
use App\Http\Services\Sync\StockInSyncWMSService;
use App\Imports\ErpStockListImport;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Http;

class UpdateStockInCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:update_stock_in';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '作废/修改发货通知单';


    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle($arguments = [])
    {
        DB::beginTransaction();
        ini_set('memory_limit', '512M');
        self::invalidStockIn();
        DB::commit();
    }

    public static function invalidStockIn()
    {
        echo "请输入作废/修改的入库明细id:\n";

        $str = fgets(STDIN);
        $str = trim($str);
        if (!$str) {
            throw new InvalidRequestException('作废的入库明细id不能为空');
        }
        $stockInItemInfo = StockInItemModel::query()->with(["stock_in"])->where("stock_in_item_id", $str)->first();
        if (empty($stockInItemInfo)) {
            throw new InvalidRequestException('入库明细不存在');
        }
        echo "已获取到数据\n";
        StockInItemModel::updateStockItemInfo(['stock_in_item_id' => $stockInItemInfo['stock_in_item_id']], ["item_status" => -3]);
        echo "作废入库明细成功\n";
        StockInModel::where("stock_in_id",$stockInItemInfo['stock_in_id'])->update(["status"=>-3]);
        echo "作废入库单成功\n";
        //更新采购明细状态
        //获取入库明细中该采购明细所有发货数量
        $totalOutQty = StockInItemModel::getAllOutQty($stockInItemInfo['purchase_item_id']);
        //同步发货数量到采购明细
        $bk = (new PurchaseItemsModel)->syncOutQty($stockInItemInfo['purchase_item_id'], $totalOutQty);
        echo "修改采购单明细发货数量成功\n";
        $puritemIdInQty = StockInItemModel::getPurItemsInQty([$stockInItemInfo['purchase_item_id']]);
        $in_qty  = $puritemIdInQty[$stockInItemInfo['purchase_item_id']]["total_in_qty"] ?? 0;
        $bk = (new PurchaseItemsModel)->syncInQty($stockInItemInfo['purchase_item_id'], $in_qty);
        echo "修改采购单明细入库数量成功\n";

        (new PurchaseOrderModel())->changeShippingStatus($stockInItemInfo['purchase_id']);//同步采购单主单的发货状态
        (new PurchaseOrderModel())->changeStockInStatus($stockInItemInfo['purchase_id']);//同步采购单主单的入库状态
        (new PurOrderService())->purOrderComplete([$stockInItemInfo['purchase_id']]);//尝试更改采购单状态为完成
        echo "更新采购单状态成功\n";

    }

}
