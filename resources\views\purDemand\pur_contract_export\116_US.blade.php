<!DOCTYPE html>
<html>

  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>Purchase Order Contract</title>
    <style type="text/css" media="screen">
      * {
        font-family: "simsun";
        font-weight: normal;
        margin: 0px;
        padding: 0px;
        color: #000;
        font-size: 12px;
      }

      b {
        font-weight: bold;
      }

      .ta-c {
        text-align: center;
      }

      .ta-r {
        text-align: right;
      }

      .pdfx {
        width: 750px;
        margin: 0 auto;
        background: #fff;
        padding-bottom: 10px;
      }

      table {
        border-collapse: collapse;
      }

      .header {
        position: relative;
        line-height: 50px;
      }

      .company {
        font-size: 30px;
      }

      .dname {
        font-size: 20px;
      }

      .tab-hea {
        height: 24px;
        line-height: 24px;
      }

      .tab-hea .hea-item {
        padding-top: 3px;
      }

      .prec {
        height: 14px;
        line-height: 14px;
      }

      .prec span {
        display: inline-block;
        width: 120px;
        vertical-align: top;
      }

      .prec div {
        display: inline-block;
      }

      table {
        border-collapse: collapse;
        width: 100%;
      }

      table td {
        padding: 3px;
        text-align: center;
      }

      .mb-5 {
        margin-bottom: 5px;
      }

      .tkitem {
        line-height: 20px;
        word-break: break-all;
        word-wrap: break-word;
        word-break: normal;
      }

      .bk {
        display: inline-block;
      }

      .yfktype {
        width: 200px;
        border-bottom: 1px solid #000;
        margin-left: 150px;
      }

      .headhp td {
        width: 50%;
        text-align: left;
      }

      .headhp {
        margin-top: 10px;
      }

      .msitem {
        line-height: 14px;
      }

      .ftable td {
        z-index: 2;
        position: relative;
      }

      .yzz {
        position: absolute;
        width: 120px;
        height: 120px;
        top: -40px;
        right: 10px;
        z-index: 1;
      }
    </style>
  </head>

  <body>
    <div class="pdfx">
      <div class="header ta-c">
        <p class="company">SEMOUR ELECTRONICS CO., LIMITED</p>
      </div>

      <div class="ta-c">FLAT/RM 903C, 9/F., Sunbeam Center, 27 Shing Yip Street, Kwun Tong, Kowloon, Hong Kong.</div>

      <div class="dname ta-c">PURCHASE ORDER</div>

      <table class="mb-5 headhp">
        <tr>
          <td>
            <div class="prec"><span>Date:</span>
              <div>{{ $pdfInfo['orderInfo']['date'] }}</div>
            </div>
          </td>
          <td>
            <div class="prec"><span>PO No:</span>
              <div>{{ $pdfInfo['orderInfo']['purchaseNum'] }}</div>
            </div>
          </td>
        </tr>
        <tr>
          <td>
            <div class="prec"><span>Tel #:</span>
              <div>852-35908493</div>
            </div>
          </td>
          <td>
            <div class="prec"><span>Shipment Terms:</span>
              <div>FOB</div>
            </div>
          </td>
        </tr>
        <tr>
          <td>
            <div class="prec"><span>Fax #:</span>
              <div></div>
            </div>
          </td>
          <td>
            <div class="prec"><span>Shipping Method:</span>
              <div style="color: red">UPS Express Saver# 0000B87Y22</div>
            </div>
          </td>
        </tr>
        <tr>
          <td>
            <div class="prec"><span>Buyer:</span>
              <div>{{ $pdfInfo['purchaseUserInfo']['name'] }}</div>
            </div>
          </td>
          <td>
            <div class="prec"><span>Payment Terms:</span>
              <div>{{ $pdfInfo['orderInfo']['payNameEn'] }}</div>
            </div>
          </td>
        </tr>
        <tr>
          <td>
            <div class="prec"><span>SHIP FROM:</span>
              <div>{{ $pdfInfo['orderInfo']['partyBCompanyName'] }}</div>
            </div>
          </td>
          <td>
            <div class="prec"><span>SHIP TO: </span>
              <div>SEMOUR ELECTRONICS CO., LIMITED</div>
            </div>
          </td>
        </tr>
        <tr>
          <td>
            <div class="prec">
              <span>Address: </span>
              <div>{{ $pdfInfo['orderInfo']['partyBAddress'] }} </div>
            </div>
          </td>
          <td> ADDRESS : FLAT/RM 903C 9/F SUNBEAM CTR 27 SHING YIP ST KWUN TONG ,KL,HK</td>
        </tr>

        <tr>
          <td>
            <div class="prec"><span>TEL:</span>
              <div>{{ $pdfInfo['orderInfo']['partyBTel'] }}</div>
            </div>
          </td>
          <td>
            <div class="prec"><span>TEL:</span>
              <div>852-35908493</div>
            </div>
          </td>
        </tr>
        <tr>
          <td>
            <div class="prec"><span>FAX:</span>
              <div>{{ $pdfInfo['orderInfo']['partyBFax'] }}</div>
            </div>
          </td>
          <td>
            <div class="prec"><span>FAX:</span>
              <div></div>
            </div>
          </td>
        </tr>
        <tr>
          <td>
            <div class="prec"><span>ATTN:</span>
              <div>{{ $pdfInfo['orderInfo']['partyBContactName'] }}</div>
            </div>
          </td>
          <td>
            <div class="prec"><span>ATTN: </span>
              <div>MR.LAM</div>
            </div>
          </td>
        </tr>
      </table>

      <table class="mb-5" border="1">
        <tr>
          <td><b>MFG P/N</b></td>
          <td><b>MFR</b></td>
          <td><b>Date Code</b></td>
          <td><b>REMARK</b></td>
          <td><b>Delivery Date</b></td>
          <td><b>QTY</b></td>
          <td><b>Unit Price</b></td>
          <td><b>Total Amount</b></td>
        </tr>
        @foreach ($pdfInfo['itemList'] as $itemInfo)
          <tr>
            <td>{{ $itemInfo['goodsName'] }}</td>
            <td>{{ $itemInfo['brandName'] }}</td>
            <td>{{ $itemInfo['dateCode'] }}</td>
            <td>{{ $itemInfo['warehouseReceiptSn'] }}</td>
            <td>{{ $itemInfo['estimatDeliveryTime'] }}</td>
            <td>{{ $itemInfo['purchaseQty'] }}</td>
            <td>{{ $itemInfo['unitPrice'] }}</td>
            <td>{{ $itemInfo['price'] }}</td>
          </tr>
        @endforeach
        <tr>
          <td colspan="7">Total Amount({{ $pdfInfo['orderInfo']['currency_code'] }})</td>
          <td>{{ $pdfInfo['itemPriceTotal'] }}</td>
        </tr>
        <tr>
          <td>Remark:</td>
          <td colspan="7" style="text-align:left;">
            1.Please kindly advise tracking No.to us once the order is delivered.<br>
            2.Please kindly note our correct PO No. on the invoice and Packling list.
          </td>
        </tr>

      </table>

      <div style="page-break-inside: avoid;break-inside: avoid;">
        <div class="msitem" style="margin-top:10px;"><b>Purchase order terms:</b></div>
        <div class="msitem">1, Vendor should supply the parts as order described.</div>
        <div class="msitem">2, The parts should be new and original factory sealed, and non-oxygenation.</div>
        <div class="msitem">3, Please sign back the order with 1 day you received, attach correct banking
          information.</div>
        <div class="msitem">4, If the parts have any quality issues within 180 days from delivery, verdor should
          accept returns and refund.</div>
        <div class="msitem">5, If could not delivery or could not delivery on time after order confirmed,</div>
        <div class="msitem">vendor should pay 3% of the amount for compensation.</div>
        <div class="msitem">6, Semour can cancel the order if any description of below happened：</div>
        <div class="msitem">The parts are different as order describe.</div>
        <div class="msitem">The delivery date delay.</div>
        <div class="msitem">Vendor refuses to sign back the order.</div>
        <div class="msitem">Vendor breaks any native or international laws.</div>
        <div style="position: relative">

          @if ($pdfInfo['orderInfo']['status'] >= 2)
            @if (isset($pdfInfo['sign_company_info']['seal_image']) && !empty($pdfInfo['sign_company_info']['seal_image']) && empty($pdfInfo['is_excel']))
              <div style="position: relative;">
                <img src="{{ $pdfInfo['sign_company_info']['seal_image'] }}" class="yzz">
              </div>
            @endif
          @endif

          <table class="mb-5 headhp ftable">
            <tr>
              <td class="ta-c">
                <div class="ta-c">Confirmed and Accepted By Supplier</div>
                <div class="ta-c">{{ $pdfInfo['orderInfo']['partyBCompanyName'] }}</div>
              </td>
              <td>
                <div class="ta-c">Confirmed and Accepted By Buyer</div>
                <div class="ta-c">SEMOUR ELECTRONICS CO., LIMITED</div>
              </td>
            </tr>
          </table>
        </div>
      </div>
    </div>

  </body>

</html>
