<!DOCTYPE html>
<html>

  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>Purchase Order Contract</title>
    <style type="text/css" media="screen">
      * {
        font-family: "simsun";
        font-weight: normal;
        margin: 0px;
        padding: 0px;
        color: #000;
        font-size: 12px;
      }

      b {
        font-weight: bold;
      }

      .ta-c {
        text-align: center;
      }

      .ta-l {
        text-align: left;
      }

      .ta-r {
        text-align: right;
      }

      .main-table {
        width: 750px;
        margin: 0 auto;
        background: #fff;
        border-collapse: collapse;
      }

      table {
        border-collapse: collapse;
        width: 100%;
      }

      .header-cell {
        padding: 10px;
        line-height: 50px;
        text-align: center;
      }

      .company {
        font-size: 30px;
        font-weight: bold;
      }

      .address-cell {
        padding: 5px;
        text-align: center;
        font-size: 12px;
      }

      .title-cell {
        padding: 10px;
        text-align: center;
        font-size: 20px;
        font-weight: bold;
      }

      .info-table {
        width: 100%;
        border-collapse: collapse;
        margin: 10px 0;
      }

      .info-table td {
        padding: 3px;
        text-align: left;
        vertical-align: top;
        width: 50%;
      }

      .label-span {
        display: inline-block;
        width: 120px;
        font-weight: normal;
      }

      .value-span {
        display: inline-block;
        font-weight: normal;
      }

      .items-table {
        width: 100%;
        border-collapse: collapse;
        border: 1px solid #000;
        margin: 5px 0;
      }

      .items-table td, .items-table th {
        border: 1px solid #000;
        padding: 3px;
        text-align: center;
        font-size: 12px;
      }

      .items-table th {
        font-weight: bold;
        background-color: #f0f0f0;
      }

      .terms-table {
        width: 100%;
        border-collapse: collapse;
        margin: 10px 0;
      }

      .terms-cell {
        padding: 2px 5px;
        text-align: left;
        line-height: 14px;
        vertical-align: top;
      }

      .signature-table {
        width: 100%;
        border-collapse: collapse;
        margin: 10px 0;
        position: relative;
      }

      .signature-cell {
        padding: 10px;
        text-align: center;
        width: 50%;
        vertical-align: top;
      }

      .seal-container {
        position: relative;
      }

      .yzz {
        position: absolute;
        width: 120px;
        height: 120px;
        top: -40px;
        right: 10px;
        z-index: 1;
      }
    </style>
  </head>

  <body>
    <!-- 主表格 - 8列布局 -->
    <table class="main-table">
      <!-- 公司名称标题行 - 合并8列 -->
      <tr>
        <td colspan="8" class="header-cell">
          <div class="company">SEMOUR ELECTRONICS CO., LIMITED</div>
        </td>
      </tr>

      <!-- 公司地址行 - 合并8列 -->
      <tr>
        <td colspan="8" class="address-cell">
          FLAT/RM 903C, 9/F., Sunbeam Center, 27 Shing Yip Street, Kwun Tong, Kowloon, Hong Kong.
        </td>
      </tr>

      <!-- 文档标题行 - 合并8列 -->
      <tr>
        <td colspan="8" class="title-cell">
          PURCHASE ORDER
        </td>
      </tr>

      <!-- 订单基本信息 - 每行4+4列 -->
      <tr>
        <td colspan="4" class="info-table">
          <span class="label-span">Date:</span>
          <span class="value-span">{{ $pdfInfo['orderInfo']['date'] }}</span>
        </td>
        <td colspan="4" class="info-table">
          <span class="label-span">PO No:</span>
          <span class="value-span">{{ $pdfInfo['orderInfo']['purchaseNum'] }}</span>
        </td>
      </tr>


      <tr>
        <td colspan="4" class="info-table">
          <span class="label-span">Tel #:</span>
          <span class="value-span">852-35908493</span>
        </td>
        <td colspan="4" class="info-table">
          <span class="label-span">Shipment Terms:</span>
          <span class="value-span">FOB</span>
        </td>
      </tr>

      <tr>
        <td colspan="4" class="info-table">
          <span class="label-span">Fax #:</span>
          <span class="value-span"></span>
        </td>
        <td colspan="4" class="info-table">
          <span class="label-span">Shipping Method:</span>
          <span class="value-span" style="color: red">UPS Express Saver# 0000B87Y22</span>
        </td>
      </tr>

      <tr>
        <td colspan="4" class="info-table">
          <span class="label-span">Buyer:</span>
          <span class="value-span">{{ $pdfInfo['purchaseUserInfo']['name'] }}</span>
        </td>
        <td colspan="4" class="info-table">
          <span class="label-span">Payment Terms:</span>
          <span class="value-span">{{ $pdfInfo['orderInfo']['payNameEn'] }}</span>
        </td>
      </tr>

      <tr>
        <td colspan="4" class="info-table">
          <span class="label-span">SHIP FROM:</span>
          <span class="value-span">{{ $pdfInfo['orderInfo']['partyBCompanyName'] }}</span>
        </td>
        <td colspan="4" class="info-table">
          <span class="label-span">SHIP TO:</span>
          <span class="value-span">SEMOUR ELECTRONICS CO., LIMITED</span>
        </td>
      </tr>

      <tr>
        <td colspan="4" class="info-table">
          <span class="label-span">Address:</span>
          <span class="value-span">{{ $pdfInfo['orderInfo']['partyBAddress'] }}</span>
        </td>
        <td colspan="4" class="info-table">
          ADDRESS : FLAT/RM 903C 9/F SUNBEAM CTR 27 SHING YIP ST KWUN TONG ,KL,HK
        </td>
      </tr>

      <tr>
        <td colspan="4" class="info-table">
          <span class="label-span">TEL:</span>
          <span class="value-span">{{ $pdfInfo['orderInfo']['partyBTel'] }}</span>
        </td>
        <td colspan="4" class="info-table">
          <span class="label-span">TEL:</span>
          <span class="value-span">852-35908493</span>
        </td>
      </tr>

      <tr>
        <td colspan="4" class="info-table">
          <span class="label-span">FAX:</span>
          <span class="value-span">{{ $pdfInfo['orderInfo']['partyBFax'] }}</span>
        </td>
        <td colspan="4" class="info-table">
          <span class="label-span">FAX:</span>
          <span class="value-span"></span>
        </td>
      </tr>

      <tr>
        <td colspan="4" class="info-table">
          <span class="label-span">ATTN:</span>
          <span class="value-span">{{ $pdfInfo['orderInfo']['partyBContactName'] }}</span>
        </td>
        <td colspan="4" class="info-table">
          <span class="label-span">ATTN:</span>
          <span class="value-span">MR.LAM</span>
        </td>
      </tr>

      <!-- 产品表格标题行 - 8列对应产品信息 -->
      <tr>
        <td style="border: 1px solid #000; padding: 3px; text-align: center; font-weight: bold;">MFG P/N</td>
        <td style="border: 1px solid #000; padding: 3px; text-align: center; font-weight: bold;">MFR</td>
        <td style="border: 1px solid #000; padding: 3px; text-align: center; font-weight: bold;">Date Code</td>
        <td style="border: 1px solid #000; padding: 3px; text-align: center; font-weight: bold;">REMARK</td>
        <td style="border: 1px solid #000; padding: 3px; text-align: center; font-weight: bold;">Delivery Date</td>
        <td style="border: 1px solid #000; padding: 3px; text-align: center; font-weight: bold;">QTY</td>
        <td style="border: 1px solid #000; padding: 3px; text-align: center; font-weight: bold;">Unit Price</td>
        <td style="border: 1px solid #000; padding: 3px; text-align: center; font-weight: bold;">Total Amount</td>
      </tr>

      @foreach ($pdfInfo['itemList'] as $itemInfo)
      <tr>
        <td style="border: 1px solid #000; padding: 3px; text-align: center;">{{ $itemInfo['goodsName'] }}</td>
        <td style="border: 1px solid #000; padding: 3px; text-align: center;">{{ $itemInfo['brandName'] }}</td>
        <td style="border: 1px solid #000; padding: 3px; text-align: center;">{{ $itemInfo['dateCode'] }}</td>
        <td style="border: 1px solid #000; padding: 3px; text-align: center;">{{ $itemInfo['warehouseReceiptSn'] }}</td>
        <td style="border: 1px solid #000; padding: 3px; text-align: center;">{{ $itemInfo['estimatDeliveryTime'] }}</td>
        <td style="border: 1px solid #000; padding: 3px; text-align: center;">{{ $itemInfo['purchaseQty'] }}</td>
        <td style="border: 1px solid #000; padding: 3px; text-align: center;">{{ $itemInfo['unitPrice'] }}</td>
        <td style="border: 1px solid #000; padding: 3px; text-align: center;">{{ $itemInfo['price'] }}</td>
      </tr>
      @endforeach

      <tr>
        <td colspan="7" style="border: 1px solid #000; padding: 3px; text-align: center;">Total Amount({{ $pdfInfo['orderInfo']['currency_code'] }})</td>
        <td style="border: 1px solid #000; padding: 3px; text-align: center;">{{ $pdfInfo['itemPriceTotal'] }}</td>
      </tr>

      <tr>
        <td style="border: 1px solid #000; padding: 3px; text-align: center;">Remark:</td>
        <td colspan="7" style="border: 1px solid #000; padding: 3px; text-align: left;">
          1.Please kindly advise tracking No.to us once the order is delivered.<br>
          2.Please kindly note our correct PO No. on the invoice and Packling list.
        </td>
      </tr>

      <div style="page-break-inside: avoid;break-inside: avoid;">
        <div class="msitem" style="margin-top:10px;"><b>Purchase order terms:</b></div>
        <div class="msitem">1, Vendor should supply the parts as order described.</div>
        <div class="msitem">2, The parts should be new and original factory sealed, and non-oxygenation.</div>
        <div class="msitem">3, Please sign back the order with 1 day you received, attach correct banking
          information.</div>
        <div class="msitem">4, If the parts have any quality issues within 180 days from delivery, verdor should
          accept returns and refund.</div>
        <div class="msitem">5, If could not delivery or could not delivery on time after order confirmed,</div>
        <div class="msitem">vendor should pay 3% of the amount for compensation.</div>
        <div class="msitem">6, Semour can cancel the order if any description of below happened：</div>
        <div class="msitem">The parts are different as order describe.</div>
        <div class="msitem">The delivery date delay.</div>
        <div class="msitem">Vendor refuses to sign back the order.</div>
        <div class="msitem">Vendor breaks any native or international laws.</div>
        <div style="position: relative">

          @if ($pdfInfo['orderInfo']['status'] >= 2)
            @if (isset($pdfInfo['sign_company_info']['seal_image']) && !empty($pdfInfo['sign_company_info']['seal_image']) && empty($pdfInfo['is_excel']))
              <div style="position: relative;">
                <img src="{{ $pdfInfo['sign_company_info']['seal_image'] }}" class="yzz">
              </div>
            @endif
          @endif

          <table class="mb-5 headhp ftable">
            <tr>
              <td class="ta-c">
                <div class="ta-c">Confirmed and Accepted By Supplier</div>
                <div class="ta-c">{{ $pdfInfo['orderInfo']['partyBCompanyName'] }}</div>
              </td>
              <td>
                <div class="ta-c">Confirmed and Accepted By Buyer</div>
                <div class="ta-c">SEMOUR ELECTRONICS CO., LIMITED</div>
              </td>
            </tr>
          </table>
        </div>
      </div>
    </div>

  </body>

</html>
