@include('css')
<link rel="stylesheet" href="/assets/js/select2.css?v={{time()}}">
<style>
    .lay-box .layui-form-label {
        width: 135px;
    }

    .layui-table, .layui-table-view {
        margin-top: 0px;
    }

    .tableboxk {
        overflow-x: auto;
    }

    .layui-table font {
        color: red;
    }

    .tableboxk input, .tableboxk select {
        height: 28px;
        line-height: 28px;
    }

    .dropdown-root {
        background-color: #FFF;
        z-index: 891;
        position: absolute;
        width: 179px;
        border: 1px solid #f3f3f3;
        min-height: 10px;
        max-height: 300px;
        overflow: auto;
        display: none;
    }

    .dropdown-root .menu-item {
        padding: 0 5px;
        cursor: pointer;
        height: 22px;
        line-height: 22px;
    }

    .dropdown-root .menu-item:hover {
        background: #f3f3f3;
    }

    .rtext {
        height: 32px;
        line-height: 32px;
        white-space: nowrap;
    }

    .bccc {
        background: rgba(255, 255, 255, 0);
        border-color: rgba(255, 255, 255, 0) !important;
        padding-left: 0px;
    }

    .bccc:hover {
        border-color: rgba(255, 255, 255, 0) !important;
    }

    .cfshow .goodsname {
        position: relative;
    }

    .cfshow .goodsname::after {
        content: "拆";
        position: absolute;
        background: #FF5722;
        width: 20px;
        height: 20px;
        text-align: center;
        border-radius: 10px;
        color: #fff;
        margin-left: 3px;
    }

    .dropdown-root {
        background-color: #FFF;
        z-index: 99999999999999;
        position: absolute;
        min-width: 178px;
        border: 1px solid #f3f3f3;
        min-height: 10px;
        max-height: 200px;
        overflow: auto;
        display: none;
        white-space: nowrap;

    }

    .dropdown-root-brand {
        min-width: 700px;
    }

    .f-red {
        color: red;
    }

    .dropdown-root .menu-item {
        padding: 0 5px;
        cursor: pointer;
        height: 22px;
        line-height: 22px;
    }

    .dropdown-root .menu-item:hover {
        background: #f3f3f3;
    }

    .dropdown-root-name, .dropdown-root-customer {
        padding-top: 10px;
        padding-bottom: 10px;
    }

    .dropdown-root-customer {
        min-width: 400px !important;
    }

    .dropdown-root-name .menu-item, .dropdown-root-customer .menu-item {
        height: 28px;
        line-height: 28px;
    }

    .dropdown-root-name .menu-item .f-red, .dropdown-root-customer .menu-item .f-red {
        color: red;
    }

    .layui-btn-disabled {
        background: #ccc;
        cursor: not-allowed;
        color: #fff;
    }

    .dropdown-root-name {
        min-width: 720px;
    }

    .dropdown-root-name .menu-item {
        height: auto !important;
        word-break: break-all;
        word-wrap: break-word;
    }

    .color-red {
        color: red;
        font-style: normal;
        font-weight: bold;
    }

    em {
        font-style: normal;
    }

    .redact {
        background: #f7eded !important;
    }

    .layui-icon-tips {
        font-size: 16px;
        color: #ffa200;
        margin-left: 4px;
        cursor: pointer;
    }

    .custom-radio .layui-form-radio {
        margin-top: 0;
    }

    .siiconbox {
        color: #ffb800;
        position: absolute;
        right: -20px;
        top: 0px;
        font-size: 18px;
        top: 2px;
        cursor: pointer;
        display: none;
    }

    .siiconbox i {
        font-size: 20px;
    }

    .layui-btn + .layui-btn.cfbtn {
        margin-left: 5px;
    }

    .sku-weight-text {
        font-size: 14px;
        color: #000;
    }

    .sku-weight-text em {
        color: #FF5722 !important;
        font-weight: bold;
    }
</style>

<div class="lay-box">
    <div class="dropdown-root dropdown-root-brand">
        <div class="menu-item">lm357</div>
        <div class="menu-item">lm358</div>
        <div class="menu-item">lm359</div>
    </div>
    <input type="hidden" value="1" id="com_id">
    <input type="hidden" value="{{$is_default_no_qc}}" id="is_default_no_qc">
    <input type="hidden" value="{{$is_auto_fill_number}}" id="is_auto_fill_number">
    <input type="hidden" value="{{$user_email}}" id="user_email">
    <input type="hidden" value="0" id="supplier_group">
    <input type="hidden" value="{{$is_default_no_qc}}" id="is_default_no_qc">
    <input type="hidden" value="{{$supplier_ids}}" id="supplier_ids">
    <input type="hidden" value="{{config('field.MROSupplierId')}}" id="mro_supplier_id">
    <form class="layui-form layui-box formbox" method="post" lay-filter="puraddForm">
        <input type="hidden" name="dgk" value="{{config("field.DGKSupplierId")}}">
        <input type="hidden" name="is_sku_offer" value="">
        <input type="hidden" name="supplier_change_reason" value="">
        <div>
            <button class="layui-btn layui-btn-normal layui-btn-sm" is_apply="0" lay-submit lay-filter="purAdd" id="purAddSubmit">保存</button>
            <button class="layui-btn layui-btn-primary layui-btn-sm" onclick="closeCurrentPageJumpOne('采购需求管理','/web/purDemand/purDemand')">取消</button>
        </div>
        <br>
        <blockquote class="layui-elem-quote layui-text">基本信息</blockquote>
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">
                    <font style="color:red;">*</font>采购组织：
                </label>
                <div class="layui-input-inline">
                    <select name="company_id" lay-filter="companyChange" id="company_id_input">
                        @foreach(config("field.PurchaseCompany") as $com_id=>$com_name)
                            <option value="{{$com_id}}">{{$com_name}}</option>
                        @endforeach
                    </select>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">币别：</label>
                <div class="layui-input-inline">
                    <select name="currency" lay-filter="currencyChange" disabled="disabled">
                        @foreach($currencyList as $currInfo)
                            <option data-symbol="{{$currInfo["symbol"]}}" data-currencyCn="{{$currInfo["currencyCn"]}}" value="{{$currInfo["id"]}}">{{$currInfo["code"]}}({{$currInfo["currencyCn"]}})</option>
                        @endforeach
                    </select>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">汇率：</label>
                <div class="layui-input-inline">
                    <input type="text" name="rate" value="1" autocomplete="off" disabled class="layui-input" style="border-color:#fff!important;">
                </div>
            </div>
            <div class="layui-inline" style="position:relative;">
                <label class="layui-form-label">
                    <font style="color:red;">*</font>供应商名称：
                </label>
                <div class="layui-input-inline lxbox lxboxClick" guid="supplier_id" params="1" urls="/api/supplier/searchSupplier" type="auto">
                    <div id="supplier_id" style="float: left;width: 168px;"></div>
                    <input type="hidden" name="supplier_id" value="" class="layui-input supplier_id">
                </div>
                <span class="siiconbox siiconboxsupplier"><i class="layui-icon layui-icon-about"></i></span>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">
                    <font style="color:red;">*</font>付款方式：
                </label>
                <div class="layui-input-inline">
                    <select name="pay_id" lay-filter="fkfsyh">
                        @foreach(\App\Http\Models\PurchaseOrderModel::$PAY_TYPE as $pay_id=>$pay_name)
                            <option value="{{$pay_id}}">{{$pay_name}}</option>
                        @endforeach
                    </select>
                </div>
            </div>
            <div class="layui-inline yfkbox">
                <label class="layui-form-label">
                    <font style="color:red;">*</font>首款要求：
                </label>
                <div class="layui-input-inline" style="width:98px;">
                    <select name="first_pay_type" lay-filter="skval">
                        <option value="1">首款比率</option>
                        <option value="2">首款金额</option>
                    </select>
                </div>
                <div class="layui-input-inline" style="width:62px;">
                    <input type="number" name="first_pay_amount" lay-verify="required" value="100" style="width:62px;" placeholder="%" autocomplete="off" class="layui-input skyz">
                </div>
            </div>
            <div class="layui-inline zqbox" style="display:none;">
                <label class="layui-form-label">
                    <font style="color:red;">*</font>天数：
                </label>
                <div class="layui-input-inline zqts" style="display:none;">
                    <select name="supp_pay_days" id="" class="zqts">
                        <option value="1">1天</option>
                        <option value="7">7天</option>
                        <option value="15">15天</option>
                    </select>
                </div>
                <div class="layui-input-inline zqts1" style="display:none;">
                    <select name="supp_pay_days1" id="" class="zqts1">
                        <option value="7">7天</option>
                        <option value="15">15天</option>
                        <option value="30">30天</option>
                        <option value="45">45天</option>
                        <option value="60">60天</option>
                        <option value="90">90天</option>
                    </select>
                </div>
            </div>
            <div class="layui-inline" style="position:relative;">
                <label class="layui-form-label"><font style="color:red;display: none" class="labelRequired">*</font>终端公司名称：</label>
                <div class="layui-input-inline">
                    <input type="text" placeholder="请输入终端公司名称" class="layui-input zdcns" name="zdcns" autocomplete="off"/>
                    <input type="hidden" name="endCustId" id="endCustId">
                </div>
                <span class="siiconbox siiconboxendCustId"><i class="layui-icon layui-icon-about"></i></span>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">终端公司英文名称：</label>
                <div class="layui-input-inline">
                    <input type="text" autocomplete="off" class="layui-input zden" disabled name="zden" style="background:#f3f3f3;">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">产品用途一级：</label>
                <div class="layui-input-inline">
                    <input type="text" autocomplete="off" class="layui-input chyj" disabled style="background:#f3f3f3;" name="chyj">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">产品用途二级：</label>
                <div class="layui-input-inline">
                    <input type="text" autocomplete="off" class="layui-input chrj" disabled name="chrj" style="background:#f3f3f3;">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">客户类型：</label>
                <div class="layui-input-inline">
                    <input type="text" autocomplete="off" class="layui-input khlxs" disabled name="khlxs" style="background:#f3f3f3;">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">备注：</label>
                <div class="layui-input-inline">
                    <input type="text" name="remark" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label"><font style="color:red;">*</font>签约公司：</label>
                <div class="layui-input-inline">
                    <span style="line-height: 30px;" class="sign_company_name"></span>
                    <input type="hidden" name="sign_com_id" value="">
                </div>
            </div>
            <div class="layui-block" style="padding-top:15px;"></div>
        </div>
    </form>
    <blockquote class="layui-elem-quote layui-text row bothSide verCenter">
        <div>
            明细&nbsp;&nbsp;&nbsp;
            <div class="layui-btn layui-btn-sm daoru" id="mb_import">导入采购明细</div>
            <a href="/excel/采购明细-导入.xlsx" class="layui-btn layui-btn-sm " style="margin-left:10px;">下载导入采购明细模板</a>
            <a class="layui-btn layui-btn-sm syncGoodsPrice" style="display: none">同步DGK价格</a>
            <!-- <a class="layui-btn layui-btn-sm tbdjk">同步DGK下单</a> -->
            <a class="layui-btn layui-btn-sm qhben">取号</a>
            <a class="layui-btn layui-btn-sm synchronizePrice" style="display: none">同步下单价格</a>
            <!-- <a class="layui-btn layui-btn-sm chooseOrderPrice" style="display: none">选择下单价格</a> -->
        </div>
        <div class="sku-weight-text" style="display: none">
            该采购单总重量为<em class="totalWeight"></em>, 预计运费<em class="estimatedShippingCost"></em>
        </div>
    </blockquote>
    <div class="tableboxk">
        <table class="layui-table" style="table-layout:fixed" lay-size="sm">
            <thead>
            <th width="28">序号</th>
            <th width="77">操作</th>
            <th width="180">型号</th>
            <th width="150">品牌</th>
            <th width="60">需求数量</th>
            <th width="80">
                <font>*</font>采购数量
            </th>
            <th width="100">
                <font>*</font>采购单价(未税)
            </th>
            <th width="100" class="hsshtd">
                <font>*</font>采购单价(含税)
            </th>
            <th width="120">小计</th>
            <th width="160">
                <font>*</font>交货仓
            </th>
            <th width="60">
                交货地
            </th>
            <th width="200" class="warehouse_receipt_sn_th"><font class="warehouse_receipt_sn_th_font" style="display: none">*</font>入仓单号</th>
            <th width="150"><font>*</font>预计交货时间<i class="layui-icon layui-icon-edit time-pre-handle" title="批量修改" style="cursor: pointer"></i></th>
            <th width="150" class="cdgh">
                <font style="display: none">*</font>
                产地
            </th>
            <th width="150">是否质检</th>
            <th width="150">质检要求</th>
            <th width="120" class="custom-tips">是否需要看货</th>
            <th width="100">
                <span>仓库打印标签</span>
                <i class="layui-icon layui-icon-edit" title="批量修改" style="cursor: pointer" id="batch_modify"></i>
            </th>
            <th width="150">D/C</th>
            <th width="150">下单备注</th>
            <th width="40">赠品</th>
            <th width="60">税率%</th>

            <th width="160">商品编码</th>
            </thead>
            <tbody class="tb-section tbmplbox"></tbody>
        </table>
    </div>
    <div style="text-align:right;font-size:14px;color:#666;padding-right:10px;">
        <b>税费：<font style="color:#FF5722;" class="tax_amount">--</font></b> &nbsp;&nbsp;&nbsp;
        <b>采购订单总金额：<font style="color:#FF5722;" class="without_total_amount">--</font></b>&nbsp;&nbsp;&nbsp;
        <b>合计：<font style="color:#FF5722;" class="total_amount">--</font></b>
    </div>
</div>

<!--明细列表-->
<script id="tbmpl" type="text/html">
    <%#  layui.each(d.list, function(index, item){ %>
    <%# if((item.default_pur_qty) >Number(item.frq_qty)){ %>
    <tr class="cgdAddTr datatr redact" frq_id="<% item.frq_id %>" supp_goods_sn="<% item.supp_goods_sn %>" order_item_id="<% item.order_item_id %>" supplier_group="<% item.supplier_group %>">
        <%# } else { %>
    <tr class="cgdAddTr datatr" frq_id="<% item.frq_id %>" supp_goods_sn="<% item.supp_goods_sn %>" order_item_id="<% item.order_item_id %>" supplier_group="<% item.supplier_group %>">
        <%# } %>
        <td class="xuhao">1</td>
        <td>
            <input type="hidden" class="spu_name" value="<% item.goods_name %>">
            <input type="hidden" class="standard_brand_id" value="<% item.brand_id %>">
            <div>
                <a class="layui-btn   layui-btn-xs scbtn">删除</a>
                <a class="layui-btn   layui-btn-xs cfbtn">拆分</a>
            </div>
        </td>
        <td>
            <div class="goodsname"><% item.goods_name %></div>
        </td>
        <td>
            <div class="brandname" title="<% item.brand_name %>" style="white-space:nowrap;overflow:hidden;text-overflow:ellipsis;">
                <% item.brand_name %>
            </div>
        </td>
        <td>
            <% item.frq_qty %>
        </td>
        <td>
            <div class="row verCenter">
                <%# if(item.frq_type == 2){ %>
                <input type="number" class="layui-input number" frq_qty="<% (item.frq_qty ) %>" max="<% (item.default_pur_qty ) %>" value="<% (item.default_pur_qty ) %>"/>
                <%# } else { %>
                <input type="number" class="layui-input number " frq_qty="<% (item.frq_qty ) %>" value="<% (item.default_pur_qty ) %>"/>
                <%# } %>
                <%# if(d.is_show_weight == 1){ %>
                <span class="row rowCenter verCenter heavyTag" lay-tips="" style="width: 33px;height: 20px;border-radius: 50%;border: 1px solid #009688;color: #009688;margin-left: 5px;cursor: pointer;display: flex">重</span>
                <%# } else { %>
                <span class="row rowCenter verCenter heavyTag" lay-tips="" style="width: 33px;height: 20px;border-radius: 50%;border: 1px solid #009688;color: #009688;margin-left: 5px;cursor: pointer;display: none">重</span>
                <%# } %>
            </div>
        </td>
        <td>
            <input type="hidden" class="dgk_sale_price" value="0">
            <%# if(item.is_gift == 1){ %>
            <input type="number" class="layui-input wsprice" min="0" data-val="0.000000" value="0.000000" disabled style="background:#f3f3f3;"/>
            <%# } else { %>
            <input type="number" class="layui-input wsprice" min="0" data-val="<% item.default_pur_without_tax_price %>" value="<% item.default_pur_without_tax_price %>"/>
            <%# } %>
        </td>
        <td class="hsshtd">
            <%# if(item.is_gift == 1){ %>
            <input type="number" class="layui-input hsprice" data-val="0.000000" value="0.000000" disabled style="background:#f3f3f3;"/>
            <%# } else { %>
            <input type="number" class="layui-input hsprice" data-val="<% item.default_pur_price %>" value="<% item.default_pur_price %>"/>
            <%# } %>
        </td>
        <td>
            <span date-val="" class="xjmoney"><% item.total_amount %></span><span class="price_type" style="margin-left: 10px;"></span>
        </td>
        <td>
            <input type="hidden" name="frq_type" class="frq_typehidddenval" value="<% item.frq_type %>">
            <select name="warehouse_id" class="warehouse_id" sku_id="<% item.sku_id %>" guidp="<% item.warehouse_id %>" sales_currency="<% item.sales_currency %>">
                <option value="">请选择</option>
                @foreach(config("field.Warehouse") as $warehouse_id => $warehouse_name)
                    <%# if(item.warehouse_id && item.warehouse_id=={{$warehouse_id}}){ %>
                    <option value="{{$warehouse_id}}" rrr="111" selected> @if(intval($warehouse_id) < 100)
                            {{$warehouse_name}}(猎芯)
                        @elseif(intval($warehouse_id) < 200)
                            {{$warehouse_name}}(深贸)
                        @elseif(intval($warehouse_id) >= 200 && intval($warehouse_id) < 230)
                            {{$warehouse_name}}(华云)
                        @elseif(intval($warehouse_id) >= 230 && intval($warehouse_id) < 260)
                            {{$warehouse_name}}
                        @elseif(intval($warehouse_id) >= 260 && intval($warehouse_id) < 290)
                            {{$warehouse_name}}
                        @elseif(intval($warehouse_id) >= 290 && intval($warehouse_id) < 300)
                            {{$warehouse_name}}
                        @elseif(intval($warehouse_id) >= 300 && intval($warehouse_id) < 330)
                            {{$warehouse_name}}
                        @endif</option>
                    <%# } else { %>
                    <option value="{{$warehouse_id}}">
                        @if(intval($warehouse_id) < 100)
                            {{$warehouse_name}}(猎芯)
                        @elseif(intval($warehouse_id) < 200)
                            {{$warehouse_name}}(深贸)
                        @elseif(intval($warehouse_id) >= 200 && intval($warehouse_id) < 230)
                            {{$warehouse_name}}(华云)
                        @elseif(intval($warehouse_id) >= 230 && intval($warehouse_id) < 260)
                            {{$warehouse_name}}
                        @elseif(intval($warehouse_id) >= 260 && intval($warehouse_id) < 290)
                            {{$warehouse_name}}
                        @elseif(intval($warehouse_id) >= 290 && intval($warehouse_id) < 300)
                            {{$warehouse_name}}
                        @elseif(intval($warehouse_id) >= 300 && intval($warehouse_id) < 330)
                            {{$warehouse_name}}
                        @endif
                    </option>
                    <%# } %>
                @endforeach
            </select>
        </td>
        <td>
            <% item.delivery_place_format %>
        </td>
        <td>
            <input type="text" class="layui-input warehouse_receipt_sn" placeholder="" value="" data-frq-type="<% item.frq_type %>" data-sales-currency="<% item.delivery_place %>"/>
        </td>
        <td>
            <input type="text" class="layui-input estimat_delivery_time" placeholder="" value="<% item.estimat_delivery_time %>"/>
        </td>
        <td width="150" class="cdgh">
            <div class="row verCenter">
                <select class="place_of_origin" is_need_place_of_origin="<% item.is_need_place_of_origin %>" lay-ignore="" style="width: 100%"></select>
            </div>
        </td>
        <td>
            <div class="row verCenter">
                <%# if(d.is_default_no_qc == 1){ %>
                <label class="row verCenter" style="margin-right: 8px;"><input class="is_qc" name="is_qc_<% new Date().getTime() %>_<% index %>" type="radio" value="1" style="margin-right: 5px"/>是</label>
                <label class="row verCenter"><input class="is_qc" name="is_qc_<% new Date().getTime() %>_<% index %>" type="radio" value="0" style="margin-right: 5px" checked/>否</label>
                <%# }else{ %>
                <label class="row verCenter" style="margin-right: 8px;"><input class="is_qc" name="is_qc_<% new Date().getTime() %>_<% index %>" type="radio" value="1" style="margin-right: 5px" checked/>是</label>
                <label class="row verCenter"><input class="is_qc" name="is_qc_<% new Date().getTime() %>_<% index %>" type="radio" value="0" style="margin-right: 5px"/>否</label>
                <%# } %>
            </div>
        </td>
        <td>
            <input type="text" class="layui-input inspect_remark" name="inspect_remark" placeholder="" value=""/>
        </td>
        <td>
            <div class="row verCenter">
                <label class="row verCenter" style="margin-right: 8px;"><input class="is_check_goods" name="is_check_goods_<% new Date().getTime() %>_<% index %>" type="radio" value="1" style="margin-right: 5px"/>是</label>
                <label class="row verCenter"><input class="is_check_goods" name="is_check_goods_<% new Date().getTime() %>_<% index %>" type="radio" value="0" style="margin-right: 5px" checked/>否</label>
            </div>
        </td>
        <td>
            <select name="wms_print_language" lay-ignore="" style="width: 100%">
                <option value="1">中文</option>
                <option value="2">英文</option>
            </select>
        </td>
        <td>
            <input type="text" class="layui-input date_code" is_need_dc="<% item.is_need_dc %>" placeholder="" value="<% item.date_code %>"/>
        </td>
        <td>
            <input type="text" class="layui-input remark" placeholder="" value="<% item.order_remark %>"/>
        </td>
        <td>
            <%# if(item.is_gift == 1){ %>
            <input type="checkbox" class="zpgift" checked guid="1">
            <%# } else { %>
            <input type="checkbox" class="zpgift" guid="0">
            <%# } %>
        </td>
        <td>
            <select class="tax_rate">
                <option value="0" <%# if(item.tax_rate == 0){ %>selected<%# } %>>0</option>
                <option value="1" <%# if(item.tax_rate == 1){ %>selected<%# } %>>1</option>
                <option value="3" <%# if(item.tax_rate == 3){ %>selected<%# } %>>3</option>
                <option value="6" <%# if(item.tax_rate == 6){ %>selected<%# } %>>6</option>
                <option value="7" <%# if(item.tax_rate == 7){ %>selected<%# } %>>7</option>
                <option value="8" <%# if(item.tax_rate == 8){ %>selected<%# } %>>8</option>
                <option value="9" <%# if(item.tax_rate == 9){ %>selected<%# } %>>9</option>
                <option value="13" <%# if(item.tax_rate == 13){ %>selected<%# } %>>13</option>
            </select>
        </td>
        <td>
            <input type="text" class="layui-input goodscode bccc" value="<% item.goods_sn %>" disabled/>
        </td>
    </tr>
    <%#  }); %>
</script>
<!--取号-->
<script type="text/html" id="qhpop">
    <form id="modelUserForm1" lay-filter="fetchNumber" class="layui-form model-form" onsubmit="return false;">
        <div class="layui-form-item">
            <label class="layui-form-label" style="padding:5px 15px;">
                <font style="color:red;">*</font>交货地
            </label>
            <div class="layui-input-block">
                <select name="sale_currency" lay-verify="required" lay-reqtext="请选择交货地" lay-vertype="tips" lay-filter="saleCurrencyChange">
                    <option value="">请选择交货地</option>
                    <option value="1">大陆</option>
                    <option value="2">香港</option>
                    <option value="12">泰国</option>
                    <option value="13">越南</option>
                    <option value="0">暂无</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label" style="padding:5px 15px;">
                <font style="color:red;">*</font>入仓单号类型
            </label>
            <div class="layui-input-block">
                <select name="orderType" lay-verify="required" lay-reqtext="请选择入仓单号类型" lay-vertype="tips">
                    <option value="">请选择入仓单号类型</option>
                    @foreach(\App\Http\Services\WarehouseReceiptSnService::ADD_PURCHASE_SELECT as $key=>$value)
                        <option value="{{$key}}">{{$value}}</option>
                    @endforeach
                </select>
            </div>
        </div>
        <div class="layui-form-item layui-aight-right">
            <button class="layui-btn layui-btn-sm" lay-filter="fetchNumberSubmit" lay-submit="">保存</button>
            <button class="layui-btn layui-btn-primary layui-btn-sm" onclick="layer.closeAll()">取消</button>
        </div>
    </form>
</script>
<!--终端公司名称-->
<script type="text/html" id="customerCompanyNameHtml">
    <%#  layui.each(d, function(index, item){ %>
    <option data="<% item.englishName %>" value="<% item.id %>"> <% item.name %></option>
    <%#  }); %>
</script>
<!--仓库打印标签修改-->
<script type="text/html" id="batchModifyHtml">
    <form lay-filter="fetchNumber" class="layui-form batchModifyForm" onsubmit="return false;" style="padding: 20px;">
        <div class="layui-form-item">
            <label class="layui-form-label" style="padding:5px 15px;">
                <font style="color:red;">*</font>标签版本
            </label>
            <div class="layui-input-block">
                <select name="print_language_batch" lay-verify="required" lay-reqtext="请选择标签版本" lay-vertype="tips">
                    <option value="1">中文</option>
                    <option value="2">英文</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item layui-aight-right" style="margin-bottom: 0">
            <button class="layui-btn layui-btn-sm" lay-filter="batchModifySubmit" lay-submit="">保存</button>
            <button class="layui-btn layui-btn-primary layui-btn-sm" onclick="layer.closeAll()">取消</button>
        </div>
    </form>
</script>
<!--产地批量修改-->
<script type="text/html" id="cooBatchHtml">
    <form lay-filter="fetchNumber" class="layui-form cooBatchForm" onsubmit="return false;" style="padding: 20px;">
        <div class="layui-form-item">
            <label class="layui-form-label" style="padding:5px 15px;">
                <font style="color:red;">*</font>选择语言
            </label>
            <div class="layui-input-block">
                <select name="print_language" lay-verify="required" lay-reqtext="请选择选择语言" lay-vertype="tips">
                    <option value="1">中文</option>
                    <option value="2">英文</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item layui-aight-right" style="margin-bottom: 0">
            <button class="layui-btn layui-btn-sm" lay-filter="batchCooSubmit" lay-submit="">保存</button>
            <button class="layui-btn layui-btn-primary layui-btn-sm" onclick="layer.closeAll()">取消</button>
        </div>
    </form>
</script>
<!--预计交货时间批量修改-->
<script type="text/html" id="timePreHtml">
    <form lay-filter="timePre" class="layui-form" onsubmit="return false;" style="padding: 20px;">
        <div class="layui-form-item">
            <label class="layui-form-label" style="padding:5px 15px;">
                <font style="color:red;">*</font>预计交货时间
            </label>
            <div class="layui-input-block">
                <input type="text" class="layui-input pre-time-val" lay-verify="required" lay-reqtext="请选择预计交货时间" lay-vertype="tips" placeholder="" readonly/>
            </div>
        </div>
        <div class="layui-form-item layui-aight-right" style="margin-bottom: 0">
            <button class="layui-btn layui-btn-sm" lay-filter="timePreSubmit" lay-submit="">确定</button>
            <button class="layui-btn layui-btn-primary layui-btn-sm" onclick="layer.closeAll()">取消</button>
        </div>
    </form>
</script>
<!--选择下单价格-->
<script type="text/html" id="chooseOrderPriceHtml">
    <form lay-filter="chooseOrderPricForm" class="layui-form" onsubmit="return false;" style="padding: 20px;">
        <div class="layui-form-item">
            <label class="layui-form-label" style="padding:5px 15px;width: 65px;">
                <font style="color:red;">*</font>价格类型：
            </label>
            <div class="layui-input-block custom-radio" style="min-height:30px;margin-left: 80px;">
                <input type="radio" name="price_type" value="2" title="成本价（未税）">
                <input type="radio" name="price_type" value="1" title="售价（未税）">
            </div>
        </div>
        <div class="layui-form-item layui-aight-right" style="margin-bottom: 0">
            <button class="layui-btn layui-btn-sm" lay-filter="chooseOrderPriceSubmit" lay-submit="">确定</button>
        </div>
    </form>
</script>

@include('js')
<script>
  var currencyRateAndTaxList = {!! $currencyRateAndTaxList !!}; //组织id=》object{id,com_name,currency_list,default_currency_id,exchange_rate_list,symbol，tax_rate}
  var is_huayun_gy_department_user = {!! $is_huayun_gy_department_user !!}; //判断是否为工业品采购部的采购员
  var signCompanyMap = {!! json_encode(\App\Http\Services\SignCompanyService::getSignCompanyMap(100)) !!} //签约公司
  var snTypeMap = {!! json_encode(\App\Http\Services\WarehouseReceiptSnService::getSnTypeForFrontEnd()) !!} //入仓单号类型
  var weightPriceRule = {!! json_encode(\App\Http\Services\GoodsService::WEIGHT_PRICE_RULE)!!} //运费规则
  console.log(weightPriceRule)
</script>
<script type="text/javascript" src="/assets/js/select2.js?v={{time()}}"></script>
<script type="text/javascript" src="/assets/js/purDemand/purAdd.js?v={{time()}}"></script>
