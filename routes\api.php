<?php

use Illuminate\Support\Facades\Route;

//接口相关路由
Route::namespace('Api')->group(function () {
    //采购计划
    Route::match(['get', 'post'], '/purchasePlan/getPurchasePlanList', 'PurchasePlanApiController@getPurchasePlanList');//获取采购计划列表
    Route::match(['get', 'post'], '/purchasePlan/deletePurchasePlan', 'PurchasePlanApiController@deletePurchasePlan');//删除采购计划列表
    Route::match(['get', 'post'], '/purchasePlan/disablePurchasePlan', 'PurchasePlanApiController@disablePurchasePlan');//作废采购计划
    Route::match(['get', 'post'], '/purchasePlan/applyAuditPurchasePlan', 'PurchasePlanApiController@applyAuditPurchasePlan');//申请审核采购计划
    Route::match(['get', 'post'], '/purchasePlan/addPurchasePlan', 'PurchasePlanApiController@addPurchasePlan');//新增采购计划
    Route::match(['get', 'post'], '/purchasePlan/updatePurchasePlan', 'PurchasePlanApiController@updatePurchasePlan')->name('updatePurchasePlan');//更新采购计划
    Route::match(['get', 'post'], '/purchasePlan/getPurchasePlanBrandList', 'PurchasePlanApiController@getPurchasePlanBrandList');//获取采购计划列表

    //采购计划详情
    Route::match(['get', 'post'], '/purchasePlanItem/getPurchasePlanItemList', 'PurchasePlanItemApiController@getPurchasePlanItemList');//获取采购计划详情列表
    Route::match(['get', 'post'], '/purchasePlanItem/addPurchasePlanItem', 'PurchasePlanItemApiController@addPurchasePlanItem');//新增采购计划详情
    Route::match(['get', 'post'], '/purchasePlanItem/updatePurchasePlanItem', 'PurchasePlanItemApiController@updatePurchasePlanItem')->name('updatePurchasePlanItem');//更新采购计划详情
    Route::match(['get', 'post'], '/purchasePlanItem/deletePurchasePlanItem', 'PurchasePlanItemApiController@deletePurchasePlanItem');//删除采购计划详情
    Route::match(['get', 'post'], '/purchasePlanItem/getPurchasePlanSku', 'PurchasePlanItemApiController@getPurchasePlanSku');//获取采购计划详情商品
    Route::match(['get', 'post'], '/purchasePlanItem/searchSpu', 'PurchasePlanItemApiController@searchSpu');//搜索采购计划商品
    Route::match(['get', 'post'], '/purchasePlanItem/sendStandardBrandByDing', 'PurchasePlanItemApiController@sendStandardBrandByDing');//发送标准品牌完善信息给运营

    //委托暂存
    Route::match(['get', 'post'], '/scmOrderTemporary/getScmOrderTemporaryList', 'ScmOrderTemporaryApiController@getScmOrderTemporaryList');//获取委托暂存列表
    Route::match(['get', 'post'], '/scmOrderTemporary/addScmOrderTemporary', 'ScmOrderTemporaryApiController@addScmOrderTemporary');//新增委托暂存
    Route::match(['get', 'post'], '/scmOrderTemporary/updateScmOrderTemporaryBaoguanData', 'ScmOrderTemporaryApiController@updateScmOrderTemporaryBaoguanData');//更新委托暂存报关数据
    Route::match(['get', 'post'], '/scmOrderTemporary/deleteScmOrderTemporary', 'ScmOrderTemporaryApiController@deleteScmOrderTemporary');//删除委托暂存
    Route::match(['get', 'post'], '/scmOrderTemporary/updateScmOrderTemporaryScmBrandName', 'ScmOrderTemporaryApiController@updateScmOrderTemporaryScmBrandName');//更新委托暂存供应链品牌
    Route::match(['get', 'post'], '/scmOrderTemporary/getScmOrderTemporarySupplierList', 'ScmOrderTemporaryApiController@getScmOrderTemporarySupplierList');//获取委托暂存的供应商列表
    Route::match(['get', 'post'], '/scmOrderTemporary/batchFetchNumber', 'ScmOrderTemporaryApiController@batchFetchNumber');//获取委托暂存的供应商列表
    Route::match(['get', 'post'], '/scmOrderTemporary/checkScmOrderWarehouseReceiptSn', 'ScmOrderTemporaryApiController@checkScmOrderWarehouseReceiptSn');//验证入仓单号是否生成了委托单
    Route::match(['get', 'post'], '/scmOrderTemporary/updateWarehouseReceiptSn', 'ScmOrderTemporaryApiController@updateWarehouseReceiptSn');//批量更新入仓单号

    //委托单
    Route::match(['get', 'post'], '/scmOrder/getScmOrderList', 'ScmOrderApiController@getScmOrderList');//获取委托单列表
    Route::match(['get', 'post'], '/scmOrder/addScmOrder', 'ScmOrderApiController@addScmOrder');//新增委托单
    Route::match(['get', 'post'], '/scmOrder/getScmOrderSupplierList', 'ScmOrderApiController@getScmOrderSupplierList');//获取委托单的供应商列表
    Route::match(['get', 'post'], '/scmOrder/getScmOrderStockInList', 'ScmOrderApiController@getScmOrderStockInList');//获取委托明细列表

    Route::match(['get', 'post'], '/scmOrder/getGYLScmOrderStockInList', 'ScmOrderApiController@getGYLScmOrderStockInList');//获取供应链委托入库列表
    Route::match(['get', 'post'], '/scmOrder/cancelScmOrder', 'ScmOrderApiController@cancelScmOrder');//作废委托单
    Route::match(['get', 'post'], '/scmOrder/editScmNumber', 'ScmOrderApiController@editScmNumber');//修改委托单数量

    //报关单
    Route::match(['get', 'post'], '/scmOrder/getCustomsList', 'ScmOrderApiController@getCustomsList');//获取报关列表
    Route::match(['get', 'post'], '/scmOrder/addCustoms', 'ScmOrderApiController@addCustoms');//新增报关

    //退货单
    Route::match(['get', 'post'], '/returnMaterial/getReturnMaterialList', 'ReturnMaterialApiController@getReturnMaterialList');//获取退货单列表
    Route::match(['get', 'post'], '/returnMaterial/getReturnMaterialForBOrder', 'ReturnMaterialApiController@getReturnMaterialForBOrder');//获取b单退货单列表
    Route::match(['get', 'post'], '/returnMaterial/disableReturnMaterial', 'ReturnMaterialApiController@disableReturnMaterial');//作废退货单
    Route::match(['get', 'post'], '/returnMaterial/applyAuditReturnMaterial', 'ReturnMaterialApiController@applyAuditReturnMaterial');//申请审核退货单
    Route::match(['get', 'post'], '/returnMaterial/getReturnMaterialSupplierList', 'ReturnMaterialApiController@getReturnMaterialSupplierList');//获取退货单供应商列表
    Route::match(['get', 'post'], '/returnMaterial/updateReturnMaterial', 'ReturnMaterialApiController@updateReturnMaterial');//更新退货单
    Route::match(['get', 'post'], '/returnMaterial/addReturnMaterial', 'ReturnMaterialApiController@addReturnMaterial')->name('addReturnMaterial');//新增退货单
    Route::match(['get', 'post'], '/returnMaterial/checkReturnMaterial', 'ReturnMaterialApiController@checkReturnMaterial')->name('checkReturnMaterial');//验证是否可以退货
    //退货出库明细
    Route::match(['get', 'post'], '/returnMaterial/getReturnMaterialStockInList', 'ReturnMaterialApiController@getReturnMaterialStockInList');//获取退货出库明细
    Route::match(['get', 'post'], '/returnMaterial/syncReturnMaterialStockInToErp', 'ReturnMaterialApiController@syncReturnMaterialStockInToErp');//同步退货单到金蝶


    //退货单详情
    Route::match(['get', 'post'], '/returnMaterialItem/getReturnMaterialItemList', 'ReturnMaterialItemApiController@getReturnMaterialItemList');//获取退货详情列表
    Route::match(['get', 'post'], '/returnMaterialItem/getReturnMaterialListForAddPage', 'ReturnMaterialItemApiController@getReturnMaterialListForAddPage');//获取退货详情列表(新增页面)

    //数据伪造路由
    Route::match(['get', 'post'], '/seedData/seed', function () { \App\Http\Services\DataService::test();});//数据伪造

    //供应链品牌
    Route::match(['get', 'post'], '/scmBrand/getScmBrandList', 'ScmBrandApiController@getScmBrandList');//获取供应商品牌列表

    //供应商
    Route::match(['get', 'post'], '/supplier/searchSupplier', 'SupplierApiController@searchSupplier');//供应商系统搜索供应商

    //采购需求
    Route::match(['get', 'post'], '/frq/getFrqlist', 'FrqApiController@getFrqlist');//获取采购需求列表
    Route::match(['get', 'post'], '/frq/del/tableFieldsCache', 'FrqApiController@delTableFieldsCache');//忽略
    Route::match(['get', 'post'], '/frq/delAll/tableFieldsCache', 'FrqApiController@delAllTableFieldsCache');//忽略
    Route::match(['get', 'post'], '/frq/getTableFieldsCache', 'FrqApiController@getTableFieldsCache');//忽略
    Route::match(["get", "post"], '/frq/shareFrq', "FrqApiController@shareFrq");//共享需求
    Route::match(["get", "post"], '/frq/deleteFrq', "FrqApiController@deleteFrq");//关闭需求
    Route::match(["get", "post"], '/frq/recycleFrq', "FrqApiController@recycleFrq");//回收需求
    Route::match(["get", "post"], '/frq/searchSupList', "FrqApiController@searchSupList");//检索供应商
    Route::match(["get", "post"], '/frq/searchComName', "FrqApiController@searchComName");//检索公司
    Route::match(["get", "post"], '/frq/getFrqBrandList', "FrqApiController@getFrqBrandList");//匹配采购需求品牌
    Route::match(["get", "post"], '/frq/getCmsSaleUser', "FrqApiController@getCmsSaleUser");//模糊检索销售员和采购员
    Route::match(["get", "post"], '/frq/getCmsPurUser', "FrqApiController@getCmsPurUser");//模糊检索销售员和采购员
    Route::match(["get", "post"], '/frq/getfqrlistByGoodsSn', "FrqApiController@getfqrlistByGoodsSn");//通过商品编码检索需求列表
    Route::match(["get", "post"], '/frq/getFrqListByAddPur', "FrqApiController@getFrqListByAddPur");//新增采购单获取需求明细列表
    Route::match(["get", "post"], '/frq/addPurOder/importFrq', "FrqApiController@importFrq");//新增采购单导入采购需求
    Route::match(["get", "post"], '/frq/importFrqToPurOrderAndStockIn', "FrqApiController@importFrqToPurOrderAndStockIn");//导入生成采购单并生成发货通知单
    Route::match(["get","post"], '/frq/addFrqToPurOrderAndStockIn', "FrqApiController@addFrqToPurOrderAndStockIn");//导入生成采购单并生成发货通知单
    Route::match(["get", "post"], '/frq/exportFrq', "FrqApiController@exportFrq");//导出采购需求
    Route::match(["get", "post"], '/frq/confirmOrder', "FrqApiController@confirmOrder");//确认订单
    Route::match(["get", "post"], '/frq/getPerformanceLevelPurList', "FrqApiController@getPerformanceLevelPurList");//获取履约程度采购员列表
    Route::match(["get", "post"], '/frq/getSameModelBrandSkuList', "FrqApiController@getSameModelBrandSkuList");//获取相同品牌型号的sku列表
    Route::match(["get", "post"], '/frq/getUserRelaPmInfo', "FrqApiController@getUserRelaPmInfo");//获取用户关联PM信息

    //寄售
    Route::match(["get", "post"], '/consignment/consignmentList', "ConsignmentApiController@consignmentList");//寄售申请单列表
    Route::match(["get", "post"], '/consignment/auditConsignment', "ConsignmentApiController@auditConsignment");//寄售申请单审核操作
    Route::match(["get", "post"], '/consignment/cancelConsignment', "ConsignmentApiController@cancelConsignment");//寄售申请单取消操作
    Route::match(["get", "post"], '/consignment/cancelConsignmentItems', "ConsignmentApiController@cancelConsignmentItems");//寄售申请单明细取消操作
    Route::match(["get", "post"], '/consignment/finishConsignment', "ConsignmentApiController@finishConsignment");//寄售申请单关单操作
    Route::match(["get", "post"], '/consignment/getConsignmentItems', "ConsignmentApiController@getConsignmentItems");//寄售申请单明细列表
    Route::match(["get", "post"], '/consignment/getRecallList', "ConsignmentApiController@getRecallList");//寄售召回列表
    Route::match(["get", "post"], '/consignment/getRecallDetail', "ConsignmentApiController@getRecallDetail");//寄售找回详情
    Route::match(["get", "post"], '/consignment/updateConsignmentItem', "ConsignmentApiController@updateConsignmentItem");//修改寄售详情

    //发货通知单 入库明细
    Route::match(["get","post"], '/stockIn/SuppDvery/getSupDeliveryItemsList', "SupplierDeliveryApiController@getSupDeliveryItemsList");//获取所有供应商发货明细列表
    Route::match(["get","post"], '/stockIn/SuppDvery/getOneStockItemsList', "SupplierDeliveryApiController@getOneStockItemsList");//获取单条供应商发货通知单明细列表
    Route::match(["get","post"], '/stockIn/SuppDvery/updateSupDvBasiInfo', "SupplierDeliveryApiController@updateSupDvBasiInfo");//修改供应商发货通知单基础信息
    Route::match(["get","post"], '/stockIn/SuppDvery/batchUpdateSupDvBasiInfo', "SupplierDeliveryApiController@batchUpdateSupDvBasiInfo");//修改供应商发货通知单基础信息
    Route::match(["get","post"], '/stockIn/SuppDvery/deleteSupDeliveryItems', "SupplierDeliveryApiController@deleteSupDeliveryItems");// 删除一条或者多条发货通知单明细
    Route::match(["get","post"], '/stockIn/SuppDvery/addSupDeliveryItems', "SupplierDeliveryApiController@addSupDeliveryItems");//新增一条或者多条发货通知单明细
    Route::match(["get","post"], '/stockIn/SuppDvery/importSupDeliItems', "SupplierDeliveryApiController@importSupDeliItems");//导入发货通知单入库明细
    Route::match(["get","post"], '/stockIn/SuppDvery/importSupDeliItemsFromUpdate', "SupplierDeliveryApiController@importSupDeliItemsFromUpdate");//编辑入库详情导入采购明细
    Route::match(["get","post"], '/stockIn/SuppDvery/abnormalSupDeliItems', "SupplierDeliveryApiController@abnormalSupDeliItems");//入库明细异常处理
    Route::match(["get","post"], '/stockIn/SuppDvery/updateSupDeliveryOutQty', "SupplierDeliveryApiController@updateSupDeliveryOutQty");//修改发货通知单明细发货数量
    Route::match(["get","post"], '/stockIn/SuppDvery/updateSupDeliveryRemark', "SupplierDeliveryApiController@updateSupDeliveryRemark");//修改发货通知单明细备注
    Route::match(["get","post"], '/stockIn/SuppDvery/addSupDelivery', "SupplierDeliveryApiController@addSupDelivery");//采购单中新增发货通知单
    Route::match(["get","post"], '/stockIn/SuppDvery/cancelSupDelivery', "SupplierDeliveryApiController@cancelSupDelivery");//作废发货通知单
    Route::match(["get","post"], '/stockIn/SuppDvery/getEmployees', "SupplierDeliveryApiController@getEmployees");//入库明细获取采购员和创建人
    Route::match(["get","post"], '/stockIn/SuppDvery/getFileImportStockInSyncKingDeeData', "SupplierDeliveryApiController@getFileImportStockInSyncKingDeeData");//获取批量导入发货单同步金蝶的数据
    Route::match(["get","post"], '/stockIn/SuppDvery/multiAddStockInSyncKingDee', "SupplierDeliveryApiController@multiAddStockInSyncKingDee");//批量导入发货单同步金蝶
    Route::match(["get","post"], '/stockIn/SuppDvery/updateSupDeliveryPlaceOfOrigin', "SupplierDeliveryApiController@updateSupDeliveryPlaceOfOrigin");//更新发货单明细产地
    Route::match(["get","post"], '/stockIn/SuppDvery/batchCancelSupDelivery', "SupplierDeliveryApiController@batchCancelSupDelivery");//批量作废发货通知单
    Route::match(["get","post"], '/stockIn/SuppDvery/getPlaceOfOriginList', "SupplierDeliveryApiController@getPlaceOfOriginList");//获取产地列表
    Route::match(["get","post"], '/stockIn/SuppDvery/v2/getPlaceOfOriginList', "SupplierDeliveryApiController@getPlaceOfOriginListV2");//获取产地列表v2
    Route::match(["get","post"], '/stockIn/SuppDvery/v2/getPlaceOfOriginListFromImport', "SupplierDeliveryApiController@getPlaceOfOriginListFromImport");//获取产地列表-导入物流
    Route::match(["get","post"], '/stockIn/SuppDvery/syncKingDeeByStockIn', "SupplierDeliveryApiController@syncKingDeeByStockIn");//发货单同步金蝶
    Route::match(["get","post"], '/stockIn/SuppDvery/ExportBatchEditShipment', "SupplierDeliveryApiController@ExportBatchEditShipment");//批量导入更新发货单物流信息
    Route::match(["get","post"], '/stockIn/SuppDvery/getExportBatchEditShipment', "SupplierDeliveryApiController@getExportBatchEditShipment");//获取导入的发货单物流信息
    Route::match(["get","post"], '/stockIn/SuppDvery/apply_special_approval', "SupplierDeliveryApiController@apply_special_approval");//申请特批审核入库

    //物流信息中间表
    Route::match(["get","post"], '/shippingIntermediateData/getList', "ShippingIntermediateDataApiController@getList");//申请特批审核入库
    Route::match(["get","post"], '/shippingIntermediateData/export', "ShippingIntermediateDataApiController@export");//物流中间表导出

    //采购单
    Route::match(["get","post"], '/purOrder/getPurOrderItemsList', "PurOrderApiController@getPurOrderItemsList");//通过采购单号查询明细列表
    Route::match(["get", "post"], '/purOrder/getSupplierName', "PurOrderApiController@getSupplierName");//获取供应商
    Route::match(["get","post"], '/pur/items/getPurOrderItemsByPurId', "PurOrderApiController@getPurOrderItemsByPurId");//新增发货通知单获取采购单明细
    Route::match(["get", "post"], '/purOrder/addPurOrder', "PurOrderApiController@addPurOrder");//新增采购单提交保存按钮操作
    Route::match(["get", "post"], '/purOrder/getPurOrderList', "PurOrderApiController@getPurOrderList");//获取采购单列表
    Route::match(["get","post"], '/purOrder/getPurOrderListByAddPayment', "PurOrderApiController@getPurOrderListByAddPayment");//新增付款申请单获取采购单明细列表
    Route::match(["get", "post"], '/purOrder/purOrderInvalid', "PurOrderApiController@purOrderInvalid");//作废采购单
    Route::match(["get","post"], '/purOrder/pruOrderApplyApprove', "PurOrderApiController@pruOrderApplyApprove");//采购单申请审核
    Route::match(["get","post"], '/purOrder/getPurOrderDetailItemsList', "PurOrderApiController@getPurOrderDetailItemsList");//采购单详情获取型号明细
    Route::match(["get", "post"], '/purOrder/searchPurOrderDetailItemsList', "PurOrderApiController@searchPurOrderDetailItemsList");//采购单详情型号明细搜索
    Route::match(["get", "post"], '/purOrder/deletePurOrderDetailItemsList', "PurOrderApiController@deletePurOrderDetailItemsList");//删除,作废采购单详情型号明细
    Route::match(["get", "post"], '/purOrder/exportPurOrderDetailItemsList', "PurOrderApiController@exportPurOrderDetailItemsList");//导出采购单详情型号明细
    Route::match(["get", "post"], '/purOrder/importPurOrderDetailItemsList', "PurOrderApiController@importPurOrderDetailItemsList");//导入采购单详情型号明细
    Route::match(["get", "post"], '/purOrder/purOrderInfo', "PurOrderApiController@purOrderInfo");//导入采购单详情型号明细
    Route::match(["get", "post"], '/purOrder/orderDetail/getPurOrderDetailitemlistsSummary', "PurOrderApiController@getPurOrderDetailitemlistsSummary");//采购单详情获取型号明细概括
    Route::match(["get", "post"], '/purOrder/updatePurOrderDetailBasisInfo', "PurOrderApiController@updatePurOrderDetailBasisInfo");//修改采购单详情基础信息
    Route::match(["get", "post"], '/purOrder/updatePurItem', "PurOrderApiController@updatePurItem");//修改采购型号明细相关数据
    Route::match(["get", "post"], '/purOrder/updatePurItemIsCheckGoods', "PurOrderApiController@updatePurItemIsCheckGoods");//修改采购明细是否看货
    Route::match(["get", "post"], '/purOrder/updatePurOrderDetailSupplier', "PurOrderApiController@updatePurOrderDetailSupplier");//修改采购单供应商信息
    Route::match(["get", "post"], '/purOrder/getPurOrderSupplierInfo', "PurOrderApiController@getPurOrderSupplierInfo");//获取供应商第一个联系人
    Route::match(["get", "post"], '/purOrder/getPuritemsOtherNums', 'PurOrderApiController@getPuritemsOtherNumsByOrderItemId');//批量统计订单商品相关数量
    Route::match(["get", "post"], '/purOrder/exportPurItems', "PurOrderApiController@exportPurItems");//导出采购明细
    Route::match(["get", "post"], '/purOrder/exportPurItemListByPurOrderList', "PurOrderApiController@exportPurItemListByPurOrderList");//导出采购单(明细)

    Route::match(["get", "post"], '/purOrder/getProduceCateList', "PurOrderApiController@getProduceCateList");//获取djk终端分类表
    Route::match(["get", "post"], '/purOrder/getEndCustInfoList', "PurOrderApiController@getEndCustInfoList");//获取djk终端公司列表
    Route::match(["get", "post"], '/purOrder/getEndCustInfoListByKeyword', "PurOrderApiController@getEndCustInfoListByKeyword");//获取djk终端公司列表
    Route::match(["get", "post"], '/purOrder/auditPurOrder', "PurOrderApiController@auditPurOrder");//采购采购反审核

    Route::match(["get", "post"], '/purOrder/purOrderItemList', "PurchaseItemApiController@purOrderItemList");//采购明细列表
    Route::match(["get", "post"], '/purOrder/getPurOrderItemListForPm', "PurchaseItemApiController@getPurOrderItemListForPm");//pm采购明细列表
    Route::match(["get", "post"], '/purOrder/updateErpInvoiceStatus', "PurOrderApiController@updateErpInvoiceStatus");//更新采购单发票状态

    Route::match(["get", "post"], '/purOrder/getFetchNumber', "PurOrderApiController@getFetchNumber");//新增时取号
    Route::match(["get", "post"], '/purOrder/getBatchFetchNumber', "PurOrderApiController@getBatchFetchNumber");//批量取号
    Route::match(["get", "post"], '/purOrder/getFetchNumberAndUpdate', "PurOrderApiController@getFetchNumberAndUpdate");//直接取号更新数据

    Route::match(["get", "post"], '/purOrder/getFileImportData', "PurOrderApiController@getFileImportData");//获取批量导入采购单数据
    Route::match(["get", "post"], '/purOrder/multiAddPurOrder', "PurOrderApiController@multiAddPurOrder");//批量新增采购单

    Route::match(["get", "post"], '/purOrder/getFileImportStockInData', "PurOrderApiController@getFileImportStockInData");//获取批量导入发货单
    Route::match(["get", "post"], '/purOrder/multiAddStockIn', "PurOrderApiController@multiAddStockIn");//批量新增发货单

    Route::match(["get", "post"], '/purOrder/syncSupplierData', "PurOrderApiController@syncSupplierData");//同步采购单供应商数据


    Route::match(["get", "post"], '/purOrder/getStatistics', "PurOrderApiController@getStatistics");//采购金额汇总
    Route::post('/purOrder/getHistoryQuoteStatistics', "PurOrderApiController@getHistoryQuoteStatistics");//获取历史报价统计

    Route::match(["get", "post"], '/dgkOrder/getNowDGKGoodsPrice', "DGKOrderApiController@getNowDGKGoodsPrice");//获取当前dgk商品价格
    Route::match(["get", "post"], '/dgkOrder/syncGoodsPrice', "DGKOrderApiController@syncGoodsPrice");//同步dgk商品价格
    Route::match(["get", "post"], '/dgkOrder/syncOrderCreate', "DGKOrderApiController@syncOrderCreate");//同步dgk订单
    Route::match(["get", "post"], '/dgkOrder/exportDgkOrder', "DGKOrderApiController@exportDgkOrder");//导出dgk订单

    Route::match(["get", "post"], '/purOrderItem/unbindSaleOrderItems', "PurchaseItemApiController@unbindSaleOrderItems");//解绑销售明细
    Route::match(["get", "post"], '/mroOrder/getNowMROGoodsPrice', "MROOrderApiController@getNowMROGoodsPrice");//获取当前mro成本价格
    Route::match(["get", "post"], '/mroOrder/syncOrderCreate', "MROOrderApiController@syncOrderCreate");//同步mro订单
    Route::match(["get", "post"], '/mroOrder/checkMROISCreatePur', "MROOrderApiController@checkMROISCreatePur");//验证需求是否可以生成采购单
    Route::match(["get", "post"], '/mroOrder/checkMROAddOrderForFrq', "MROOrderApiController@checkMROAddOrderForFrq");//验证需求是否可以生成采购单

    Route::match(["get", "post"], '/purOrder/importCoo', "PurOrderApiController@importCoo");//批量导入产地
    Route::match(["get", "post"], '/purOrder/submitImportCoo', "PurOrderApiController@submitImportCoo");//批量导入产地-提交
    Route::match(["get", "post"], '/purOrder/calculatePurOrderPrice', "PurOrderApiController@calculatePurOrderPrice");//新增采购单-计算明细价格
    Route::match(["get", "post"], '/purOrder/getSkuWeight', "PurOrderApiController@getSkuWeight");//采购单计算价格
    Route::match(["get", "post"], '/purOrder/getPurItemsByWarehouseReceiptSns', "PurOrderApiController@getPurItemsByWarehouseReceiptSns");//根据入仓单号获取采购明细及采购单信息
Route::match(["get", "post"], '/purOrder/getPurchaseOrderSkuList', "PurOrderApiController@getPurchaseOrderSkuList");//获取采购单SKU列表信息

    //附件接口
    Route::match(["get", "post"], '/uploadFile/addFile', 'UploadFileApiController@addFile');    // 新增附件
    Route::match(["get", "post"], '/uploadFile/deleteFile', 'UploadFileApiController@deleteFile');    // 删除附件

    //合同
    Route::match(["get", "post"], '/purContract/updateOrderContract', "PurContractApiController@updateOrderContract");//修改采购单合同信息
    Route::match(["get", "post"], '/purContract/invalidateOrderContract', "PurContractApiController@invalidateOrderContract");//失效采购合同
    Route::match(["get", "post"], '/purContract/addOrderContract', "PurContractApiController@addOrderContract");//创建采购合同
    Route::match(["get", "post"], '/purContract/submitContractForSign', "PurContractApiController@submitContractForSign");//提交采购合同

    //终端公司接口
    Route::match(["get", "post"], '/endCust/addEndCust', 'EndCustApiController@addEndCust');   //新增终端公司
    Route::match(["get", "post"], '/endCust/updateEndCust', 'EndCustApiController@updateEndCust');   //修改终端公司
    Route::match(["get", "post"], '/endCust/disableEndCust', 'EndCustApiController@disableEndCust');   //禁用终端公司
    Route::match(["get", "post"], '/endCust/deleteEndCust', 'EndCustApiController@deleteEndCust');   //删除终端公司
    Route::match(["get", "post"], '/endCust/endCustList', 'EndCustApiController@endCustList');   //获取终端公司列表
    Route::match(["get", "post"], '/endCust/getEndCustInfoByName', 'EndCustApiController@getEndCustInfoByName');   //获取单条终端公司数据
    Route::match(["get", "post"], '/endCust/getEndCustCateList', 'EndCustApiController@getEndCustCateList');   //获取单条终端公司数据
    Route::match(["get", "post"], '/endCust/getUserListByPrefixName', 'EndCustApiController@getUserListByPrefixName');   //根据用户名模糊匹配用户
    Route::match(["get", "post"], '/endCust/import', 'EndCustApiController@import');   //导入终端公司
    Route::match(["get", "post"], '/endCust/assignRuel', 'EndCustApiController@assignRuel');   //分配规则配置
    Route::match(["get", "post"], '/endCust/autoAssignTerminal', 'EndCustApiController@autoAssignTerminal');   //采购下单自动分配终端
    Route::match(["get", "post"], '/endCust/getAssignRuel', 'EndCustApiController@getAssignRuel');   //获取终端分配规则
    Route::match(["get", "post"], '/endCust/getTerminalAddOrderNums', 'EndCustApiController@getTerminalAddOrderNums');   //获取终端下单次数
    Route::match(["get", "post"], '/endCust/exportPurOrderCust', 'EndCustApiController@exportPurOrderCust');
    //导出采购单详情终端公司信息

    // 标准品牌接口
    Route::match(["get", "post"], '/standardBrand/getStandardBrand', 'StandardBrandApiController@getStandardBrand');    //搜索获取标准品牌

    //特批入库审核

    Route::match(["get", "post"], '/spestock_approve/getSpeApprovelist', 'SpestockApproceController@getSpeApprovelist');    //特批入库审核列表
    Route::match(["get", "post"], '/spestock_approve/getSuppliers', 'SpestockApproceController@getSuppliers');    //特批入库列表搜索供应商
    Route::match(["get", "post"], '/spestock_approve/getPurers', 'SpestockApproceController@getPurers');    //特批入库列表搜索采购员
    Route::match(["get", "post"], '/spestock_approve/audit', 'SpestockApproceController@audit');    //特批入库审核操作按钮


    //采购单更换供应商
    Route::match(["get", "post"], '/purSupChange/checkChangeFrqSupplier', 'PurchaseSupplierChangeApiController@checkChangeFrqSupplier');    //验证是否可以更换供应商
    Route::match(["get", "post"], '/purSupChange/list', 'PurchaseSupplierChangeApiController@list');    //获取变更日志

    //供应链委托单功能迁移
    Route::match(["get", "post"], '/gyl_scm_order/placeOfOriginTaxList', 'GYLScmOrderApiController@placeOfOriginTaxList');//产地税待确认列表
    Route::match(["get", "post"], '/gyl_scm_order/handleTax', 'GYLScmOrderApiController@handleTax');//处理产地税
    Route::match(["get", "post"], '/gyl_scm_order/getScmOrderList', 'GYLScmOrderApiController@getScmOrderList');//获取供应链委托单列表
    Route::match(["get", "post"], '/gyl_scm_order/getScmOrderDetail', 'GYLScmOrderApiController@getScmOrderDetail');//获取供应链委托单明细数据
    Route::match(["get", "post"], '/gyl_scm_order/deleteScmOrder', 'GYLScmOrderApiController@deleteScmOrder');//删除整单
    Route::match(["get", "post"], '/gyl_scm_order/createPurchaseOrder', 'GYLScmOrderApiController@createPurchaseOrder');//生成采购单
    Route::match(["get", "post"], '/gyl_scm_order/unAuditScmOrder', 'GYLScmOrderApiController@unAuditScmOrder');//反审核委托单
    Route::match(["get", "post"], '/gyl_scm_order/editScmOrderDetailAmount', 'GYLScmOrderApiController@editScmOrderDetailAmount');//编辑委托单明细金额
    Route::match(["get", "post"], '/gyl_scm_order/deleteScmOrderDetail', 'GYLScmOrderApiController@deleteScmOrderDetail');//删除委托单明细
    Route::match(["get", "post"], '/gyl_scm_order/getPurchaseOrderList', 'GYLScmOrderApiController@getPurchaseOrderList');//获取供应链采购单列表
    Route::match(["get", "post"], '/gyl_scm_order/getPurchaseOrderDetailList', 'GYLScmOrderApiController@getPurchaseOrderDetailList');//获取供应链采购单明细列表
    Route::match(["get", "post"], '/gyl_scm_order/getBaoGuanProgress', 'GYLScmOrderApiController@getBaoGuanProgress');//获取供应链报关进度
    Route::match(["get", "post"], '/gyl_scm_order/getBaoGuanShipmentLogs', 'GYLScmOrderApiController@getBaoGuanShipmentLog');//获取委托明细更新物流日志
    Route::match(["get", "post"], '/gyl_scm_order/deletePurchaseOrder', 'GYLScmOrderApiController@deletePurchaseOrder');//删除采购单接口
    Route::match(["get", "post"], '/gyl_scm_order/deletePurchaseOrderDetail', 'GYLScmOrderApiController@deletePurchaseOrderDetail');//删除采购单明细接口
    Route::match(["get", "post"], '/gyl_scm_order/editPurchaseOrderDetail', 'GYLScmOrderApiController@editPurchaseOrderDetail');//编辑采购单明细接口
    Route::match(["get","post"], '/gyl_scm_order/batchUpdateSupDvBasiInfo', "SupplierDeliveryApiController@batchUpdateScmShipingCoo");//填写物流
    Route::match(["get","post"], '/gyl_scm_order/getExportBatchEditShipmentScm', "SupplierDeliveryApiController@getExportBatchEditShipmentScm");//委托单获取导入的发货单物流信息
    Route::match(["get","post"], '/gyl_scm_order/ExportBatchEditShipmentScm', "SupplierDeliveryApiController@ExportBatchEditShipmentScm");//委托单批量导入更新发货单物流信息
    //清关
    Route::match(["get", "post"], '/clearance/list', 'ClearanceApiController@list');    //清关列表
    Route::match(["get", "post"], '/clearance/clearance', 'ClearanceApiController@clearance');    //清关
    Route::match(["get", "post"], '/clearance/exportClearance', 'ClearanceApiController@exportClearance');    //导出清关
    Route::match(["get", "post"], '/clearance/handleClearanceException', 'ClearanceApiController@handleClearanceException');    //异常处理
    Route::match(["get", "post"], '/clearance/unMergeClearance', 'ClearanceApiController@unMergeClearance');    //取消合并清关
    Route::match(["get", "post"], '/clearance/mergeClearance', 'ClearanceApiController@mergeClearance');    //合并清关
    Route::match(['get', 'post'], '/clearance/detailDialog', 'ClearanceApiController@detailDialog');   //清关详情弹窗

    //清关优化新接口
    Route::match(["get", "post"], '/clearanceOptimize/list', 'OptimizeClearanceApiController@list'); //清关优化列表
    Route::match(["get", "post"], '/clearanceOptimize/clearance', 'OptimizeClearanceApiController@clearance');    //清关优化清关
    Route::match(["get", "post"], '/clearanceOptimize/exportClearance', 'OptimizeClearanceApiController@exportClearance');    //清关优化导出清关
    Route::match(["get", "post"], '/clearanceOptimize/handleClearanceException', 'OptimizeClearanceApiController@handleClearanceException');    //清关优化异常处理
    Route::match(["get", "post"], '/clearanceOptimize/unMergeClearance', 'OptimizeClearanceApiController@unMergeClearance');    //清关优化取消合并清关
    Route::match(["get", "post"], '/clearanceOptimize/mergeClearance', 'OptimizeClearanceApiController@mergeClearance');    //清关优化合并清关
    Route::match(['get', 'post'], '/clearanceOptimize/detailDialog', 'OptimizeClearanceApiController@detailDialog');   //清关优化清关详情弹窗

    //获取一体化状态的接口
    Route::match(["get", "post"], '/endCust/getCompanyEntityListStatus', 'EndCustApiController@getCompanyEntityListStatus');    //获取一体化状态的接口

    //转仓单
    Route::match(["get", "post"], '/transferWarehouse/list', 'TransferWarehouseOrderApiController@list');    //获取转仓单列表
    Route::match(["get", "post"], '/transferWarehouse/itemList', 'TransferWarehouseOrderApiController@itemList');//获取转仓单明细列表
    Route::match(["get", "post"], '/transferWarehouse/add', 'TransferWarehouseOrderApiController@add');    //创建转仓单
    Route::match(["get", "post"], '/transferWarehouse/addOrderNew', 'TransferWarehouse\TransferOrderApiController@addOrderNew');    //创建转仓单(新统一接口)
    Route::match(["get", "post"], '/transferWarehouse/addForOldOrder', 'TransferWarehouseOrderApiController@addForOldOrder');    //创建旧单转转仓单
    Route::match(["get", "post"], '/transferWarehouse/getStockInItemList', 'TransferWarehouseOrderApiController@getStockInItemList');//获取入库明细的信息/包括转仓数量等
    Route::match(["get", "post"], '/transferWarehouse/updateTransferNum', 'TransferWarehouseOrderApiController@updateTransferNum');//更新转仓数量
    Route::match(["get", "post"], '/transferWarehouse/getSelfGoodsId', 'TransferWarehouseOrderApiController@getSelfGoodsId');//获取自营id(对接黄哥)
    Route::match(["get", "post"], '/transferWarehouse/syncWms', 'TransferWarehouseOrderApiController@syncWms');    //手动同步wms
    Route::match(["get", "post"], '/transferWarehouse/verify', 'TransferWarehouseOrderApiController@verify');    //审核
    Route::match(["get", "post"], '/transferWarehouse/getSkuList', 'TransferWarehouseOrderApiController@getSkuList');//获取自营商品列表
    Route::match(["get", "post"], '/transferWarehouse/cancel', 'TransferWarehouseOrderApiController@cancel');//作废转仓单
    Route::match(["get", "post"], '/transferWarehouse/getWmsStockList', 'TransferWarehouse\TransferOrderApiController@getWmsStockList');//获取wms库存数据
    Route::match(["get", "post"], '/transferWarehouse/getSkuInfoByErpStockId', 'TransferWarehouse\TransferOrderApiController@getSkuInfoByErpStockId');//根据erp库存id获取sku信息
    Route::match(["get", "post"], '/transferWarehouse/addScmOrderTemporary', 'TransferWarehouse\TransferOrderApiController@addScmOrderTemporary');//加入委托暂存


    //预检单
    Route::get('/precheckOrder/precheckItems', 'PrecheckOrderApiController@precheckItems');//预检单列表
    Route::get('/precheckOrder/exportPrecheckItems', 'PrecheckOrderApiController@exportPrecheckItems');//导出预检单列表
    Route::post('/precheckOrder/deliveryAct', 'PrecheckOrderApiController@deliveryAct');//预检单通知发货
    Route::post('/precheckOrder/updateFromShipment', 'PrecheckOrderApiController@updateFromShipment');//预检单-修改来货信息
    Route::post('/precheckOrder/cancelDeliveryAct', 'PrecheckOrderApiController@cancelDeliveryAct');//预检单取消通知发货
    Route::post('/precheckOrder/invalidPrecheckItems', 'PrecheckOrderApiController@invalidPrecheckItems');//预检单作废预检单
    Route::get('/precheckOrder/getZhiJianInfo', 'PrecheckOrderApiController@getZhiJianInfo');//预检单查看质检详情

    //异常处理
    Route::any('/abnormalHandle/list', 'AbnormalHandle\AbnormalHandleApiController@list');//异常处理列表
    Route::any('/abnormalHandle/handle', 'AbnormalHandle\AbnormalHandleApiController@handle');//异常处理
    Route::any('/abnormalHandle/update', 'AbnormalHandle\AbnormalHandleApiController@update');//异常处理更新
    Route::any('/abnormalHandle/getCommonWords', 'AbnormalHandle\AbnormalHandleApiController@getCommonWords');//获取常用词
    Route::any('/abnormalHandle/updateWords', 'AbnormalHandle\AbnormalHandleApiController@updateWords');//更新常用词

    //物流费用
    Route::any('/shippingFee/list', 'ShippingFee\ShippingFeeApiController@list');//物流费用列表
    Route::any('/shippingFee/manualAdd', 'ShippingFee\ShippingFeeApiController@manualAdd');//手工录入
    Route::any('/shippingFee/cancel', 'ShippingFee\ShippingFeeApiController@cancel');//作废物流费用
    Route::any('/shippingFee/getImportData', 'ShippingFee\ShippingFeeApiController@getImportData');//获取导入数据
    Route::any('/shippingFee/importFee', 'ShippingFee\ShippingFeeApiController@importFee');//匹配导入
    Route::any('/shippingFee/export', 'ShippingFee\ShippingFeeApiController@export');//导出物流费用
    Route::any('/shippingFeeDetail/list', 'ShippingFee\ShippingFeeDetailApiController@list');//物流费用明细列表
    Route::any('/shippingFeeDetail/submit', 'ShippingFee\ShippingFeeDetailApiController@submit');//提交物流费用明细
    Route::any('/shippingFeeDetail/confirm', 'ShippingFee\ShippingFeeDetailApiController@confirm');//确认费用明细
    Route::any('/shippingFeeDetail/counterConfirm', 'ShippingFee\ShippingFeeDetailApiController@counterConfirm');//反确认物流闽西
    Route::any('/shippingFeeDetail/update', 'ShippingFee\ShippingFeeDetailApiController@update');//更新物流明细
    Route::any('/shippingFeeDetail/updateFeeType', 'ShippingFee\ShippingFeeDetailApiController@updateFeeType');//更新物流费用类型
    Route::any('/shippingFeeDetail/export', 'ShippingFee\ShippingFeeDetailApiController@export');//导出物流费用明细

    //地区
    Route::any('/region/list', 'RegionApiController@list');//地区列表

    //第三方质检
    Route::any('/qualityInspection/list', 'QualityInspection\QualityInspectionApiController@list');//第三方质检列表
    Route::any('/qualityInspection/update', 'QualityInspection\QualityInspectionApiController@update');//更新质检数据
    Route::any('/qualityInspection/createDataByPurchaseItemIdArr', 'QualityInspection\QualityInspectionApiController@createDataByPurchaseItemIdArr');//根据采购单明细id创建之间数据
    Route::any('/qualityInspection/updateBackShipmentInfo', 'QualityInspection\QualityInspectionApiController@updateBackShipmentInfo');//更新寄回物流
    Route::any('/qualityInspection/confirmFee', 'QualityInspection\QualityInspectionApiController@confirmFee');//确认质检费用
    Route::any('/qualityInspection/counterConfirm', 'QualityInspection\QualityInspectionApiController@counterConfirm');//反确认质检
    Route::any('/qualityInspection/cancel', 'QualityInspection\QualityInspectionApiController@cancel');//作废质检
    Route::any('/qualityInspection/export', 'QualityInspection\QualityInspectionApiController@export');//导出质检列表
    Route::any('/qualityInspectionCenter/list', 'QualityInspection\QualityInspectionCenterApiController@list');//质检中心列表
    Route::any('/qualityInspectionCenter/createData', 'QualityInspection\QualityInspectionCenterApiController@createData');//创建质检中心数据
    Route::any('/qualityInspectionCenter/update', 'QualityInspection\QualityInspectionCenterApiController@update');//更新质检中心数据
    Route::any('/qualityInspectionCenter/updateStatus', 'QualityInspection\QualityInspectionCenterApiController@updateStatus');//更新质检中心启用状态

    //付款申请单对账
    Route::any('/paymentReconciliation/list', 'PaymentReconciliation\PaymentReconciliationApiController@list');//付款申请单对账任务列表
    Route::any('/paymentReconciliation/detailList', 'PaymentReconciliation\PaymentReconciliationApiController@detailList');//付款申请单对账详情列表
    Route::any('/paymentReconciliation/updateHandleTag', 'PaymentReconciliation\PaymentReconciliationApiController@updateHandleTag');//处理标记
    Route::any('/paymentReconciliation/import', 'PaymentReconciliation\PaymentReconciliationApiController@import');//导入付款申请单对账列表
    Route::any('/paymentReconciliation/export', 'PaymentReconciliation\PaymentReconciliationApiController@export');//导出付款申请单对账列表

    //供应商对账
    Route::any('/supplierReconciliation/list', 'SupplierReconciliation\SupplierReconciliationApiController@list');//供应商对账任务列表
    Route::any('/supplierReconciliation/getInfo', 'SupplierReconciliation\SupplierReconciliationApiController@getInfo');//供应商对账详情
    Route::any('/supplierReconciliation/detailList', 'SupplierReconciliation\SupplierReconciliationApiController@detailList');//供应商对账明细列表
    Route::any('/supplierReconciliation/deleteReconciliation', 'SupplierReconciliation\SupplierReconciliationApiController@deleteReconciliation');//删除对账
    Route::any('/supplierReconciliation/export', 'SupplierReconciliation\SupplierReconciliationApiController@export');//导出供应商对账列表
    Route::any('/supplierReconciliation/createByManual', 'SupplierReconciliation\SupplierReconciliationApiController@createByManual');//手动创建录入单
    Route::any('/supplierReconciliation/getWaitPaymentPayableItemIdList', 'SupplierReconciliation\SupplierReconciliationApiController@getWaitPaymentPayableItemIdList');//根据对账单id获取待付款应付单明细id
    Route::any('/supplierReconciliation/updateRemark', 'SupplierReconciliation\SupplierReconciliationApiController@updateRemark');//更新对账单备注

});

// 行为日志
Route::get('/actionlog/getLogs', 'ActionLogController@getLogs');    // 获取行为日志
Route::post('/goods/createGoods', "GoodsController@createGoods");      // 创建商品

//采购结算
Route::post('/payment/addPayment', 'PaymentController@addPayment');//添加付款申请单
Route::post('/payment/addRefund', 'PaymentController@addRefund');//添加退款申请单

//采购销售统计报表
Route::namespace('Api\PurchaseSalesStatistics')->group(function () {
    Route::match(["get", "post"], '/purchaseSalesStatistics/list', 'PurchaseSalesStatisticsApiController@list');//接单采购明细列表
    Route::match(["get", "post"], '/purchaseSalesStatistics/dailyReport', 'PurchaseSalesStatisticsApiController@dailyReport');//采购销售日报列表
    Route::match(["get", "post"], '/purchaseSalesStatistics/weekReport', 'PurchaseSalesStatisticsApiController@weekReport');//采购销售日报列表
    Route::match(["get", "post"], '/purchaseSalesStatistics/performanceLevelReport', 'PurchaseSalesStatisticsApiController@performanceLevelReport');//采购销售履约类型统计列表
    Route::match(["get", "post"], '/purchaseSalesStatistics/exportList', 'PurchaseSalesStatisticsApiController@exportList');//导出接单采购明细
});
Route::post('/payment/joinPayment', 'PaymentController@joinPayment');//合并付款申请单
Route::post('/payment/getCanjoinPaymentList', 'PaymentController@getCanjoinPaymentList');//获取可合并付款申请单列表
Route::post('/payment/updatePayment', 'PaymentController@updatePayment');//修改付款申请单
Route::get('/payment/updateUploadFiles', 'PaymentController@updateUploadFiles');//更新付款单附件
Route::post('/payment/getPaymentList', 'PaymentController@getPaymentList');//获取付款申请单列表
Route::post('/payment/getPaymentItems', 'PaymentController@getPaymentItems');//获取付款申请单明细列表
Route::post('/payment/addAudit', 'PaymentController@addAudit'); //付款申请单加入审核
Route::post('/payment/delPayments', 'PaymentController@delPayments'); //删除付款申请单
Route::post('/payment/closePayment', 'PaymentController@closePayment'); //作废付款申请单
Route::get('/payment/searchCreateName', 'PaymentController@searchCreateName'); //搜索用户名
Route::get('/payment/searchPurName', 'PaymentController@searchPurName'); //搜索采购用户名
Route::get('/payment/searchSupplierName', 'PaymentController@searchSupplierName'); //搜索供应商名
Route::get('/payment/checkPurCanMakePayment', 'PaymentController@checkPurCanMakePayment'); //检测采购单是否可生成付款申请单
Route::get('/payment/checkPurCanMakePaymentForPayable', 'PaymentController@checkPurCanMakePaymentForPayable');
//检测未付款的采购明细是否可生成付款申请单
Route::get('/payment/checkPayableCanMakePayment', 'PaymentController@checkPayableCanMakePayment'); //检测应付单是否可生成付款申请单
Route::get('/payment/checkPayableItemCanMakePayment', 'PaymentController@checkPayableItemCanMakePayment');//检测应付单明细是否可生成付款申请单
Route::get('/payment/checkPurCanMakeRefund', 'PaymentController@checkPurCanMakeRefund'); //检测采购单是否可生成退款申请单
Route::get('/payment/checkPayableCanMakeRefund', 'PaymentController@checkPayableCanMakeRefund'); //检测应付单是否可生成退款申请单
Route::match(["get", "post"], '/payment/syncSupplierData', 'PaymentController@syncSupplierData'); //同步付款申请单供应商数据
Route::match(["get", "post"], '/payment/syncContractFileId', 'PaymentController@syncContractFileId'); //同步付款申请单合同文件
Route::post('/payment/getApplyPaymentItems', 'PaymentController@getApplyPaymentItems');//获取申请付款单明细列表
Route::post('/payment/exportNotSynchItems', 'PaymentController@exportNotSynchItems');//导出对账单未同步数据
Route::post('/payment/updatePaymentRemark', 'PaymentController@updatePaymentRemark');//更新付款申请单备注
Route::post('/payment/manualSyncAuditLog', 'Api\Payment\PaymentApiController@manualSyncAuditLog');//手动同步日志
Route::post('/payment/manualSyncErp', 'Api\Payment\PaymentApiController@manualSyncErp');//手动同步金蝶
Route::post('/payment/manualSyncPayment', 'Api\Payment\PaymentApiController@manualSyncPayment');//手动同步付款单



Route::post('/payable/getPayableList', 'PayableController@getPayableList'); //获取应付单列表
Route::post('/payable/getPayableItemList', 'PayableController@getPayableItemList'); //获取应付单明细列表
Route::post('/payable/getNoPaymentPayableList', 'PayableController@getNoPaymentPayableList'); //获取未付款的采购明细应付单
Route::post('/payable/exportPayableItemList', 'PayableController@exportPayableItemList'); //导出应付单明细列表
Route::post('/payable/exportNoPaymentPayableList', 'PayableController@exportNoPaymentPayableList'); //导出未付款的采购明细应付单
Route::post('/payable/getPayableItems', 'PayableController@getPayableItems'); //获取应付单明细
Route::post('/payable/closePayable', 'PayableController@closePayable'); //关闭应付单
Route::get('/payable/searchSupplierName', 'PayableController@searchSupplierName'); //搜索供应商名
Route::get('/payable/searchPurName', 'PayableController@searchPurName'); //搜索采购用户名

// 审核中心列表
Route::get('/approve/getTransWHApproveList', 'ApproveController@getTransWHApproveList');    // 获取转仓单审核列表
Route::get('/approve/getTransWHApproveDetail', 'ApproveController@getTransWHApproveDetail');    // 获取转仓单审核详情
Route::post('/approve/getPurApproveList', 'ApproveController@getPurApproveList');    // 获取采购单审核列表
Route::post('/approve/getPaymentApproveList', 'ApproveController@getPaymentApproveList');    // 获取付款申请单审核列表
Route::post('/approve/getServiceApproveList', 'ApproveController@getServiceApproveList');    // 获取售后单审核列表
Route::post('/approve/editServicePrice', 'ApproveController@editServicePrice');    // 修改售后单价格
Route::post('/approve/getReturnApproveList', 'ApproveController@getReturnApproveList');    // 获取退货单审核列表
Route::post('/approve/getPlanApproveList', 'ApproveController@getPlanApproveList');    // 获取采购计划单审核列表
Route::get('/approve/searchServiceCreateName', 'ApproveController@searchServiceCreateName'); //搜索售后单创建用户名
Route::get('/approve/searchServiceBuyerName', 'ApproveController@searchServiceBuyerName'); //搜索售后单采购用户名
Route::get('/approve/searchServiceSellerName', 'ApproveController@searchServiceSellerName'); //搜索售后单销售用户名
Route::get('/approve/searchCreateName', 'ApproveController@searchCreateName'); //搜索创建用户名
Route::get('/approve/searchSupplierName', 'ApproveController@searchSupplierName'); //搜索供应商名
Route::match(["get", "post"], '/approve/getStatisticsInfo', "ApproveController@getStatisticsInfo"); // 获取统计信息
Route::post('/approve/getAllowApproveList', "ApproveController@getAllowApproveList"); // 允许展示审核列表
Route::match(["get", "post"], '/approve/getPurSaleUnbindList', 'ApproveController@getPurSaleUnbindList');    //采销解绑审核列表
Route::match([ "get","post"], '/approve/auditPurSaleUnbindList', 'ApproveController@auditPurSaleUnbindList');    //采销解绑审核操作
Route::match([ "get","post"], '/approve/transferWarehouseAudit', 'ApproveController@transferWarehouseAudit');    //转仓单审核操作


// 审核接口
Route::post('/approve/planAudit', 'ApproveController@planAudit');    // 采购计划单审核
Route::post('/approve/returnAudit', 'ApproveController@returnAudit');    // 退货单审核
Route::post('/approve/paymentAudit', 'ApproveController@paymentAudit');    // 付款申请单审核
Route::post('/approve/purAudit', 'ApproveController@purAudit');    // 采购单审核
Route::post('/approve/serviceAudit', 'ApproveController@serviceAudit');    // 售后单审核


