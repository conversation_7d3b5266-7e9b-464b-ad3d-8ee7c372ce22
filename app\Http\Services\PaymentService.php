<?php

namespace App\Http\Services;

use App\Enum\PurCommonEnum;
use App\Exceptions\InvalidRequestException;
use App\Http\IdSender\IdSender;
use App\Http\Models\PayableItemsModel;
use App\Http\Models\PaymentItemsModel;
use App\Http\Models\PaymentModel;
use App\Http\Models\PaymentReceivableModel;
use App\Http\Models\PurchaseContractModel;
use App\Http\Models\PurchaseItemsModel;
use App\Http\Models\PurchaseOrderModel;
use App\Http\Models\Qc\QcModel;
use App\Http\Models\StockInItemModel;
use App\Http\Models\SupplierChannelModel;
use App\Http\Models\SupplierReceiptModel;
use App\Http\Models\UploadFileModel;
use App\Http\Queue\RabbitQueueModel;
use App\Http\Services\Api\PurContractService;
use App\Http\Services\Api\PurOrderApiService;
use App\Http\Services\Qc\QcService;
use App\Http\Services\Sync\PurchaseOrderSyncService;
use App\Http\Utils\Currency;
use App\Http\Utils\FileUrl;
use App\Http\Utils\NameConvert;
use App\Http\Utils\SoapRequester;
use Carbon\Carbon;
use FontLib\Table\Type\post;
use Illuminate\Support\Facades\DB;

class PaymentService extends BaseService
{
    // erp 退采购款，退预付款的 type传值
    const ERP_REFUND_TYPE = 13;

    /**
     * 付款申请单的打印预览信息
     * @param $payment_id <p>付款申请单ID</p>
     * @param $payment_info <p>付款单信息</p>
     * @param $receivable_info <p>收款单位信息</p>
     * @return array
     * <AUTHOR>
     * @date 2022/3/30
     */
    public static function getPaymentPrintPreviewInfo($payment_id, $payment_info, &$receivable_info, $supplier_receipts)
    {
        $companyId = $payment_info['company_id'];
        // 金额：大写，取申请付款总金额
        $payment_amount_with_charges = bcadd($payment_info['payment_amount'], $payment_info['bank_charges'], 2);
        $payment_amount_with_charges = bcadd($payment_amount_with_charges, $payment_info['extra_fees'], 2);
        //越南盾
        if ($payment_info['currency'] == PurCommonEnum::CurrencyVND) {
            $rmb_upper_char_arr = PaymentService::rmbNumToUpperCharArr($payment_amount_with_charges, 0);
            // 申请单据上是 9位 值
            $rmb_upper_char_arr = array_pad($rmb_upper_char_arr, -11, ' ');
            unset($rmb_upper_char_arr[10]);
        } else {
            $rmb_upper_char_arr = PaymentService::rmbNumToUpperCharArr($payment_amount_with_charges);
            // 申请单据上是 9位 值
            $rmb_upper_char_arr = array_pad($rmb_upper_char_arr, -9, ' ');
        }
        // 付款原因
        $payment_reason = [
            'erp_payment_sn' => $payment_info['erp_payment_sn'],      //  金蝶付款申请单号：取金蝶付款申请单号
            'gross_profit'   => '',
            'seller_name'    => ''
        ];

        // 毛利
        $total_gross_profit_map = PaymentService::getTotalGrossProfitMap([$payment_info]);
        $gross_profit = isset($total_gross_profit_map[$payment_id]) ? $total_gross_profit_map[$payment_id] : 0;
        // 加上币别前缀
        $payment_reason['gross_profit'] = '总毛利：' . NameConvert::getFmtPrice($gross_profit, PriceService::CURRENCY_TYPE_RMB, '');
        $payment_reason['gross_profit_num'] = (string)number_format($gross_profit, 2, '.', ",");
        $payment_reason['gross_profit_currency'] = Currency::getCurrencySign(PurCommonEnum::$standardCurrency[$companyId] ?? PurCommonEnum::CurrencyRMB);
        $payment_reason['gross_profit_currency_base64'] = Currency::getCurrencySignImgBase64($payment_info['currency']);
        $payment_reason['gross_profit_currency_base64_1'] = Currency::getCurrencySignImgBase641(PurCommonEnum::$standardCurrency[$companyId] ?? PurCommonEnum::CurrencyRMB);


        // 付款申请单明细中多个的 purchase_id
        $purchase_ids = PaymentItemsModel::getPurchaseIdsByPaymentId($payment_id);

        // 销售员：显示关联销售订单的销售员名称
        $seller_name = PurchaseItemsService::getSellerNameByPurchaseId($purchase_ids[0]);
        if (!is_null($seller_name)) {
            $payment_reason['selleor_name'] = '销售：' . $seller_name;
        }

        // 如果账户名称为空，付款申请单里的收款单位展示为 开户名称
        if ($receivable_info) {
            if (empty($receivable_info['account_name'])) {
                foreach ($supplier_receipts as $supplier_receipt) {
                    if ($supplier_receipt['account_no'] == $receivable_info['bank_sn']) {
                        // $receivable_info['account_name'] = $supplier_receipt['account_name'];
                        $receivable_info['account_name'] = $supplier_receipt['bank_name'];
                        break;
                    }
                }
            }
        }

        // 打印预览数据
        return [
            'payment_info'                     => $payment_info,                                    // 币别,1:人民币，2：美金
            'payment_currency'                 => $payment_info['currency'],                        // 币别,1:人民币，2：美金
            'payment_amount_with_charges'      => $payment_amount_with_charges,                     // 总金额
            'bank_charges'                     => $payment_info['bank_charges'],                    // 手续费
            'payment_amount'                   => $payment_info['payment_amount'],                  // 申请付款金额
            'payment_amount_chars'             => $rmb_upper_char_arr,
            'receive_account_name'             => $receivable_info['account_name'] ?? "",
            'receive_account_bank_sn'          => $receivable_info['bank_sn'] ?? "",
            'receive_account_bank_branch_name' => $receivable_info['branch_name'] ?? "",
            'payment_reason'                   => $payment_reason,
            'payment_ticket_creator_name'      => '',    // 制单：读取采购员姓名
            'payment_ticket_create_year'       => Carbon::now()->year,
            'payment_ticket_create_month'      => Carbon::now()->month,
            'payment_ticket_create_day'        => Carbon::now()->day,
        ];
    }

    public static function getPrintPaymentList($paymentIdArr)
    {
        $paymentList = PaymentModel::getPaymentsByIds($paymentIdArr);
        $printList = [];
        foreach ($paymentList as $paymentInfo) {
            $paymentInfo['create_time'] = NameConvert::getDateTime($paymentInfo['create_time']);
            $paymentInfo['payment_amount'] = $paymentInfo['payment_amount_total'];
            $paymentInfo['payment_all_amount_total'] = NameConvert::getFmtPrice($paymentInfo['payment_amount_total'] + $paymentInfo['bank_charges'] + $paymentInfo['extra_fees'], $paymentInfo['currency']);
            $paymentInfo['payment_amount_total'] = NameConvert::getFmtPrice($paymentInfo['payment_amount_total'], $paymentInfo['currency']);
            $paymentInfo['currency_val'] = NameConvert::getCurrencyName($paymentInfo['currency']);
            $paymentId = $paymentInfo['payment_id'];
            $receivableInfo = PaymentReceivableModel::getReceivableByPaymentId($paymentId);
            $supplierReceipts = SupplierService::getSupplierReceipts($paymentInfo['supplier_id']);
            // 打印预览信息
            $paymentPrintPreviewInfo = PaymentService::getPaymentPrintPreviewInfo($paymentId, $paymentInfo, $receivableInfo, $supplierReceipts);
            $printList[] = $paymentPrintPreviewInfo;
            unset($receivableInfo);
        }
        return $printList;
    }

    /**
     * 将 人民币金额转换成 大写形式的字符数组
     * 例子： 输入：2022.13； 输出数组：[贰,零,贰,贰,壹,叄]
     * @param int $decimals 规定多少个小数，默认2位. 会四舍五入。
     * @param     $rmb
     * @return array $rmb_num_str
     * <AUTHOR>
     * @date 2022/3/29
     */
    public static function rmbNumToUpperCharArr($rmb, $decimals = 2)
    {
        $rmb_num_str = (string)number_format($rmb, $decimals, '', '');

        $rmb_num_arr = str_split($rmb_num_str);

        $rmb_upper_char_arr = [];
        foreach ($rmb_num_arr as $rmb_num) {
            $rmb_upper_char_arr[] = NameConvert::numToUpperChar($rmb_num);
        }

        return $rmb_upper_char_arr;
    }


    // 获取付款申请单map
    public static function getPaymentListMapByIds($payment_ids)
    {
        $payment_list_map = [];
        $payment_list = PaymentModel::getPaymentsByIds($payment_ids);
        if ($payment_list && is_array($payment_list)) {
            $payment_list_map = array_combine(array_column($payment_list, 'payment_id'), $payment_list);
        }
        return $payment_list_map;
    }

    // 获取付款申请单id with pur_pay_name map, 付款申请单关联采购单付款方式map
    public static function getPaymentIdWithPurPayNameMap($payment_list_map)
    {
        $payment_pur_pay_name_map = [];
        if ($payment_list_map) {
            $payment_ids = array_column($payment_list_map, 'payment_id');
            $payment_item_list = PaymentItemsModel::getListByPaymentIds($payment_ids);
            if ($payment_item_list) {
                $purchase_ids = array_column($payment_item_list, 'purchase_id');
                $purchase_map = PurOrderService::getPurchaseMap($purchase_ids);
                foreach ($payment_item_list as $payment_item) {
                    $payment_pur_pay_name_map[$payment_item['payment_id']] = isset($purchase_map[$payment_item['purchase_id']]) ? $purchase_map[$payment_item['purchase_id']]['pay_name'] : "";
                }
            }
        }
        return $payment_pur_pay_name_map;
    }

    // 获取付款申请单总毛利map, 付款申请单id=>付款申请单关联采购明细汇总毛利
    public static function getTotalGrossProfitMap($payment_list_map)
    {
        $total_gross_profit_map = [];
        $payment_ids = array_column($payment_list_map, 'payment_id');
        $payment_item_list = PaymentItemsModel::getListByPaymentIds($payment_ids);
        if ($payment_item_list) {
            $purchase_item_ids = array_column($payment_item_list, 'purchase_item_id');
            $purchase_item_map = PurOrderService::getPurchaseItemsMap($purchase_item_ids);
            foreach ($payment_item_list as $payment_item) {
                $gross_profit = isset($purchase_item_map[$payment_item['purchase_item_id']]) ? $purchase_item_map[$payment_item['purchase_item_id']]['gross_profit'] : 0;
                if (!isset($total_gross_profit_map[$payment_item['payment_id']])) {
                    $total_gross_profit_map[$payment_item['payment_id']] = 0;
                }
                $total_gross_profit_map[$payment_item['payment_id']] += $gross_profit;
            }
        }
        return $total_gross_profit_map;
    }

    // 根据采购单号获取采购单关联付款申请单id
    public static function getRelaPaymentIdsByPurchaseSn($purchase_sn)
    {
        $rela_payment_ids = [];
        if ($purchase_sn) {
            $purchase_info = PurchaseOrderModel::getPurOrderInfoBySn($purchase_sn);
            if ($purchase_info) {
                $payment_item_list = PaymentItemsModel::getItemsByPurchaseId($purchase_info['purchase_id']);
                if ($payment_item_list) {
                    $rela_payment_ids = array_column($payment_item_list, 'payment_id');
                    $rela_payment_ids = array_unique($rela_payment_ids);
                    $rela_payment_ids = array_values(array_filter($rela_payment_ids));
                }
            }
        }
        return $rela_payment_ids;
    }


    // 审核通过
    public static function auditPass($payment_id)
    {
        PaymentModel::updateById(['review_time' => time(), 'status' => PaymentModel::STATUS_WAIT_PAY], $payment_id);

        return true;
    }

    // 审核不通过,需要回滚状态为待提审
    public static function auditNotPass($payment_id)
    {
        PaymentModel::updateById(['status' => PaymentModel::STATUS_TO_AUDIT], $payment_id);
        return true;
    }

    // 采购单合同文件变更
    public static function updateContractFileIdByPurId($purchase_id)
    {
        // 采购单合同文件变更的时候，判断是否有关联付款申请单，如果有，那么需要更新合同文件到erp
        $has_pur_payment_items_list = PaymentItemsModel::getItemsByPurchaseId($purchase_id);
        if ($has_pur_payment_items_list) {
            $payment_ids = array_column($has_pur_payment_items_list, 'payment_id');
            $payment_list = PaymentModel::getPaymentsByIdsAndStatues($payment_ids, [PaymentModel::STATUS_WAIT_PAY, PaymentModel::STATUS_FINISH]);
            if ($payment_list) {
                $payment_id_and_payment_list_map = [];
                foreach ($has_pur_payment_items_list as $payment_item) {
                    $payment_id_and_payment_list_map[$payment_item['payment_id']][] = $payment_item;
                }

                $contractFileIdMap = PurContractService::getContractFileIdMapByPurIds([$purchase_id]);
                foreach ($payment_list as $payment_info) {
                    $payment_item_list = isset($payment_id_and_payment_list_map[$payment_info['payment_id']]) ? $payment_id_and_payment_list_map[$payment_info['payment_id']] : [];
                    if ($payment_info['payment_type'] == PaymentModel::PAYMENT_TYPE_PUR) {
                        $purchase_item_ids = array_column($payment_item_list, 'purchase_item_id');
                        $purchase_item_map = PurOrderService::getPurchaseItemsMap($purchase_item_ids);
                        foreach ($payment_item_list as $item_info) {
                            $ENTRYS[] = [
                                "ERPENTRYID" => (string)(isset($purchase_item_map[$item_info['purchase_item_id']])) ? $purchase_item_map[$item_info['purchase_item_id']]['erp_purchase_item_id'] : '',
                                "FILEID"     => $contractFileIdMap[$item_info['purchase_id']],
                            ];
                        }
                    } else {
                        $payable_item_ids = array_column($payment_item_list, 'payable_item_id');
                        $payable_item_map = PayableService::getPayableItemsMap($payable_item_ids);
                        foreach ($payment_item_list as $item_info) {
                            $ENTRYS[] = [
                                "ERPENTRYID" => (string)(isset($payable_item_map[$item_info['payable_item_id']])) ? $payable_item_map[$item_info['payable_item_id']]['erp_payable_item_id'] : '',
                                "FILEID"     => $contractFileIdMap[$item_info['purchase_id']],
                            ];
                        }
                    }
                }

                $queue_data = [
                    "TYPE"         => 2,
                    "ENTRYS"       => $ENTRYS,
                    "__search_key" => "updateContractFileIdByPurId_{$purchase_id}"
                ];

                $QueueModel = new RabbitQueueModel();
                $QueueModel->insertQueue("CallBackUpData", $queue_data, RabbitQueueModel::QUEUE_ERP, RabbitQueueModel::FORWARD_TYPE_SOAP);
            }
        }
        return true;
    }

    // 采购单合同文件变更
    public static function updateContractFileIdByPaymentIdArr($payment_ids)
    {
        $has_pur_payment_items_list = PaymentItemsModel::getListByPaymentIds($payment_ids);
        $payment_list = PaymentModel::getPaymentsByIdsAndStatues($payment_ids, [
            PaymentModel::STATUS_WAIT_PAY,
            PaymentModel::STATUS_FINISH,
            PaymentModel::STATUS_AUDIT,
        ]);
        if ($payment_list) {
            $ENTRYS = [];
            $payment_id_and_payment_list_map = [];
            foreach ($has_pur_payment_items_list as $payment_item) {
                $payment_id_and_payment_list_map[$payment_item['payment_id']][] = $payment_item;
            }

            $contractFileIdMap = PurContractService::getContractFileIdMapByPurIds(array_column($has_pur_payment_items_list, 'purchase_id'));
            foreach ($payment_list as $payment_info) {
                $payment_item_list = isset($payment_id_and_payment_list_map[$payment_info['payment_id']]) ? $payment_id_and_payment_list_map[$payment_info['payment_id']] : [];
                if ($payment_info['payment_type'] == PaymentModel::PAYMENT_TYPE_PUR) {
                    $purchase_item_ids = array_column($payment_item_list, 'purchase_item_id');
                    $purchase_item_map = PurOrderService::getPurchaseItemsMap($purchase_item_ids);
                    foreach ($payment_item_list as $item_info) {
                        $ENTRYS[] = [
                            "ERPENTRYID" => (string)(isset($purchase_item_map[$item_info['purchase_item_id']])) ? $purchase_item_map[$item_info['purchase_item_id']]['erp_purchase_item_id'] : '',
                            "FILEID"     => isset($contractFileIdMap[$item_info['purchase_id']]) ? $contractFileIdMap[$item_info['purchase_id']] : "",
                        ];
                    }
                } else {
                    $payable_item_ids = array_column($payment_item_list, 'payable_item_id');
                    $payable_item_map = PayableService::getPayableItemsMap($payable_item_ids);
                    foreach ($payment_item_list as $item_info) {
                        $ENTRYS[] = [
                            "ERPENTRYID" => (string)(isset($payable_item_map[$item_info['payable_item_id']])) ? $payable_item_map[$item_info['payable_item_id']]['erp_payable_item_id'] : '',
                            "FILEID"     => isset($contractFileIdMap[$item_info['purchase_id']]) ? $contractFileIdMap[$item_info['purchase_id']] : "",
                        ];
                    }
                }
            }

            $queue_data = [
                "TYPE"         => 2,
                "ENTRYS"       => $ENTRYS,
                "__search_key" => "updateContractFileIdByPaymentIds"
            ];

            $QueueModel = new RabbitQueueModel();
            $QueueModel->insertQueue("CallBackUpData", $queue_data, RabbitQueueModel::QUEUE_ERP, RabbitQueueModel::FORWARD_TYPE_SOAP);
        }
    }

    // 根据应付单明细ids或采购明细ids，和类型，获取付款中金额map， 明细id=>付款中金额
    public static function getInPayAmountMapByItemIds($item_ids, $payment_type = PaymentModel::PAYMENT_TYPE_PUR)
    {
        $in_pay_amount_map = [];
        // 查询明细列表，然后过滤付款申请单主单状态不对的，付款中=申请付款金额-已付款金额
        if ($payment_type == PaymentModel::PAYMENT_TYPE_PUR) {
            $payment_item_list = PaymentItemsModel::getItemsByPurchaseItemIds($item_ids);
        } else {
            $payment_item_list = PaymentItemsModel::getItemsByPayableItemIds($item_ids);
        }
        if ($payment_item_list && is_array($payment_item_list)) {
            $payment_ids = array_column($payment_item_list, 'payment_id');
            $payment_list = PaymentModel::getPaymentsByIdsAndStatues($payment_ids, [PaymentModel::STATUS_WAIT_PAY, PaymentModel::STATUS_FINISH]);
            if ($payment_list) {
                $available_payment_ids = array_column($payment_list, 'payment_id');
                foreach ($payment_item_list as $item_info) {
                    if (in_array($item_info['payment_id'], $available_payment_ids)) {
                        if ($payment_type == PaymentModel::PAYMENT_TYPE_PUR) {
                            $rela_item_id = $item_info['purchase_item_id'];
                        } else {
                            $rela_item_id = $item_info['payable_item_id'];
                        }
                        if (!isset($in_pay_amount_map[$rela_item_id])) {
                            $in_pay_amount_map[$rela_item_id] = 0;
                        }
                        $in_pay_amount_map[$rela_item_id] += ($item_info['payment_amount'] - $item_info['amount_paid']);
                    }
                }
            }
        }
        return $in_pay_amount_map;
    }

    // 根据应付单明细id，获取已付款金额map， 明细id=>已付款金额
    public static function getApplyPaidAmountMapByPayableIds($payable_ids)
    {
        $paid_amount_map = [];
        // 查询明细列表，然后过滤付款申请单主单状态不对的，付款中=申请付款金额-已付款金额
        $payment_item_list = PaymentItemsModel::getItemsByPayableIds($payable_ids);
        if ($payment_item_list && is_array($payment_item_list)) {
            $payment_ids = array_column($payment_item_list, 'payment_id');
            $payment_list = PaymentModel::getPaymentsByIdsAndStatues($payment_ids, [PaymentModel::STATUS_WAIT_PAY, PaymentModel::STATUS_FINISH]);
            if ($payment_list) {
                $available_payment_ids = array_column($payment_list, 'payment_id');
                foreach ($payment_item_list as $item_info) {
                    if (in_array($item_info['payment_id'], $available_payment_ids)) {
                        if (!isset($paid_amount_map[$item_info['payable_id']])) {
                            $paid_amount_map[$item_info['payable_id']] = 0;
                        }
                        $paid_amount_map[$item_info['payable_id']] += ($item_info['amount_paid']);
                    }
                }
            }
        }
        return $paid_amount_map;
    }

    // 根据应付单明细id，获取已付款金额map， 明细id=>已付款金额
    public static function getPaidAmountMapByPayableIds($payable_ids)
    {
        $paid_amount_map = [];
        // 查询明细列表，然后过滤付款申请单主单状态不对的，付款中=申请付款金额-已付款金额
        $payable_item_list = PayableItemsModel::getItemsByPayableIds($payable_ids);
        if ($payable_item_list && is_array($payable_item_list)) {
            foreach ($payable_item_list as $item_info) {
                if (!isset($paid_amount_map[$item_info['payable_id']])) {
                    $paid_amount_map[$item_info['payable_id']] = 0;
                }
                $paid_amount_map[$item_info['payable_id']] += ($item_info['amount_paid']);
            }
        }
        return $paid_amount_map;
    }

    // 根据应付单明细id，获取已付款金额map， 明细id=>已付款金额
    public static function getPaidAmountMapByItemIds($item_ids, $payment_type = PaymentModel::PAYMENT_TYPE_PUR)
    {
        $paid_amount_map = [];
        // 查询明细列表，然后过滤付款申请单主单状态不对的，付款中=申请付款金额-已付款金额
        if ($payment_type == PaymentModel::PAYMENT_TYPE_PUR) {
            $payment_item_list = PaymentItemsModel::getItemsByPurchaseItemIdsAndPaymentTypes($item_ids, [
                PaymentItemsModel::PAY_TYPE_PRE_PAYMENT,
                PaymentItemsModel::PAY_TYPE_UN_PRE_PAYMENT,
                PaymentItemsModel::PAY_TYPE_PUR_PAYMENT,
            ]);
        } else {
            $payment_item_list = PaymentItemsModel::getItemsByPayableItemIds($item_ids);
        }
        if ($payment_item_list && is_array($payment_item_list)) {
            $payment_ids = array_column($payment_item_list, 'payment_id');
            $payment_list = PaymentModel::getPaymentsByIdsAndStatues($payment_ids, [PaymentModel::STATUS_WAIT_PAY, PaymentModel::STATUS_FINISH]);
            if ($payment_list) {
                $available_payment_ids = array_column($payment_list, 'payment_id');
                foreach ($payment_item_list as $item_info) {
                    if (in_array($item_info['payment_id'], $available_payment_ids)) {
                        if ($payment_type == PaymentModel::PAYMENT_TYPE_PUR) {
                            $rela_item_id = $item_info['purchase_item_id'];
                        } else {
                            $rela_item_id = $item_info['payable_item_id'];
                        }
                        if (!isset($paid_amount_map[$rela_item_id])) {
                            $paid_amount_map[$rela_item_id] = 0;
                        }
                        $paid_amount_map[$rela_item_id] += ($item_info['amount_paid']);
                    }
                }
            }
        }
        return $paid_amount_map;
    }

    // 根据应付单明细id，获取申请付款金额map， 明细id=>申请付款
    public static function getApplyAmountMapByPayableIds($payable_ids)
    {
        $apply_amount_map = [];
        // 查询明细列表，然后过滤付款申请单主单状态不对的，付款中=申请付款金额-已付款金额
        $payment_item_list = PaymentItemsModel::getItemsByPayableIds($payable_ids);
        if ($payment_item_list && is_array($payment_item_list)) {
            $payment_ids = array_column($payment_item_list, 'payment_id');
            $payment_list = PaymentModel::getPaymentsByIdsAndStatues($payment_ids, [
                    PaymentModel::STATUS_TO_AUDIT,
                    PaymentModel::STATUS_AUDIT,
                    PaymentModel::STATUS_WAIT_PAY,
                    PaymentModel::STATUS_FINISH
                ]);
            if ($payment_list) {
                $available_payment_ids = array_column($payment_list, 'payment_id');
                foreach ($payment_item_list as $item_info) {
                    if (in_array($item_info['payment_id'], $available_payment_ids)) {
                        if (!isset($apply_amount_map[$item_info['payable_id']])) {
                            $apply_amount_map[$item_info['payable_id']] = 0;
                        }
                        $apply_amount_map[$item_info['payable_id']] += ($item_info['payment_amount']);
                    }
                }
            }
        }
        return $apply_amount_map;
    }

    // 根据应付单明细id，获取申请付款金额map， 明细id=>申请付款
    public static function getApplyAmountMapByItemIds($item_ids, $payment_type = PaymentModel::PAYMENT_TYPE_PUR)
    {
        $apply_amount_map = [];
        // 查询明细列表，然后过滤付款申请单主单状态不对的，付款中=申请付款金额-已付款金额
        if ($payment_type == PaymentModel::PAYMENT_TYPE_PUR) {
            $payment_item_list = PaymentItemsModel::getItemsByPurchaseItemIdsAndPaymentTypes($item_ids, [
                PaymentItemsModel::PAY_TYPE_PRE_PAYMENT,
                PaymentItemsModel::PAY_TYPE_UN_PRE_PAYMENT,
                PaymentItemsModel::PAY_TYPE_PUR_PAYMENT,
            ]);
        } else {
            $payment_item_list = PaymentItemsModel::getItemsByPayableItemIds($item_ids);
        }
        if ($payment_item_list && is_array($payment_item_list)) {
            $payment_ids = array_column($payment_item_list, 'payment_id');
            $payment_list = PaymentModel::getPaymentsByIdsAndStatues($payment_ids, [
                    PaymentModel::STATUS_TO_AUDIT,
                    PaymentModel::STATUS_AUDIT,
                    PaymentModel::STATUS_WAIT_PAY,
                    PaymentModel::STATUS_FINISH
                ]);
            if ($payment_list) {
                $available_payment_ids = array_column($payment_list, 'payment_id');
                foreach ($payment_item_list as $item_info) {
                    if (in_array($item_info['payment_id'], $available_payment_ids)) {
                        if ($payment_type == PaymentModel::PAYMENT_TYPE_PUR) {
                            $rela_item_id = $item_info['purchase_item_id'];
                        } else {
                            $rela_item_id = $item_info['payable_item_id'];
                        }
                        if (!isset($apply_amount_map[$rela_item_id])) {
                            $apply_amount_map[$rela_item_id] = 0;
                        }
                        $apply_amount_map[$rela_item_id] += ($item_info['payment_amount']);
                    }
                }
            }
        }
        return $apply_amount_map;
    }

    // 根据采购单id，获取申请付款金额map， 采购单id=>申请付款
    public static function getApplyAmountMapByPurIds($purchase_ids)
    {
        $apply_amount_map = [];
        // 查询明细列表，然后过滤付款申请单主单状态不对的，付款中=申请付款金额-已付款金额
        $payment_item_list = PaymentItemsModel::getItemsByPurchaseIds($purchase_ids);
        if ($payment_item_list && is_array($payment_item_list)) {
            $payment_ids = array_column($payment_item_list, 'payment_id');
            $payment_list = PaymentModel::getPaymentsByIdsAndStatues($payment_ids, [
                    PaymentModel::STATUS_TO_AUDIT,
                    PaymentModel::STATUS_AUDIT,
                    PaymentModel::STATUS_WAIT_PAY,
                    PaymentModel::STATUS_FINISH
                ]);
            if ($payment_list) {
                $available_payment_ids = array_column($payment_list, 'payment_id');
                foreach ($payment_item_list as $item_info) {
                    if (in_array($item_info['payment_id'], $available_payment_ids)) {
                        if (!isset($apply_amount_map[$item_info['purchase_id']])) {
                            $apply_amount_map[$item_info['purchase_id']] = 0;
                        }
                        $apply_amount_map[$item_info['purchase_id']] += ($item_info['payment_amount']);
                    }
                }
            }
        }
        return $apply_amount_map;
    }

    // 根据付款申请单id，获取已付款金额
    public static function getPaidAmountByPaymentId($payment_id)
    {
        $paid_amount = 0;
        $payment_item_list = PaymentItemsModel::getListByPaymentId($payment_id);
        if ($payment_item_list && is_array($payment_item_list)) {
            foreach ($payment_item_list as $item_info) {
                $paid_amount += ($item_info['amount_paid']);
            }
        }
        return $paid_amount;
    }

    /**
     * checkUploadFile
     * 验证上传附件
     * @param $companyId
     * @param $fileIds
     * <AUTHOR>
     * Time: 5:42 下午
     */
    public static function checkUploadFile($companyId, $fileIds)
    {
        //猎芯不需要验证
        if ($companyId == PurchaseOrderModel::LIEXINKEJI) {
            return false;
        }
        if (empty($fileIds)) {
            throw new \InvalidArgumentException("采购组织为深贸电子，请必须上传 Profromal Inovice");
        }
        //必须要有附件,并且附件类型要是 FILE_TYPE_PROFROMAL_INOVICE
        $fileList = UploadFileModel::getListInIds($fileIds);
        if (empty($fileList)) {
            throw new \InvalidArgumentException("采购组织为深贸电子，请必须上传 Profromal Inovice");
        }
        foreach ($fileList as $fileInfo) {
            if ($fileInfo['file_type'] != UploadFileModel::FILE_TYPE_PROFROMAL_INOVICE['id']) {
                throw new \InvalidArgumentException("附件类型必须为 Profromal Inovice");
            }
        }

        return true;
    }

    public static function getPaymentUploadFileList($fileIds)
    {
        $fileIds = explode(",", $fileIds);
        if (empty($fileIds)) {
            return [];
        }
        $list = UploadFileModel::getListInIds($fileIds);
        return $list;
    }


    /**
     * 验证采购单有效的合同是否存在
     * checkValidContract
     * @param $purIds
     * <AUTHOR>
     * Time: 9:21 上午
     */
    public static function checkPurListValidContract($purIds,$returnMsg=false)
    {
        $purList = PurchaseOrderModel::getPurOrderListByIdArr($purIds);
        //获取代购供应商编码
        $supplierSn = config("config.DaiGouSUP");
        //过滤代购供应商
        foreach ($purList as $key => $purInfo) {
            if (in_array(strtolower($purInfo['supplier_sn']), $supplierSn)) {
                unset($purList[$key]);
            }
        }
        //初始化返回值 默认都是通过
        $returnArr = [];
        foreach($purIds as $purIdItem){
            $returnArr[$purIdItem]  = true;
        }


        if (empty($purList)) {
            if($returnMsg){
                return $returnArr;
            }else{
                return true;
            }
        }
        $purIds = array_column($purList, 'purchase_id');
        //获取有效的中文合同,如果没有代表缺少
        $list = PurchaseContractModel::getValidContractListByPurchaseIds($purIds);
        //取数组差集
        $noContractIds = array_diff($purIds, array_column($list, 'purchase_id'));
        if (!empty($noContractIds)) {
            $purSn = [];
            foreach ($purList as $purInfo) {
                if (in_array($purInfo['purchase_id'], $noContractIds)) {
                    $purSn[] = $purInfo['purchase_sn'];
                    if($returnMsg){
                        $returnArr[$purInfo['purchase_id']]  = false;
                    }
                }
            }
            if(!$returnMsg){
                throw new \Exception("合同单号:" . implode(",", $purSn) . "不存在有效的合同记录");
            }

        }
        $checkTemp = [];
        //判断是否有上传供应商合同
        foreach ($list as $contractInfo) {
            if (!empty($contractInfo['oss_file_id'])) {
                $checkTemp[$contractInfo['purchase_id']] = true;
            } else {
                if (!isset($checkTemp[$contractInfo['purchase_id']])) {
                    $checkTemp[$contractInfo['purchase_id']] = false;
                    if($returnMsg){
                        $returnArr[$contractInfo['purchase_id']]  = false;
                    }
                }
            }
        }
        foreach ($checkTemp as $purchaseSn => $result) {
            if ($result != true && !$returnMsg) {
                throw new \InvalidArgumentException("操作单据关联的采购单没有上传有效的供应商合同");
            }
        }

        if($returnMsg){
            return $returnArr;
        }
        return true;
    }

    /**
     * 验证采购单有效的合同是否存在
     * checkValidContract
     * @param $purIds
     * <AUTHOR>
     * Time: 9:21 上午
     */
    public static function checkPurListValidContractByPurchaseList($purList)
    {
        //获取代购供应商编码
        $supplierSn = config("config.DaiGouSUP");
        //过滤代购供应商
        foreach ($purList as $key => $purInfo) {
            if (in_array(strtolower($purInfo['supplier_sn']), $supplierSn)) {
                unset($purList[$key]);
            }
        }
        if (empty($purList)) {
            return true;
        }
        $purIds = array_column($purList, 'purchase_id');
        //获取有效的中文合同,如果没有代表缺少
        $list = PurchaseContractModel::getValidContractListByPurchaseIds($purIds);
        //取数组差集
        $noContractIds = array_diff($purIds, array_column($list, 'purchase_id'));
        if (!empty($noContractIds)) {
            $purSn = [];
            foreach ($purList as $purInfo) {
                if (in_array($purInfo['purchase_id'], $noContractIds)) {
                    $purSn[] = $purInfo['purchase_sn'];
                }
            }
            throw new \Exception("合同单号:" . implode(",", $purSn) . "不存在有效的合同记录");
        }
        $checkTemp = [];
        //判断是否有上传供应商合同
        foreach ($list as $contractInfo) {
            if (!empty($contractInfo['oss_file_id'])) {
                $checkTemp[$contractInfo['purchase_id']] = true;
            } else {
                if (!isset($checkTemp[$contractInfo['purchase_id']])) {
                    $checkTemp[$contractInfo['purchase_id']] = false;
                }
            }
        }
        foreach ($checkTemp as $purchaseSn => $result) {
            if ($result != true) {
                throw new \InvalidArgumentException("付款单关联的采购单没有上传有效的供应商合同");
            }
        }

        return true;
    }

    /**
     * 验证应付单有效的合同是否存在
     * checkValidContract
     * @param $supplierId
     * @param $itemList
     * <AUTHOR>
     * Time: 9:21 上午
     */
    public static function checkPayableItemListValidContract($supplierId, $itemList)
    {
        $supplierInfo = SupplierChannelModel::getSupplierBySupplierId($supplierId);
        $supplierSn = config("config.DaiGouSUP");
        if (in_array(strtolower($supplierInfo['supplier_code']), $supplierSn)) {
            return true;
        }
        $purIds = array_column($itemList, 'purchase_id');
        //去重purIds
        $purIds = array_unique($purIds);
        //获取有效的中文合同,如果没有代表缺少
        $list = PurchaseContractModel::getValidContractListByPurchaseIds($purIds);
        //取数组差集
        $noContractIds = array_diff($purIds, array_column($list, 'purchase_id'));
        if (!empty($noContractIds)) {
            throw new \InvalidArgumentException("应付单关联的采购单不存在有效的合同记录");
        }
        $checkTemp = [];
        //判断是否有上传供应商合同
        foreach ($list as $contractInfo) {
            if (!empty($contractInfo['oss_file_id'])) {
                $checkTemp[$contractInfo['purchase_id']] = true;
            } else {
                if (!isset($checkTemp[$contractInfo['purchase_id']])) {
                    $checkTemp[$contractInfo['purchase_id']] = false;
                }
            }
        }
        foreach ($checkTemp as $purchaseSn => $result) {
            if ($result != true) {
                throw new \InvalidArgumentException("应付单关联的采购单没有上传有效的供应商合同");
            }
        }
        return true;
    }

    public static function syncSupplierData($paymentId)
    {
        $paymentInfo = PaymentModel::getPaymentById($paymentId);
        if (empty($paymentInfo)) {
            throw new \Exception("付款单不存在");
        }
        $supplierId = $paymentInfo['supplier_id'];
        $supplierInfo = SupplierChannelModel::getSupplierBySupplierId($supplierId);
        if (empty($supplierInfo)) {
            throw new \Exception("供应商已经不存在");
        }
        if ($supplierInfo['status'] != SupplierChannelModel::STATUS_ENABLE) {
            throw new \InvalidArgumentException("供应商信息没有审核通过");
        }
        if (!in_array($paymentInfo['status'], [PaymentModel::STATUS_WAIT_PAY, PaymentModel::STATUS_TO_AUDIT])) {
            throw new \InvalidArgumentException("付款单状态不是待付款或者待提审");
        }
        if (!PaymentService::checkKingdeeHasPayment($paymentInfo['erp_payment_sn'])) {
            throw new \InvalidArgumentException("该付款申请已经生成生成下游付款单");
        }

        try {
            DB::connection("mysql")->beginTransaction();
            //更新付款申请单供应商信息
            $paymentUpdateData = [
                'supplier_name' => $supplierInfo['supplier_name'],
                'supplier_id'   => $supplierInfo['supplier_id'],
            ];
            PaymentModel::updateById($paymentUpdateData, $paymentId);
            //更新付款申请单银行收款信息
            self::updatePaymentReceivable($paymentId, $paymentInfo['receipt_id']);
            //记录日志
            PaymentService::syncKingdeeReceipt($paymentInfo['erp_payment_sn'], $paymentInfo['receipt_id'], $supplierInfo['supplier_name']);

            ActionLogService::addLog(ActionLogService::TYPE_ACTION_PAYMENT_UPDATE, $paymentId, ["message" => "同步了该付款申请单供应商信息"]);
            DB::connection("mysql")->commit();
        } catch (\Exception $e) {
            DB::connection("mysql")->rollBack();
            throw new \Exception($e->getMessage());
        }
    }


    // 更新供应商收款信息
    public static function updatePaymentReceivable($payment_id, $receipt_id)
    {
        $receipt_info = SupplierReceiptModel::getReceiptById($receipt_id);
        if (empty($receipt_info)) {
            throw new \Exception("供应商收款信息查询失败");
        }

        $receivable_info = PaymentReceivableModel::getReceivableByPaymentId($payment_id);
        $new_receivable_info = [
            "payment_id"           => $payment_id,
            "bank_name"            => $receipt_info['bank_name'],
            "branch_name"          => $receipt_info['bank_adderss'],
            "bank_sn"              => $receipt_info['account_no'],
            "account_name"         => $receipt_info['bank_name'],       // 供应商系统以bank_name作为开户名称
            "wire_transfer_number" => $receipt_info['swift_code'],
            "credential_info"      => $receipt_info['certificate'],
            "remark"               => $receipt_info['remark'],
        ];

        if (empty($receivable_info)) {
            PaymentReceivableModel::addReceivable($new_receivable_info);
        } else {
            PaymentReceivableModel::updateById($new_receivable_info, $receivable_info['receivable_id']);
        }
        // 如果账户名不相同，说明在修改
        if ($receivable_info && ($receivable_info['account_name'] != $new_receivable_info['account_name'])) {
            // 添加修改日志
            ActionLogService::addLog(ActionLogService::TYPE_ACTION_PAYMENT_UPDATE, $payment_id, ["message" => "开户名称由 {$receivable_info['account_name']} 修改为 {$new_receivable_info['account_name']}"]);
        }
        return true;
    }

    // 根据采购ids，获取可退款的申请单明细列表; 付款申请单状态为完成的， 同时未申请退款的付款申请单明细
    public static function getCanRefundItemsByPurIds($pur_ids)
    {
        $can_refund_item_list = [];

        // 获取所有预付款的付款明细，只选择付款申请单已完成的。
        $payment_item_list = PaymentItemsModel::getItemsByPurchaseIdsAndPaymentTypeWithPurItem($pur_ids, PaymentItemsModel::PAY_TYPE_PRE_PAYMENT);
        if ($payment_item_list) {
            $payment_ids = array_column($payment_item_list, 'payment_id');
            $payment_list = PaymentModel::getPaymentsByIdsAndStatues($payment_ids, [PaymentModel::STATUS_FINISH]);
            $finish_payment_ids = [];
            if ($payment_list) {
                $finish_payment_ids = array_column($payment_list, 'payment_id');
            }

            // 如果没有状态为完成的付款申请单，那么不能退款
            if (empty($finish_payment_ids)) {
                return $can_refund_item_list;
            }
            $erp_related_ids = array_column($payment_item_list, 'related_id');
            $apply_refund_erp_ids = self::getApplyRefundErpIds($erp_related_ids, PaymentItemsModel::PAY_TYPE_UN_PRE_PAYMENT);
            foreach ($payment_item_list as $payment_item) {
                // 如果当前明细已申请退款，那么就不能在退款了，跳过
                if (in_array($payment_item['related_id'], $apply_refund_erp_ids)) {
                    continue;
                }

                // 如果当前明细的付款申请单状态为完成，才能申请退款
                if (in_array($payment_item['payment_id'], $finish_payment_ids)) {
                    if (bccomp($payment_item['payment_amount'], $payment_item['amount_paid'], 2) == 0) {
                        $can_refund_item_list[] = $payment_item;
                    }
                }
            }
        }
        return $can_refund_item_list;
    }

    // 根据应付单ids，获取可退款的申请单明细列表; 付款申请单状态为完成的， 同时未申请退款的付款申请单明细
    public static function getCanRefundItemsByPayableIds($payable_ids)
    {
        $can_refund_item_list = [];

        // 获取所有采购付款的付款明细，只选择付款申请单已完成的。
        $payment_item_list = PaymentItemsModel::getItemsByPayableIdsAndPaymentType($payable_ids, PaymentItemsModel::PAY_TYPE_PUR_PAYMENT);
        if ($payment_item_list) {
            $payment_ids = array_column($payment_item_list, 'payment_id');
            $payment_list = PaymentModel::getPaymentsByIdsAndStatues($payment_ids, [PaymentModel::STATUS_FINISH]);
            $finish_payment_ids = [];
            if ($payment_list) {
                $finish_payment_ids = array_column($payment_list, 'payment_id');
            }

            // 如果没有状态为完成的付款申请单，那么不能退款
            if (empty($finish_payment_ids)) {
                return $can_refund_item_list;
            }

            $erp_related_ids = array_column($payment_item_list, 'related_id');
            $apply_refund_erp_ids = self::getApplyRefundErpIds($erp_related_ids, PaymentItemsModel::PAY_TYPE_UN_PUR_PAYMENT);
            foreach ($payment_item_list as $payment_item) {
                // 如果当前明细已申请退款，那么就不能在退款了，跳过
                if (in_array($payment_item['related_id'], $apply_refund_erp_ids)) {
                    continue;
                }

                // 如果当前明细的付款申请单状态为完成，才能申请退款
                if (in_array($payment_item['payment_id'], $finish_payment_ids)) {
                    if (bccomp($payment_item['payment_amount'], $payment_item['amount_paid'], 2) == 0) {
                        $can_refund_item_list[] = $payment_item;
                    }
                }
            }
        }
        return $can_refund_item_list;
    }

    public static function getPaymentMapByPurchaseItemIdArr($purItemIdArr)
    {
        $payment_list = PaymentItemsModel::getPaymentsByPurItemIdsAndStatues($purItemIdArr, [
            PaymentModel::STATUS_TO_AUDIT,
            PaymentModel::STATUS_AUDIT,
            PaymentModel::STATUS_WAIT_PAY,
        ]);
        $paymentMap = array_column($payment_list, null, 'purchase_item_id');
        return $paymentMap;
    }

    // 根据erp关联ids，获取已申请退款的erp关联ids
    public static function getApplyRefundErpIds(
        $erp_related_ids, $payment_type = PaymentItemsModel::PAY_TYPE_UN_PRE_PAYMENT
    ) {
        $apply_refund_erp_ids = [];
        // 获取所有预付款的付款明细，只选择付款申请单已完成的。
        $payment_item_list = PaymentItemsModel::getItemByErpIdsAndPaymentType($erp_related_ids, $payment_type);
        if ($payment_item_list) {
            $payment_ids = array_column($payment_item_list, 'payment_id');
            $payment_list = PaymentModel::getPaymentsByIdsAndStatues($payment_ids, [
                PaymentModel::STATUS_TO_AUDIT,
                PaymentModel::STATUS_AUDIT,
                PaymentModel::STATUS_WAIT_PAY,
                PaymentModel::STATUS_FINISH
            ]);
            if (empty($payment_list)) {
                return $apply_refund_erp_ids;
            }
            $applied_payment_list_map = array_column($payment_list, null, 'payment_id');

            foreach ($payment_item_list as $payment_item) {
                //原有逻辑:只要是存在付款申请单，就不能再次发起退款
                //20230404 修改,因为要支持多次退款,所以肯定会存在多个退款申请,但是退款申请应该为已完成的,才能再次发起退款
                //所以,如果不存在付款申请单,则允许退款
                //如果存在并且状态为已完成,并且关联的付款申请单  退款金额小于支付金额,则允许退款
                //否则不允许
                if (!isset($applied_payment_list_map[$payment_item['payment_id']])) {
                    continue;
                }
                if ($applied_payment_list_map[$payment_item['payment_id']]['status'] == PaymentModel::STATUS_FINISH) {
                    continue;
                }

                $apply_refund_erp_ids[] = $payment_item['related_id'];
            }
        }
        return $apply_refund_erp_ids;
    }

    // 获取付款申请单 map  采购id=>采购信息
    public static function getPaymentMap($payment_ids)
    {
        $payment_map = [];
        $payment_list = PaymentModel::getPaymentsByIds($payment_ids);
        if ($payment_list && is_array($payment_list)) {
            $payment_map = array_combine(array_column($payment_list, 'payment_id'), $payment_list);
        }
        return $payment_map;
    }

    // 获取付款申请单明细列表map  明细id=>明细信息
    public static function getPaymentItemsMap($payment_items_ids)
    {
        $payment_items_map = [];
        $item_list = PaymentItemsModel::getItemsByIdsWithPurItem($payment_items_ids);
        if ($item_list && is_array($item_list)) {
            $payment_items_map = array_combine(array_column($item_list, 'payment_item_id'), $item_list);
        }
        return $payment_items_map;
    }

    public static function isFrqHY($paymentId)
    {
        $itemList = PaymentItemsModel::getItemsByPaymentIdWithPurItem($paymentId);
        $result = true;
        foreach ($itemList as $item) {
            if (!empty($item['purchase_order']['purchase_type']) && $item['purchase_order']['purchase_type'] != PurchaseOrderModel::PURCHASE_TYPE_HUAYUN) {
                $result = false;
            }
        }
        return $result;
    }

    public static function getPaymentCompanyId($paymentId)
    {
        $itemList = PaymentItemsModel::getItemsByPaymentIdWithPurItem($paymentId);
        $companyId = 0;
        foreach ($itemList as $item) {
            $companyId = $item['purchase_order']['company_id'];
            break;
        }
        return $companyId;
    }

    //已完成的付款申请单作废 临时处理线上问题用
    public static function invalidPayment($payment_ids)
    {
        $purchaseOrderSyncService = new PurchaseOrderSyncService();
        (new self())->startTransaction();
        $allPurIds = [];
        foreach ($payment_ids as $payment_id) {
            //作废付款申请单
            PaymentModel::invalidPayment($payment_id);
            //获取付款申请单的关联采购id
            $purIds = PaymentItemsModel::getPurchaseIdByPaymentId($payment_id);
            if (!empty($purIds)) {
                $allPurIds = array_merge($allPurIds, $purIds);
            }
        }

        $allPurIds = array_filter_unique($allPurIds);
        if (!empty($allPurIds)) {
            $purchaseOrderSyncService->checkPayStatusCausePay($allPurIds);
        }

        (new self())->commitTransaction();
    }

    public static function checkKingdeeHasPayment($kingdeePaymentSn)
    {
        if (empty($kingdeePaymentSn)){
            return 1;
        }
        $data = [
            "TYPE"    => "5",
            "ORDERNO" => (string)$kingdeePaymentSn
        ];
        $res = (new ThirdErpService)->push(2, 'checkBill', $data);
        if ($res['code'] != 0) {
            return 0;
        }
        return 1;
    }

    public static function syncKingdeeReceipt($kingdeePaymentSn, $receiptId, $supplierName)
    {
        $receipt_info = SupplierReceiptModel::getReceiptById($receiptId);
        if (empty($receipt_info)) {
            throw new \Exception("供应商收款信息查询失败");
        }
        $data = [
            "TYPE"            => "21",
            "BANK_NAME"       => $receipt_info['bank_adderss'],
            "BANK_ACCOUNT_NO" => $receipt_info['account_no'],
            "ORDERNO"         => $kingdeePaymentSn,
            "SUPPLIER"        => $supplierName,
        ];
        $res = (new ThirdErpService)->push(2, 'updateOrderStatus', $data);
        if ($res['code'] != 0) {
            throw new InvalidRequestException('同步供应商收款信息到ERP失败，原因：' . $res['msg']);
        }
        return $res;
    }

    public static function syncKingdeeRemark($kingdeePaymentSn,$kingdeeBpSn, $remark)
    {
        $data = [
            "TYPE"           => "27",
            "er_PayRequestBill_number" => $kingdeePaymentSn,
            "er_PaytBill_number" => $kingdeeBpSn,
            "DES"            => $remark,
        ];
        $res = (new ThirdErpService)->push(2, 'updateOrderStatus', $data);
        if ($res['code'] != 0) {
            throw new InvalidRequestException('同步付款申请单备注信息到ERP失败，原因：' . $res['msg']);
        }
        return $res;
    }

    public static function getPurchaseItemStockInQcResult($purchaseItemIdArr)
    {
        //获取所有的采购单明细
        $purchaseItemList = PurchaseItemsModel::getAllPurItems($purchaseItemIdArr,["is_qc","qc_result","purchase_item_id"]);
        $qcResultMap = [];
        foreach ($purchaseItemIdArr as $purchaseItemId) {
            $qcResultMap[$purchaseItemId] = [
                'is_qc'     => "否",
                'qc_result' => "未质检",
                'qc_qualified' => 1 // 质检是否合格：1合格，2不合格
            ];
        }

        foreach ($purchaseItemList as $key => $purchaseItem) {
            if (!empty($purchaseItem['is_qc'])) {
                $qcResultMap[$purchaseItem['purchase_item_id']]['is_qc'] = "是";
            }
            if (isset($purchaseItem['qc_result']) && $purchaseItem['qc_result'] != 4) {
                $qcResultMap[$purchaseItem['purchase_item_id']]['qc_result'] = "已质检";

                // 根据质检结果判断是否合格
                // 1退货=不合格，2正常入库=合格，3特批入库=合格，4待处理=待处理
                if ($purchaseItem['qc_result'] == 1) {
                    $qcResultMap[$purchaseItem['purchase_item_id']]['qc_qualified'] = 2; // 不合格
                } elseif (in_array($purchaseItem['qc_result'], [2, 3])) {
                    $qcResultMap[$purchaseItem['purchase_item_id']]['qc_qualified'] = 1; // 合格
                }
            }
        }
        return $qcResultMap;
    }

    public static function getPaymentItemListStockInQcResult($paymentItemList)
    {
        $purchaseItemIdArr = array_column($paymentItemList, 'purchase_item_id');
        //先根据采购明细的质检来初始化
        $purchaseItemQcResultMap = self::getPurchaseItemStockInQcResult($purchaseItemIdArr);
        $qcResultMap = [];
        //根据付款申请单来初始化
        foreach ($paymentItemList as $paymentItemInfo) {
            $purchaseItemId = $paymentItemInfo['purchase_item_id'];
            $purchaseItemQcResult = $purchaseItemQcResultMap[$purchaseItemId] ?? [];
            if (!isset($qcResultMap[$paymentItemInfo['payment_id']])) {
                $qcResultMap[$paymentItemInfo['payment_id']] = [
                    'is_qc'            => "否",
                    'qc_result'        => "未质检",
                    'qc_qualified'     => 1, // 质检是否合格：1合格，2不合格，默认合格
                    'is_qc_count'      => 0,
                    'count'            => 0,
                    'unqualified_count' => 0, // 不合格数量
                ];
            }
            if (($purchaseItemQcResult['is_qc'] ?? "") == "是") {
                $qcResultMap[$paymentItemInfo['payment_id']]['is_qc'] = "是";
                $qcResultMap[$paymentItemInfo['payment_id']]['count'] += 1;
            }
            if (($purchaseItemQcResult['qc_result'] ?? "") == "已质检") {
                $qcResultMap[$paymentItemInfo['payment_id']]['is_qc_count'] += 1;
            }
            // 检查是否有不合格的明细
            if (($purchaseItemQcResult['qc_qualified'] ?? null) == 2) {
                $qcResultMap[$paymentItemInfo['payment_id']]['unqualified_count'] += 1;
                // 如果存在一条不合格的，则直接返回质检不合格
                $qcResultMap[$paymentItemInfo['payment_id']]['qc_qualified'] = 2;
            }
        }
        if (!empty($qcResultMap)) {
            foreach ($qcResultMap as $key => $qcResult) {
                if ($qcResult['is_qc_count'] == $qcResult['count']) {
                    $qcResultMap[$key]['qc_result'] = "全部质检";
                } elseif ($qcResult['is_qc_count'] > 0) {
                    $qcResultMap[$key]['qc_result'] = "部分质检";
                }
                //                var_dump($qcResult);
                if ($qcResult['is_qc'] == "否" && $qcResult['is_qc_count'] == 0) {
                    $qcResultMap[$key]['qc_result'] = "无需质检";
                }
            }
        }

        return $qcResultMap;
    }

    public static function autoChoiceRefundItemId($paymentItemInfo, $refundAmount)
    {
        return [
            "payment_item_id"     => $paymentItemInfo['payment_item_id'],
            "related_id"          => $paymentItemInfo['related_id'],
            "refund_amount"       => $refundAmount,
            "kingdee_pay_item_id" => $paymentItemInfo['kingdee_pay_item_id'],
        ];
    }

    public static function getRefundValueByRefundInfo($refundInfo)
    {
        //根据refundInfo  related_id获取付款申请单明细信息
        $payItemInfo = PaymentItemsModel::getItemByErpIdAndPaymentTypes($refundInfo['related_id'], $refundInfo["refund_amount"], [PaymentItemsModel::PAY_TYPE_PRE_PAYMENT]);
        if (empty($payItemInfo)) {
            throw new \Exception("关联的付款申请单明细不存在,请联系管理员,relateId:{$refundInfo['related_id']}");
        }
        return self::autoChoiceRefundItemId($payItemInfo, $refundInfo['refund_amount']);
    }

    public static function delErpPayment($payment_id)
    {
        $data = SoapRequester::request(SoapRequester::SOAP_NAME_ERP, "deleteOrder", ["TYPE" => 8, "NUMBER" => $payment_id]);
        return ($data && isset($data['is_del']) && $data['is_del']) ? true : false;
    }

    public static function delErpBPPayment($bpSn)
    {
        $data = SoapRequester::request(SoapRequester::SOAP_NAME_ERP, "deleteOrder", ["TYPE" => 13, "ERP_BILLNUMBER" => $bpSn]);
        return ($data);
    }

    public static function syncBankPayStatus($paymentId, $status)
    {
        $paymentInfo = PaymentModel::getPaymentById($paymentId);
        if (empty($paymentInfo)) {
            throw new \Exception("付款单不存在");
        }
        $updateData = [
            'bank_pay_status' => $status
        ];
        PaymentModel::updateById($updateData, $paymentId);
    }

    public static function checkSupplierUnitedStatus($supplierId, $companyId)
    {
        $supplierInfo = SupplierChannelModel::getSupplierBySupplierId($supplierId);
        if (empty($supplierInfo)) {
            throw new \Exception("未找到");
        }
        PurOrderApiService::checkCompanyUnitedStatus($companyId, $supplierInfo['supplier_name']);
    }

}
