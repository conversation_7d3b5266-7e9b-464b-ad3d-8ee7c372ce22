<?php

namespace App\Http\Services;

use App\Exceptions\InvalidRequestException;
use App\Http\Models\CmsUserIntraCodeModel;
use App\Http\Models\PurchaseItemsModel;
use App\Http\Models\PurchaseOrderModel;
use App\Http\Models\SupplierChannelModel;
use App\Http\Utils\ValidatorMsg;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

/**
 * 采购单SKU列表服务类
 */
class PurchaseOrderSkuService extends BaseService
{
    /**
     * 获取采购单SKU列表
     * @param array $params
     * @return array
     * @throws InvalidRequestException
     */
    public function getPurchaseOrderSkuList($params)
    {
        // 参数验证
        $this->validateParams($params);

        $purchaseId = $params['purchase_id'];

        // 获取采购单信息
        $purchaseOrder = PurchaseOrderModel::getPurchaseOrderInfoForSku($purchaseId);

        // 获取供应商信息
        $supplierInfo = SupplierChannelModel::getSupplierInfoBySnForSku($purchaseOrder['supplier_sn']);

        // 获取供应商用户ID
        $supplierUserId = CmsUserIntraCodeModel::getSupplierUserIdByChannelUid($supplierInfo['yunxin_channel_uid']??0);

        // 如果下单的采购员就是供应商的线上采购员，直接返回空数据
         if ($purchaseOrder['purchase_uid'] == $supplierUserId) {
             return [
                 'list' => [],
                 'total' => 0,
                 "reason"=>"下单的采购员就是供应商的线上采购员"
             ];
         }

        // 获取采购单明细
        $purchaseItems = PurchaseItemsModel::getEnabledPurchaseItemsByPurchaseId($purchaseId);

        if (empty($purchaseItems)) {
            return [
                'list' => [],
                'total' => 0,
                "reason"=>"采购单明细为空"
            ];
        }

        // 查询SKU列表
        $skuList = $this->searchSkuList($purchaseItems, $purchaseOrder);

        return [
            'list' => $skuList,
            'total' => count($skuList)
        ];
    }

    /**
     * 验证参数
     * @param array $params
     * @throws InvalidRequestException
     */
    private function validateParams($params)
    {
        $validator = Validator::make($params, [
            'purchase_id' => 'required|integer',
        ]);

        if ($validator->fails()) {
            $errors = $validator->errors()->toArray();
            $errMsg = ValidatorMsg::getMsg($errors);
            throw new InvalidRequestException($errMsg);
        }
    }

    /**
     * 查询SKU列表
     * @param array $purchaseItems
     * @param array $purchaseOrder
     * @return array
     */
    private function searchSkuList($purchaseItems, $purchaseOrder)
    {
        $skuList = [];

        // 遍历每个采购单明细，检查基石中是否存在上架状态的SKU
        foreach ($purchaseItems as $item) {
            try {
                // 调用基石接口查询SKU信息
                $footstoneResult = $this->searchFootstoneSku($item['goods_name'], $item['brand_name'],$purchaseItems['supplier_id']);

                if (!empty($footstoneResult)) {
                    foreach ($footstoneResult as $skuInfo) {
                        $skuList[] = [
                            'model' => $skuInfo['goods_name_origin'] ?? $item['goods_name'],
                            'brand' => $skuInfo['standard_brand_name'] ?? $item['brand_name'],
                            'order_purchaser' => $purchaseOrder['purchase_name'] ?? '',
                            'sku_online_purchaser' => $skuInfo['encoded_user_name'] ?? '',
                            'purchase_item_id' => $item['purchase_item_id'],
                            'goods_name' => $item['goods_name'],
                            'brand_name' => $item['brand_name']
                        ];
                    }
                }
            } catch (\Exception $e) {
                // 记录错误日志，但不影响其他SKU的查询
                Log::error("查询基石SKU失败: " . $e->getMessage(), [
                    'purchase_item_id' => $item['purchase_item_id'],
                    'goods_name' => $item['goods_name'],
                    'brand_name' => $item['brand_name']
                ]);
            }
        }

        return $skuList;
    }

    /**
     * 查询基石系统中的SKU信息
     * @param string $goodsName 商品型号
     * @param string $brandName 品牌名称
     * @return array
     * @throws \Exception
     */
    private function searchFootstoneSku($goodsName, $brandName,$supplierId=0)
    {
        try {
            $requestData = [
                "goods_name_origin/eq" => $goodsName,
                "standard_brand_name/eq" => $brandName,
                "status/eq" => 1, // 1未过期 0过期
                "goods_status/eq" => 1, // 1上架 0下架
            ];
            if (!empty($supplierId)){
                $requestData['supplier_id/eqs'] = $supplierId;
            }

            $url = config('website.footstone_new_url') . '/open/searchAbSku';
            $response = Http::asJson()->post($url, $requestData);
            $body = $response->body();
            $res = json_decode($body, true);

            if (empty($res)) {
                throw new \Exception("获取商品信息失败: $body");
            }

            if ($res['code'] != 0) {
                throw new \Exception("获取商品信息失败: {$res['msg']}");
            }
            return $res['data'] ?? [];

        } catch (\Exception $e) {
            Log::error("查询基石SKU接口失败: " . $e->getMessage(), [
                'goods_name' => $goodsName,
                'brand_name' => $brandName,
                'url' => $url ?? '',
                'request_data' => $requestData ?? []
            ]);
            throw $e;
        }
    }
}
