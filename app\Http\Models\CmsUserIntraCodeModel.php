<?php

namespace App\Http\Models;


use Illuminate\Database\Eloquent\Model;


class CmsUserIntraCodeModel extends Model
{
    protected $connection = 'cms';

    protected $table = 'lie_intracode';

    public $timestamps = false;


    public static function getAdmUidsByCodeIds($code_ids)
    {
        $res = self::whereIn('code_id', $code_ids)->get();
        return ($res) ? $res->toArray() : [];
    }
    public static function getAdmUidByCodeId($code_id)
    {
        $res = self::where('code_id', $code_id)->first();
        return ($res) ? $res->toArray() : [];
    }

    public static function getListByAdminIds($admin_ids)
    {
        $res = self::whereIn('admin_id', $admin_ids)->get();
        return ($res) ? $res->toArray() : [];
    }

    //根据用户id获取code_id
    public static function getCodeIdByAdminId($adminId)
    {
        return self::where('admin_id', $adminId)->value('code_id');
    }

    /**
     * 获取供应商用户ID（SKU接口专用）
     * @param string $yunxinChannelUid
     * @return int
     * @throws \App\Exceptions\InvalidRequestException
     */
    public static function getSupplierUserIdByChannelUid($yunxinChannelUid)
    {
        $userInfo = self::getAdmUidByCodeId($yunxinChannelUid);
        return $userInfo['admin_id']??0;
    }

}
