<?php

namespace App\Http\Controllers;

use App\Exports\AbnormalPaymentItemsExport;
use App\Http\ApiHelper\ApiCode;
use App\Http\Caches\RateCache;
use App\Http\IdSender\IdSender;
use App\Http\Models\PayableItemsModel;
use App\Http\Models\PayableModel;
use App\Http\Models\PaymentItemsModel;
use App\Http\Models\PaymentModel;
use App\Http\Models\PaymentReceivableModel;
use App\Http\Models\PurchaseApproveModel;
use App\Http\Models\PurchaseItemsModel;
use App\Http\Models\PurchaseOrderModel;
use App\Http\Models\SupplierReceiptModel;
use App\Http\Models\UploadFileModel;
use App\Http\Models\CmsUserInfoModel;
use App\Http\Queue\RabbitQueueModel;
use App\Http\Services\ActionLogService;
use App\Http\Services\Api\Audit\AuditPaymentService;
use App\Http\Services\Api\Audit\AuditService;
use App\Http\Services\Api\PurContractService;
use App\Http\Services\ApproveService;
use App\Http\Services\PayableService;
use App\Http\Services\PaymentService;
use App\Http\Services\PermService;
use App\Http\Services\PriceService;
use App\Http\Services\PurOrderService;
use App\Http\Services\SupplierService;
use App\Http\Services\UserService;
use App\Http\Utils\ArrUtil;
use App\Http\Utils\Currency;
use App\Http\Utils\ErrMsg;
use App\Http\Utils\NameConvert;
use App\Http\Utils\SoapRequester;
use App\Http\Utils\ValidatorMsg;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;
use PhpOffice\PhpSpreadsheet\IOFactory;

class PaymentController extends Controller
{

    // 付款申请单列表
    public function paymentList(Request $request)
    {
        return view('payment.paymentList');
    }

    // 付款申请单列表
    public function toPaymentList(Request $request)
    {
        return view('payment.toPaymentList');
    }

    //采购单-新增付款申请单
    public function purPaymentApply(Request $request)
    {
        $pur_ids = $request->input("pur_ids");
        if (empty($pur_ids)) {
            return $this->setError("请传递采购单ids");
        }
        $pur_ids = explode(",", $pur_ids);
        $pur_ids = array_filter($pur_ids);

        $pur_list = PurchaseOrderModel::getPurchasesByIds($pur_ids);
        if (empty($pur_list)) {
            return $this->setError("采购单信息查询失败");
        }

        // 传递明细
        $selected_purchase_item_ids = $request->input("purchase_item_ids");
        $selected_purchase_item_ids = explode(",", $selected_purchase_item_ids);
        $selected_purchase_item_ids = array_filter($selected_purchase_item_ids);

        try {
            // 基本信息中的 采购组织，供应商必须一致，所以取其中一个采购单的信息就可以获取到数据;
            // 供应商名称，采购组织，采购员必须为同一个
            // 币种也得同一个
            //判断采购单的字段是否一致
            (new PurOrderService())->checkPurOrderSameAttr($pur_list, ["pay_id", "company_id", "supplier_id", "purchase_uid", "currency"]);
            //判断采购单有效合同状态
            PaymentService::checkPurListValidContract($pur_ids);
        } catch (\Throwable $throwable) {
            Log::error(json_encode(ErrMsg::getExceptionInfo($throwable)));
            return $this->setError($throwable->getMessage());
        }


        $PurOrderService = new PurOrderService;
        $apply_item_list = $PurOrderService->getPurOrderListByAddPayment(['pur_ids' => implode(",", $pur_ids)]);

        $contractFileUrlMap = PurContractService::getContractFileUrlMapByPurIds($pur_ids);
        // 如果之前已申请付款单，但未付款完成，那么不能继续申请
        foreach ($apply_item_list as $i => $apply_item) {
            // 如果是指定采购单中的明细id，那么只有这些明细id可以申请付款
            if ($selected_purchase_item_ids) {
                if (!in_array($apply_item['purchase_item_id'], $selected_purchase_item_ids)) {
                    unset($apply_item_list[$i]);
                    continue;
                }
            }

            //获取合同信息
            $apply_item_list[$i]['purContractUrl'] = $contractFileUrlMap[$apply_item['purchase_id']] ?? "";
            if ($apply_item['payment_amount_ing'] != 0) {
                unset($apply_item_list[$i]);
            }
        }
        $apply_item_list = array_values($apply_item_list);
        $data = [
            "base_info"         => [
                "company_name"  => $pur_list[0]['company_name'],
                "company_id"    => $pur_list[0]['company_id'],
                "supplier_id"   => $pur_list[0]['supplier_id'],
                "currency"      => $pur_list[0]['currency'],
                "pay_name"      => Arr::get(PurchaseOrderModel::$PAY_TYPE, intval($pur_list[0]["pay_id"]), ""),
                "currency_val"  => Currency::getName($pur_list[0]['currency']),
                "currency_rate" => Currency::getCurrencyExchangeRate($pur_list[0]['currency'], $pur_list[0]['company_id']),
                "pur_pay_name"  => $pur_list[0]['pay_name'],
            ],
            "supplier_receipts" => SupplierService::getSupplierReceipts($pur_list[0]['supplier_id']),
            "apply_item_list"   => $apply_item_list,
            "payment_type_map"  => [
                1 => '预付款',
            ],
        ];
        return view('payment.purPaymentApply', $data);
    }

    /*    private function getCurrencyValByCompanyId($company_id)
        {
            $currency_val = '';
            if ($company_id == PurchaseOrderModel::LIEXINKEJI) {
                $currency_val = "人民币";
            }
            if ($company_id == PurchaseOrderModel::SHENMAO) {
                $currency_val = "美元";
            }
            return $currency_val;
        }*/

    //应付单-新增付款申请单
    public function payablePaymentApply(Request $request)
    {
        $payable_ids = $request->input("payable_ids");
        if (empty($payable_ids)) {
            return $this->setError("请传应付单ids");
        }
        $payable_ids = explode(",", $payable_ids);
        $payable_ids = array_filter($payable_ids);

        // 基本信息中的 采购组织，供应商必须一致，所以取其中一个采购单的信息就可以获取到数据;
        // 供应商名称，采购组织，采购员必须为同一个
        // 币种也得同一个
        $payable_list = PayableModel::getPayablesByIds($payable_ids);
        if ($payable_list && is_array($payable_list)) {
            $payable_map = array_combine(array_column($payable_list, 'payable_id'), $payable_list);
            $payable_company_list = array_column($payable_list, 'company_id');
            if (!ArrUtil::isSame($payable_company_list)) {
                return $this->setError("采购组织必须相同");
            }
            $payable_supplier_list = array_column($payable_list, 'supplier_id');
            if (!ArrUtil::isSame($payable_supplier_list)) {
                return $this->setError("供应商必须相同");
            }
            $payable_purchase_uid_list = array_column($payable_list, 'purchase_uid');
            if (!ArrUtil::isSame($payable_purchase_uid_list)) {
                return $this->setError("采购员必须相同");
            }
            $payable_currency_list = array_column($payable_list, 'currency');
            if (!ArrUtil::isSame($payable_currency_list)) {
                return $this->setError("应付单币种必须相同");
            }
        } else {
            return $this->setError("应付单信息查询失败");
        }

        $apply_item_list = PayableService::getAddPaymentItems($payable_ids);

        //验证有效合同判断
        try {
            PaymentService::checkPayableItemListValidContract($payable_list[0]['supplier_id'], $apply_item_list);
        } catch (\Throwable $throwable) {
            Log::error(json_encode(ErrMsg::getExceptionInfo($throwable)));
            return $this->setError($throwable->getMessage());
        }
        // 如果之前已申请付款单，但未付款完成，那么不能继续申请
        $contractFileUrlMap = PurContractService::getContractFileUrlMapByPurIds(array_column($apply_item_list, 'purchase_id'));
        foreach ($apply_item_list as $i => &$apply_item) {
            $apply_item['payable_sn'] = isset($payable_map[$apply_item['payable_id']]) ? $payable_map[$apply_item['payable_id']]['payable_sn'] : '';
            //获取合同信息
            $apply_item_list[$i]['purContractUrl'] = $contractFileUrlMap[$apply_item['purchase_id']] ?? "";
            if ($apply_item['in_pay_amount'] != 0) {
                unset($apply_item_list[$i]);
            }

            // 如果关联的采购明细有付款中金额，那么不能申请付款
            if ($apply_item['purchase_in_pay_amount'] != 0) {
                unset($apply_item_list[$i]);
            }

            // 如果已全部付款，那么也不需要在申请付款了
            if ($apply_item['no_pay_amount'] == 0) {
                unset($apply_item_list[$i]);
            }
        }
        $apply_item_list = array_values($apply_item_list);
        $data = [
            "upload_file_type_map" => UploadFileModel::FILE_TYPE_FUNCTION_MAP['payment'],
            "base_info"            => [
                "company_name"  => $payable_list[0]['company_name'],
                "company_id"    => $payable_list[0]['company_id'],
                "supplier_id"   => $payable_list[0]['supplier_id'],
                "currency_val"  => Currency::getName($payable_list[0]['currency']),
                "currency"      => $payable_list[0]['currency'],
                "currency_rate" => Currency::getCurrencyExchangeRate($payable_list[0]['currency'], $payable_list[0]['company_id']),
                "pur_pay_name"  => $payable_list[0]['pay_name'],
            ],
            "supplier_receipts"    => SupplierService::getSupplierReceipts($payable_list[0]['supplier_id']),
            "apply_item_list"      => $apply_item_list,
            "payment_type_map"     => [
                3 => '采购付款',
            ],
        ];
        return view('payment.payablePaymentApply', $data);
    }

    //新版本新增付款申请单
    public function paymentApply(Request $request)
    {
        $payment_type = $request->input("payment_type");
        $payable_item_ids = $request->input("payable_item_ids");
        $purchase_item_ids = $request->input("purchase_item_ids");
        $pur_ids = $request->input("pur_ids");
        $pur_ids = explode(",", $pur_ids);
        $pur_ids = array_filter(array_values($pur_ids));

        $payable_item_ids = explode(",", $payable_item_ids);
        $payable_item_ids = array_filter(array_values($payable_item_ids));

        $purchase_item_ids = explode(",", $purchase_item_ids);
        $purchase_item_ids = array_filter(array_values($purchase_item_ids));

        if (empty($payment_type) || !in_array($payment_type, [PaymentModel::PAYMENT_TYPE_PUR, PaymentModel::PAYMENT_TYPE_PAYABLE])) {
            return $this->setError("付款申请单来源类型错误");
        }

        if (empty($purchase_item_ids) && empty($payable_item_ids) && empty($pur_ids)) {
            return $this->setError("请传递应付单明细ids, 或采购明细ids");
        }

        try {
            if ($payment_type == PaymentModel::PAYMENT_TYPE_PUR) {
                // 基本信息中的 采购组织，供应商必须一致，所以取其中一个采购单的信息就可以获取到数据;
                // 供应商名称，采购组织，采购员必须为同一个
                // 币种也得同一个
                //判断采购单的字段是否一致
                if ($pur_ids) {
                    $pur_list = PurchaseOrderModel::getPurchasesByIds($pur_ids);
                    if (empty($pur_list)) {
                        return $this->setError("采购单信息查询失败");
                    }
                } else {
                    $purchase_item_list = PurchaseItemsModel::getItemsByIds($purchase_item_ids);
                    if (empty($purchase_item_list)) {
                        return $this->setError("采购明细信息查询失败");
                    }
                    $pur_ids = array_column($purchase_item_list, 'purchase_id');
                    $pur_list = PurchaseOrderModel::getPurchasesByIds($pur_ids);
                    if (empty($pur_list)) {
                        return $this->setError("采购单信息查询失败");
                    }
                }

                (new PurOrderService())->checkPurOrderSameAttr($pur_list, ["pay_id", "company_id", "supplier_id", "purchase_uid", "currency"]);
                //判断采购单有效合同状态
                PaymentService::checkPurListValidContractByPurchaseList($pur_list);

                $PurOrderService = new PurOrderService;
                $apply_item_list = $PurOrderService->getPurOrderListByAddPayment(['pur_ids' => implode(",", $pur_ids)]);
                $contractFileUrlMap = PurContractService::getContractFileUrlMapByPurIds($pur_ids);
                // 如果之前已申请付款单，但未付款完成，那么不能继续申请
                foreach ($apply_item_list as $i => $apply_item) {
                    // 如果是明细进来的，那么要判断明细id，那么只有这些明细id可以申请付款
                    if (empty($pur_ids) && !in_array($apply_item['purchase_item_id'], $purchase_item_ids)) {
                        unset($apply_item_list[$i]);
                        continue;
                    }

                    //获取合同信息
                    $apply_item_list[$i]['purContractUrl'] = $contractFileUrlMap[$apply_item['purchase_id']] ?? "";
                    if ($apply_item['payment_amount_ing'] != 0) {
                        unset($apply_item_list[$i]);
                    }
                }
                $apply_item_list = array_values($apply_item_list);
                $data = [
                    "payment_type"      => $payment_type,
                    "base_info"         => [
                        "company_name"      => $pur_list[0]['company_name'],
                        "company_id"        => $pur_list[0]['company_id'],
                        "supplier_id"       => $pur_list[0]['supplier_id'],
                        "currency"          => $pur_list[0]['currency'],
                        "sign_company_id"   => $pur_list[0]['sign_company_id'],
                        "sign_company_name" => $pur_list[0]['sign_company_name'],
                        "pay_name"          => Arr::get(PurchaseOrderModel::$PAY_TYPE, intval($pur_list[0]["pay_id"]), ""),
                        "currency_val"      => Currency::getName($pur_list[0]['currency']),
                        "currency_rate"     => Currency::getCurrencyExchangeRate($pur_list[0]['currency'], $pur_list[0]['company_id']),
                        "pur_pay_name"      => $pur_list[0]['pay_name'],
                    ],
                    "supplier_receipts" => SupplierService::getSupplierReceipts($pur_list[0]['supplier_id']),
                    "apply_item_list"   => $apply_item_list,
                    "payment_type_map"  => [
                        1 => '预付款',
                    ],
                ];
                return view('payment.paymentApply', $data);
            } else {
                // 基本信息中的 采购组织，供应商必须一致，所以取其中一个采购单的信息就可以获取到数据;
                // 供应商名称，采购组织，采购员必须为同一个
                // 币种也得同一个
                $payable_item_list = PayableItemsModel::getItemsByIds($payable_item_ids);
                if (empty($payable_item_list)) {
                    return $this->setError("应付单明细信息查询失败");
                }
                $payable_list = PayableModel::getPayablesByIds(array_column($payable_item_list, 'payable_id'));
                if (empty($payable_list)) {
                    return $this->setError("应付单信息查询失败");
                }
                PayableService::checkPayableSameAttr($payable_list);

                $payable_map = array_combine(array_column($payable_list, 'payable_id'), $payable_list);

                $apply_item_list = PayableService::getAddPaymentItemsByPayableItems($payable_item_list);

                //验证有效合同判断
                PaymentService::checkPayableItemListValidContract($payable_list[0]['supplier_id'], $apply_item_list);

                // 如果之前已申请付款单，但未付款完成，那么不能继续申请
                $contractFileUrlMap = PurContractService::getContractFileUrlMapByPurIds(array_column($apply_item_list, 'purchase_id'));
                foreach ($apply_item_list as $i => &$apply_item) {
                    $apply_item['payable_sn'] = isset($payable_map[$apply_item['payable_id']]) ? $payable_map[$apply_item['payable_id']]['payable_sn'] : '';
                    //获取合同信息
                    $apply_item_list[$i]['purContractUrl'] = $contractFileUrlMap[$apply_item['purchase_id']] ?? "";
                    if ($apply_item['in_pay_amount'] != 0) {
                        unset($apply_item_list[$i]);
                    }

                    // 如果关联的采购明细有付款中金额，那么不能申请付款
                    if ($apply_item['purchase_in_pay_amount'] != 0) {
                        unset($apply_item_list[$i]);
                    }

                    // 如果已全部付款，那么也不需要在申请付款了
                    if ($apply_item['no_pay_amount'] == 0) {
                        unset($apply_item_list[$i]);
                    }
                }
                $apply_item_list = array_values($apply_item_list);
                //获取采购单数据
                $purchaseOrderInfo = PurchaseOrderModel::getPurOrderInfoById([$apply_item_list[0]['purchase_id']] ?? 0);
                $data = [
                    "payment_type"      => $payment_type,
                    "base_info"         => [
                        "company_name"      => $payable_list[0]['company_name'],
                        "company_id"        => $payable_list[0]['company_id'],
                        "supplier_id"       => $payable_list[0]['supplier_id'],
                        "currency_val"      => Currency::getName($payable_list[0]['currency']),
                        "currency"          => $payable_list[0]['currency'],
                        "currency_rate"     => Currency::getCurrencyExchangeRate($payable_list[0]['currency'], $payable_list[0]['company_id']),
                        "pur_pay_name"      => $payable_list[0]['pay_name'],
                        "pay_name"          => "",
                        "sign_company_id"   => $purchaseOrderInfo[0]['sign_company_id'] ?? "",
                        "sign_company_name" => $purchaseOrderInfo[0]['sign_company_name'] ?? "",
                    ],
                    "supplier_receipts" => SupplierService::getSupplierReceipts($payable_list[0]['supplier_id']),
                    "apply_item_list"   => $apply_item_list,
                    "payment_type_map"  => [
                        3 => '采购付款',
                    ],
                ];
                return view('payment.paymentApply', $data);
            }
        } catch (\Exception $exception) {
            Log::error(json_encode(ErrMsg::getExceptionInfo($exception)));
            return $this->setError($exception->getMessage());
        }
    }


    //采购单-新增退款申请单
    public function purRefundApply(Request $request)
    {
        $pur_ids = $request->input("pur_ids");
        if (empty($pur_ids)) {
            return $this->setError("请传递采购单ids");
        }
        $pur_ids = explode(",", $pur_ids);
        $pur_ids = array_filter($pur_ids);

        $pur_list = PurchaseOrderModel::getPurchasesByIds($pur_ids);
        if (empty($pur_list)) {
            return $this->setError("采购单信息查询失败");
        }

        // 传递明细
        $selected_purchase_item_ids = $request->input("purchase_item_ids");
        $selected_purchase_item_ids = explode(",", $selected_purchase_item_ids);
        $selected_purchase_item_ids = array_filter($selected_purchase_item_ids);

        $refund_item_list = PaymentService::getCanRefundItemsByPurIds($pur_ids);
        if ($refund_item_list) {
            $purchase_item_ids = array_column($refund_item_list, 'purchase_item_id');
            $purchase_item_map = PurOrderService::getPurchaseItemsMap($purchase_item_ids);
            $purchase_ids = array_column($refund_item_list, 'purchase_id');
            $purchase_map = PurOrderService::getPurchaseMap($purchase_ids);

            $payment_ids = array_column($refund_item_list, 'payment_id');
            $payment_map = PaymentService::getPaymentMap($payment_ids);
            $purItemAmountMap = [];
            //获取已付金额
            $amountPaid = PaymentItemsModel::getAmountPaidFromPurItems($purchase_item_ids);
            $amountPaid = arrayChangeKeyByField($amountPaid, "purchase_item_id");//purchase_item_id,total_amount_paid

            foreach ($refund_item_list as $i => &$payment_item) {
                $purItem = $payment_item['purchase_item'];
                $stockAmount = decimal_number_format(($purItem["in_qty"] - $purItem["return_qty"]) * $purItem["price_in_tax"]);
                if (empty($purItemAmountMap[$payment_item['purchase_item_id']])) {
                    $purItemAmountMap[$payment_item['purchase_item_id']] = 0;
                }
                // 如果是指定采购单中的明细id，那么只有这些明细id可以申请退款
                if ($selected_purchase_item_ids) {
                    if (!in_array($payment_item['purchase_item_id'], $selected_purchase_item_ids)) {
                        unset($refund_item_list[$i]);
                        continue;
                    }
                }

                $payment_item['pay_name'] = "退预付款";
                $payment_item['sale_amount'] = NameConvert::getNoSignFmtPrice(self::getSaleAmount($pur_list[0]['company_name'], $payment_item));
                //                $payment_item['apply_refund_amount'] = bcsub($payment_item['payment_amount'], $payment_item['refund_amount'], 2);
                // 获取关联毛利
                // **针对采购单为代购类型，毛利显示“-”
                $payment_item['gross_profit'] = isset($purchase_item_map[$payment_item['purchase_item_id']]) ? $purchase_item_map[$payment_item['purchase_item_id']]['gross_profit'] : 0;
                $purchase_type = (isset($purchase_map[$payment_item['purchase_id']])) ? $purchase_map[$payment_item['purchase_id']]['purchase_type'] : 0;
                if ($purchase_type == PurchaseOrderModel::PURCHASE_TYPE_PURCHASING) {
                    $payment_item['gross_profit'] = '-';
                }

                $payment_item['payment_sn'] = (isset($payment_map[$payment_item['payment_id']])) ? $payment_map[$payment_item['payment_id']]['payment_sn'] : '';
                $payment_item['rela_sn'] = (isset($purchase_map[$payment_item['purchase_id']])) ? $purchase_map[$payment_item['purchase_id']]['purchase_sn'] : '';


                //                var_dump($payment_item['stock_amount'],$payment_item['amount_paid'],$payment_item['payment_amount'],
                //                    $payment_item['refund_amount']);die;
                $payment_item['apply_refund_amount'] = bcsub($payment_item['payment_amount'], $payment_item['refund_amount'], 2);

                if (($amountPaid[$payment_item['purchase_item_id']]['total_amount_paid'] ?? 0) < $stockAmount) {
                    return $this->setError("请先退货，才能退款；");
                }
            }
        }
        //var_dump($refund_item_list);die;
        $data = [
            "base_info"         => [
                "company_name"      => $pur_list[0]['company_name'],
                "company_id"        => $pur_list[0]['company_id'],
                "supplier_id"       => $pur_list[0]['supplier_id'],
                "currency"          => $pur_list[0]['currency'],
                "pay_name"          => "退预付款",
                "currency_val"      => Currency::getName($pur_list[0]['currency']),
                "currency_rate"     => Currency::getCurrencyExchangeRate($pur_list[0]['currency'], $pur_list[0]['company_id']),
                "pur_pay_name"      => $pur_list[0]['pay_name'],
                "sign_company_id"   => $pur_list[0]['sign_company_id'] ?? "",
                "sign_company_name" => $pur_list[0]['sign_company_name'] ?? "",
            ],
            "supplier_receipts" => [],
            "apply_item_list"   => array_values($refund_item_list),
            "payment_type_map"  => [
                2 => '退预付款'
            ],
        ];
        return view('payment.purRefundApply', $data);
    }


    //应付单-新增退款申请单
    public function payableRefundApply(Request $request)
    {
        $payable_ids = $request->input("payable_ids");
        if (empty($payable_ids)) {
            return $this->setError("请传应付单ids");
        }
        $payable_ids = explode(",", $payable_ids);
        $payable_ids = array_filter($payable_ids);

        // 基本信息中的 采购组织，供应商必须一致，所以取其中一个采购单的信息就可以获取到数据;
        // 供应商名称，采购组织，采购员必须为同一个
        // 币种也得同一个
        $payable_list = PayableModel::getPayablesByIds($payable_ids);
        if ($payable_list && is_array($payable_list)) {
            $payable_map = array_combine(array_column($payable_list, 'payable_id'), $payable_list);
            $payable_company_list = array_column($payable_list, 'company_id');
            if (!ArrUtil::isSame($payable_company_list)) {
                return $this->setError("采购组织必须相同");
            }
            $payable_supplier_list = array_column($payable_list, 'supplier_id');
            if (!ArrUtil::isSame($payable_supplier_list)) {
                return $this->setError("供应商必须相同");
            }
            $payable_purchase_uid_list = array_column($payable_list, 'purchase_uid');
            if (!ArrUtil::isSame($payable_purchase_uid_list)) {
                return $this->setError("采购员必须相同");
            }
            $payable_currency_list = array_column($payable_list, 'currency');
            if (!ArrUtil::isSame($payable_currency_list)) {
                return $this->setError("应付单币种必须相同");
            }
        } else {
            return $this->setError("应付单信息查询失败");
        }

        $refund_item_list = PaymentService::getCanRefundItemsByPayableIds($payable_ids);
        if ($refund_item_list) {
            $purchase_item_ids = array_column($refund_item_list, 'purchase_item_id');
            $purchase_item_map = PurOrderService::getPurchaseItemsMap($purchase_item_ids);
            $payable_ids = array_column($refund_item_list, 'payable_id');
            $purchase_map = PayableService::getPayableMap($payable_ids);

            $payment_ids = array_column($refund_item_list, 'payment_id');
            $payment_map = PaymentService::getPaymentMap($payment_ids);


            foreach ($refund_item_list as &$payment_item) {
                $payment_item['pay_name'] = "退预付款";
                $payment_item['sale_amount'] = NameConvert::getNoSignFmtPrice(self::getSaleAmount($payable_list[0]['company_name'], $payment_item));
                // 获取关联毛利
                // **针对采购单为代购类型，毛利显示“-”
                $payment_item['gross_profit'] = isset($purchase_item_map[$payment_item['purchase_item_id']]) ? $purchase_item_map[$payment_item['purchase_item_id']]['gross_profit'] : 0;
                $purchase_type = (isset($purchase_map[$payment_item['purchase_id']])) ? $purchase_map[$payment_item['purchase_id']]['purchase_type'] : 0;
                if ($purchase_type == PurchaseOrderModel::PURCHASE_TYPE_PURCHASING) {
                    $payment_item['gross_profit'] = '-';
                }
                $payment_item['rela_sn'] = (isset($payable_map[$payment_item['payable_id']])) ? $payable_map[$payment_item['payable_id']]['payable_sn'] : '';
                $payment_item['payment_sn'] = (isset($payment_map[$payment_item['payment_id']])) ? $payment_map[$payment_item['payment_id']]['payment_sn'] : '';
            }
        }

        //获取采购单数据
        $purchaseOrderInfo = PurchaseOrderModel::getPurOrderInfoById([$refund_item_list[0]['purchase_id'] ?? 0] ?? 0);
        $data = [
            "base_info"        => [
                "company_name"      => $payable_list[0]['company_name'],
                "company_id"        => $payable_list[0]['company_id'],
                "supplier_id"       => $payable_list[0]['supplier_id'],
                "currency"          => $payable_list[0]['currency'],
                "pay_name"          => "退采购付款",
                "currency_val"      => Currency::getName($payable_list[0]['currency']),
                "currency_rate"     => Currency::getCurrencyExchangeRate($payable_list[0]['currency'], $payable_list[0]['company_id']),
                "pur_pay_name"      => $payable_list[0]['pay_name'],
                "sign_company_id"   => $purchaseOrderInfo[0]['sign_company_id'] ?? "",
                "sign_company_name" => $purchaseOrderInfo[0]['sign_company_name'] ?? "",
            ],
            "apply_item_list"  => $refund_item_list,
            "payment_type_map" => [
                4 => '退采购付款'
            ],
        ];
        return view('payment.payableRefundApply', $data);
    }


    // 导入付款申请单对账单
    public function getApplyPaymentItems(Request $request)
    {
        $payment_type = $request->input("payment_type");
        $payable_item_ids = $request->input("payable_item_ids");
        $purchase_item_ids = $request->input("purchase_item_ids");
        $pur_ids = $request->input("pur_ids");
        $pur_ids = explode(",", $pur_ids);
        $pur_ids = array_filter(array_values($pur_ids));
        $from_pur_page = ($pur_ids) ? 1 : 0;

        $payable_item_ids = explode(",", $payable_item_ids);
        $payable_item_ids = array_filter(array_values($payable_item_ids));

        $purchase_item_ids = explode(",", $purchase_item_ids);
        $purchase_item_ids = array_filter(array_values($purchase_item_ids));

        if (empty($payment_type) || !in_array($payment_type, [PaymentModel::PAYMENT_TYPE_PUR, PaymentModel::PAYMENT_TYPE_PAYABLE])) {
            return $this->setError("付款申请单来源类型错误");
        }

        if (empty($purchase_item_ids) && empty($payable_item_ids) && empty($pur_ids)) {
            return $this->setError("请传递应付单明细ids, 或采购明细ids");
        }

        $importList = [];
        $file = $request->file('file');
        if ($file) {
            Log::info('importReconciliationExcel: ' . $file->extension());
            if (!in_array($file->extension(), ['xlsx', 'csv', 'xls', 'bin', 'zip'])) {
                return $this->setError("上传文件格式错误");
            }

            // 获取导入内容
            $excel_data = Excel::toArray(null, $file);

            // 多个sheet，取第一个
            $importList = $excel_data[0];
            // 去掉表头
            unset($importList[0]);
            if (empty($importList)) {
                return $this->setError("上传文件未填写内容");
            }
            $importList = array_filter($importList, "App\Http\Utils\ArrUtil::notContainsOnlyNull");
            $importList = array_values($importList);
        }

        try {
            if ($payment_type == PaymentModel::PAYMENT_TYPE_PUR) {
                if ($pur_ids) {
                    $pur_list = PurchaseOrderModel::getPurchasesByIds($pur_ids);
                    if (empty($pur_list)) {
                        return $this->setError("采购单信息查询失败");
                    }
                } else {
                    $purchase_item_list = PurchaseItemsModel::getItemsByIds($purchase_item_ids);
                    if (empty($purchase_item_list)) {
                        return $this->setError("采购明细信息查询失败");
                    }
                    $pur_ids = array_column($purchase_item_list, 'purchase_id');
                    $pur_list = PurchaseOrderModel::getPurchasesByIds($pur_ids);
                    if (empty($pur_list)) {
                        return $this->setError("采购单信息查询失败");
                    }
                }

                $PurOrderService = new PurOrderService;
                $apply_item_list = $PurOrderService->getPurOrderListByAddPayment(['pur_ids' => implode(",", $pur_ids)]);
                $contractFileUrlMap = PurContractService::getContractFileUrlMapByPurIds($pur_ids);
                // 如果之前已申请付款单，但未付款完成，那么不能继续申请
                foreach ($apply_item_list as $i => &$apply_item) {
                    // 如果是明细进来的，那么要判断明细id，那么只有这些明细id可以申请付款
                    if (empty($from_pur_page) && !in_array($apply_item['purchase_item_id'], $purchase_item_ids)) {
                        unset($apply_item_list[$i]);
                        continue;
                    }

                    //获取合同信息
                    $apply_item['purContractUrl'] = $contractFileUrlMap[$apply_item['purchase_id']] ?? "";
                }
                $apply_item_list = array_values($apply_item_list);
            } else {
                $payable_item_list = PayableItemsModel::getItemsByIds($payable_item_ids);
                if (empty($payable_item_list)) {
                    return $this->setError("应付单明细信息查询失败");
                }
                $payable_list = PayableModel::getPayablesByIds(array_column($payable_item_list, 'payable_id'));
                if (empty($payable_list)) {
                    return $this->setError("应付单信息查询失败");
                }
                $payable_map = array_combine(array_column($payable_list, 'payable_id'), $payable_list);
                $apply_item_list = PayableService::getAddPaymentItemsByPayableItems($payable_item_list);

                // 如果之前已申请付款单，但未付款完成，那么不能继续申请
                $contractFileUrlMap = PurContractService::getContractFileUrlMapByPurIds(array_column($apply_item_list, 'purchase_id'));
                foreach ($apply_item_list as $i => &$apply_item) {
                    $apply_item['payable_sn'] = isset($payable_map[$apply_item['payable_id']]) ? $payable_map[$apply_item['payable_id']]['payable_sn'] : '';
                    //获取合同信息
                    $apply_item['purContractUrl'] = $contractFileUrlMap[$apply_item['purchase_id']] ?? "";

                    $stock_in_time = isset($payable_map[$apply_item['payable_id']]) ? $payable_map[$apply_item['payable_id']]['stock_in_time'] : 0;
                    $apply_item['stock_in_time_val'] = NameConvert::getDateTime($stock_in_time);
                }
                $apply_item_list = array_values($apply_item_list);
            }

            if (empty($apply_item_list)) {
                return $this->setError("没有可申请付款明细");
            }

            // 循环遍历付款申请明细，设置对账匹配状态
            foreach ($apply_item_list as &$apply_item) {
                $apply_item['reconciliation_status'] = 0;  //默认没有匹配上导入的数据
                $apply_item['reconciliation_amount'] = 0;
                $apply_item_po_goods_name_key = md5("{$apply_item['po_number']}-{$apply_item['goods_name']}");
                foreach ($importList as $import_info) {
                    $po_goods_name_key = md5("{$import_info[0]}-{$import_info[1]}");
                    if ($apply_item_po_goods_name_key == $po_goods_name_key) {
                        $apply_item['reconciliation_amount'] = $import_info[2];
                        $apply_item['reconciliation_status'] = 1;                                  //  “对账金额”和“付款申请金额”不一致
                        $result = bccomp($apply_item['apply_payment_amount'], $import_info[2], 2); // 比较两个数，精确到小数点后2位
                        if ($result == 0) {
                            $apply_item['reconciliation_status'] = 2;  //  “对账金额”和“付款申请金额”一致
                            break;
                        }
                    }
                }
            }
            unset($apply_item);

            $not_synch_list = [];
            $apply_item_po_goods_name_key_list = [];
            foreach ($apply_item_list as $apply_item) {
                $apply_item_po_goods_name_key = md5("{$apply_item['po_number']}-{$apply_item['goods_name']}");
                $apply_item_po_goods_name_key_list[] = $apply_item_po_goods_name_key;
            }

            foreach ($importList as $import_info) {
                $po_goods_name_key = md5("{$import_info[0]}-{$import_info[1]}");
                if (!in_array($po_goods_name_key, $apply_item_po_goods_name_key_list)) {
                    $not_synch_list[$po_goods_name_key] = [
                        "po_number"             => (string)$import_info[0],
                        "goods_name"            => (string)$import_info[1],
                        "reconciliation_amount" => (float)$import_info[2],
                    ];
                }
            }
            return $this->setSuccessData([
                'apply_item_list' => $apply_item_list,
                'not_synch_list'  => array_values($not_synch_list)
            ]);
        } catch (\Throwable $throwable) {
            Log::error(json_encode(ErrMsg::getExceptionInfo($throwable)));
            return $this->setError($throwable->getMessage());
        }
    }

    // 导出付款申请单异常数据
    public function exportAbnormalItems(Request $request)
    {
        $payment_type = $request->input("payment_type");
        $payable_item_ids = $request->input("payable_item_ids");
        $purchase_item_ids = $request->input("purchase_item_ids");

        $payable_item_ids = explode(",", $payable_item_ids);
        $payable_item_ids = array_filter(array_values($payable_item_ids));

        $purchase_item_ids = explode(",", $purchase_item_ids);
        $purchase_item_ids = array_filter(array_values($purchase_item_ids));

        if (empty($payment_type) || !in_array($payment_type, [PaymentModel::PAYMENT_TYPE_PUR, PaymentModel::PAYMENT_TYPE_PAYABLE])) {
            return $this->setError("付款申请单来源类型错误");
        }

        if (empty($purchase_item_ids) && empty($payable_item_ids)) {
            return $this->setError("请传递应付单明细ids, 或采购明细ids");
        }

        if ($payment_type == PaymentModel::PAYMENT_TYPE_PUR) {
            $purchase_item_list = PurchaseItemsModel::getItemsByIds($purchase_item_ids);
            if (empty($purchase_item_list)) {
                return $this->setError("采购明细信息查询失败");
            }
            $pur_ids = array_column($purchase_item_list, 'purchase_id');
            $pur_list = PurchaseOrderModel::getPurchasesByIds($pur_ids);
            if (empty($pur_list)) {
                return $this->setError("采购单信息查询失败");
            }

            $PurOrderService = new PurOrderService;
            $apply_item_list = $PurOrderService->getPurOrderListByAddPayment(['pur_ids' => implode(",", $pur_ids)]);
            $contractFileUrlMap = PurContractService::getContractFileUrlMapByPurIds($pur_ids);
            // 如果之前已申请付款单，但未付款完成，那么不能继续申请
            foreach ($apply_item_list as $i => &$apply_item) {
                // 如果是指定采购单中的明细id，那么只有这些明细id可以申请付款
                if (!in_array($apply_item['purchase_item_id'], $purchase_item_ids)) {
                    unset($apply_item_list[$i]);
                    continue;
                }
                //获取合同信息
                $apply_item['purContractUrl'] = $contractFileUrlMap[$apply_item['purchase_id']] ?? "";
            }
            $apply_item_list = array_values($apply_item_list);
        } else {
            $payable_item_list = PayableItemsModel::getItemsByIds($payable_item_ids);
            if (empty($payable_item_list)) {
                return $this->setError("应付单明细信息查询失败");
            }
            $payable_list = PayableModel::getPayablesByIds(array_column($payable_item_list, 'payable_id'));
            if (empty($payable_list)) {
                return $this->setError("应付单信息查询失败");
            }
            $payable_map = array_combine(array_column($payable_list, 'payable_id'), $payable_list);
            $apply_item_list = PayableService::getAddPaymentItemsByPayableItems($payable_item_list);

            // 如果之前已申请付款单，但未付款完成，那么不能继续申请
            $contractFileUrlMap = PurContractService::getContractFileUrlMapByPurIds(array_column($apply_item_list, 'purchase_id'));
            foreach ($apply_item_list as $i => &$apply_item) {
                $apply_item['payable_sn'] = isset($payable_map[$apply_item['payable_id']]) ? $payable_map[$apply_item['payable_id']]['payable_sn'] : '';
                //获取合同信息
                $apply_item['purContractUrl'] = $contractFileUrlMap[$apply_item['purchase_id']] ?? "";
                $stock_in_time = isset($payable_map[$apply_item['payable_id']]) ? $payable_map[$apply_item['payable_id']]['stock_in_time'] : 0;
                $apply_item['stock_in_time_val'] = NameConvert::getDateTime($stock_in_time);
            }
            $apply_item_list = array_values($apply_item_list);
        }

        return Excel::download(new AbnormalPaymentItemsExport($payment_type, $apply_item_list), 'abnormal_payment_items.xlsx');
    }

    // 导出对账单未同步数据
    public function exportNotSynchItems(Request $request)
    {
        $not_synch_list_json = $request->input("not_synch_list");
        $not_synch_list = json_decode($not_synch_list_json, true);
        if (empty($not_synch_list)) {
            return $this->setError("请传递异常数据");
        }
        $templateFile = base_path() . "/public/template/对账单导入模板.xlsx";
        $reader = IOFactory::createReader("Xlsx");
        $spreadsheet = $reader->load($templateFile);
        $objActSheet = $spreadsheet->getActiveSheet();
        $itemCount = count($not_synch_list);
        $cellNum = 2;
        if ($itemCount > 1) {
            $objActSheet->insertNewRowBefore(3, $itemCount - 1);
        }
        foreach ($not_synch_list as $i => $item) {
            $cellRelationData["A{$cellNum}"] = $item['po_number'];
            $cellRelationData["B{$cellNum}"] = $item['goods_name'];
            $cellRelationData["C{$cellNum}"] = $item['reconciliation_amount'];
            $cellNum++;
        }
        foreach ($cellRelationData as $key => $cellData) {
            $objActSheet->setCellValue($key, (string)$cellData);
        }
        //在原有模板的基础上创建新的模板
        $objWriter = IOFactory::createWriter($spreadsheet, 'Xlsx');
        $outputFileName = "not_synch_reconciliation.xlsx";
        //保存文件
        header("Content-Type: application/force-download");
        header("Content-Type: application/octet-stream");
        header("Content-Type: application/download");
        header('Content-Disposition:inline;filename="' . $outputFileName . '"');
        header("Content-Transfer-Encoding: binary");
        header("Expires: Mon, 26 Jul 1997 05:00:00 GMT");
        header("Last-Modified: " . gmdate("D, d M Y H:i:s") . " GMT");
        header("Cache-Control: must-revalidate, post-check=0, pre-check=0");
        header("Pragma: no-cache");
        $objWriter->save("php://output");
    }

    // 检测采购单是否可生成付款申请单
    public function checkPurCanMakePayment(Request $request)
    {
        $pur_ids = $request->input("pur_ids");
        if (empty($pur_ids)) {
            return $this->setError("请传递采购单ids");
        }
        $pur_ids = explode(",", $pur_ids);
        $pur_ids = array_filter($pur_ids);

        // 传递明细
        $selected_purchase_item_ids = $request->input("purchase_item_ids");
        $selected_purchase_item_ids = explode(",", $selected_purchase_item_ids);
        $selected_purchase_item_ids = array_filter($selected_purchase_item_ids);


        $PurOrderService = new PurOrderService;
        $apply_item_list = $PurOrderService->getPurOrderListByAddPayment(['pur_ids' => implode(",", $pur_ids)]);


        //判断采购单有效合同状态
        try {
            PaymentService::checkPurListValidContract($pur_ids);
        } catch (\Throwable $throwable) {
            return $this->setError($throwable->getMessage());
        }


        $errorMsg = [];
        // 如果之前已申请付款单，但未付款完成，那么不能继续申请
        foreach ($apply_item_list as $i => $apply_item) {
            // 如果是指定采购单中的明细id，那么只有这些明细id可以申请付款
            if ($selected_purchase_item_ids) {
                if (!in_array($apply_item['purchase_item_id'], $selected_purchase_item_ids)) {
                    unset($apply_item_list[$i]);
                    continue;
                }
            }
            //还有未完成的付款明细
            if ($apply_item['payment_amount_ing'] != 0) {
                $errorMsg[0] = sprintf("需要完成前面申请的付款申请单，才能继续申请付款");
                unset($apply_item_list[$i]);
                continue;
            }
            //未付金额为0  没有可预付的金额
            if ($apply_item['no_payment_amount'] == 0) {
                unset($apply_item_list[$i]);
                continue;
            }

            //已付金额大于等于预付金额
            //            if ($apply_item['amount_paid'] >= $apply_item['advance_payment']) {
            //                $financial = "温玲敏";
            //                if ($apply_item["purOrdercurrency"] != "1") {
            //                    $financial = "李弯";
            //                }
            //                $remindMsg = "<p style='font-size: 10px;'>注意：到货入库后，金蝶要隔天才会推<span style='font-weight: 800;color: #42999A;font-size: 14px;'>应付单</span>同步到采购系统，如果急需付款，请在到货入库后，找<span style='font-weight: 400;color: #42999A;'>财务{$financial}</span>在金蝶帮忙推下应付单</p>";
            //                $yufukuanMsg = "";
            //                if (in_array($apply_item["pay_id"], [1, 5])) {
            //                    $payName = \Arr::get(PurchaseOrderModel::$PAY_TYPE, $apply_item["pay_id"]);
            //                    if ($apply_item["first_pay_type"] == 1) {
            //                        $yufukuanMsg .= sprintf("<span style='color:#F8173B'>%s%s%% 已付款%s%%</span>", $payName,
            //                            $apply_item["first_pay_amount"] * 100, $apply_item["first_pay_amount"] * 100);
            //                    } else {
            //                        $yufukuanMsg .= sprintf("<span style='color:#F8173B'>%s%s,已付款%s</span>", $payName,
            //                            $apply_item["first_pay_amount"], $apply_item["first_pay_amount"]);
            //                    }
            //                    $errorMsg[] = sprintf("<p style='font-size:18px;font-weight:bold;'>付款方式：%s；剩余付款要等到货入库后，在应付单页面申请付款</p>%s",
            //                        $yufukuanMsg, $remindMsg);
            //                }
            //                unset($apply_item_list[$i]);
            //            }
        }

        if (empty($apply_item_list)) {
            if (!empty($errorMsg)) {
                return $this->setError(current($errorMsg));
            }
            return $this->setError("没有找到符合条件可付款的明细");
        }


        // 如果没有供应商付款信息，那么提示去供应商系统维护
        $apply_item_list = array_values($apply_item_list);
        if ($apply_item_list) {
            $pur_list = PurchaseOrderModel::getPurchasesByIds($pur_ids);
            $supplier_receipts = SupplierService::getSupplierReceipts($pur_list[0]['supplier_id']);
            if (empty($supplier_receipts)) {
                $data = [
                    "can_make_payment" => 0,
                    "message"          => "缺乏有效付款信息，请去供应商系统维护财务信息"
                ];
                return $this->setSuccessData($data);
            }
        }

        $data = [
            "can_make_payment" => ($apply_item_list) ? 1 : 0,
            "message"          => ($apply_item_list) ? "" : "该采购单当前没有可付款明细",
        ];
        return $this->setSuccessData($data);
    }

    // 检测采购单是否可生成付款申请单
    public function checkPurCanMakePaymentForPayable(Request $request)
    {
        $pur_ids = $request->input("pur_ids");
        if (empty($pur_ids)) {
            return $this->setError("请传递采购单ids");
        }
        $pur_ids = explode(",", $pur_ids);
        $pur_ids = array_filter($pur_ids);

        // 传递明细
        $selected_purchase_item_ids = $request->input("purchase_item_ids");
        $selected_purchase_item_ids = explode(",", $selected_purchase_item_ids);
        $selected_purchase_item_ids = array_filter($selected_purchase_item_ids);


        $PurOrderService = new PurOrderService;
        $apply_item_list = $PurOrderService->getPurOrderListByAddPayment(['pur_ids' => implode(",", $pur_ids)]);


        //判断采购单有效合同状态
        try {
            PaymentService::checkPurListValidContract($pur_ids);
        } catch (\Throwable $throwable) {
            return $this->setError($throwable->getMessage());
        }

        $errorPurchaseSn = [];
        $errorMsg = [];
        // 如果之前已申请付款单，但未付款完成，那么不能继续申请
        foreach ($apply_item_list as $i => $apply_item) {
            // 如果是指定采购单中的明细id，那么只有这些明细id可以申请付款
            if ($selected_purchase_item_ids) {
                if (!in_array($apply_item['purchase_item_id'], $selected_purchase_item_ids)) {
                    unset($apply_item_list[$i]);
                    continue;
                }
            }
            //还有未完成的付款明细
            if ($apply_item['payment_amount_ing'] != 0) {
                $errorMsg[0] = sprintf("需要完成前面申请的付款申请单，才能继续申请付款");
                $errorPurchaseSn[] = $apply_item['purchase_sn'];
                unset($apply_item_list[$i]);

                continue;
            }
            //未付金额为0  没有可预付的金额
            if ($apply_item['no_payment_amount'] == 0) {
                unset($apply_item_list[$i]);
                continue;
            }
        }

        if (empty($apply_item_list)) {
            if (!empty($errorMsg)) {
                return $this->setError(current($errorMsg));
            }
            return $this->setError("没有找到符合条件可付款的明细");
        }


        // 如果没有供应商付款信息，那么提示去供应商系统维护
        $apply_item_list = array_values($apply_item_list);
        if ($apply_item_list) {
            $pur_list = PurchaseOrderModel::getPurchasesByIds($pur_ids);
            $supplier_receipts = SupplierService::getSupplierReceipts($pur_list[0]['supplier_id']);
            if (empty($supplier_receipts)) {
                $data = [
                    "can_make_payment" => 0,
                    "message"          => "缺乏有效付款信息，请去供应商系统维护财务信息"
                ];
                return $this->setSuccessData($data);
            }
        }

        $errorPurchaseSn = array_unique($errorPurchaseSn);
        $data = [
            "can_make_payment"  => ($apply_item_list) ? 1 : 0,
            "message"           => ($apply_item_list) ? "" : "该采购单当前没有可付款明细",
            "error_purchase_sn" => $errorPurchaseSn
        ];
        return $this->setSuccessData($data);
    }

    // 检测应付单是否可生成付款申请单
    public function checkPayableCanMakePayment(Request $request)
    {
        $payable_ids = $request->input("payable_ids");
        if (empty($payable_ids)) {
            return $this->setError("请传应付单ids");
        }
        $payable_ids = explode(",", $payable_ids);
        $payable_ids = array_filter($payable_ids);

        // 基本信息中的 采购组织，供应商必须一致，所以取其中一个采购单的信息就可以获取到数据;
        // 供应商名称，采购组织，采购员必须为同一个
        // 币种也得同一个
        $payable_list = PayableModel::getPayablesByIds($payable_ids);
        if ($payable_list && is_array($payable_list)) {
            $payable_map = array_combine(array_column($payable_list, 'payable_id'), $payable_list);
            $payable_company_list = array_column($payable_list, 'company_id');
            if (!ArrUtil::isSame($payable_company_list)) {
                return $this->setError("采购组织必须相同");
            }
            $payable_supplier_list = array_column($payable_list, 'supplier_id');
            if (!ArrUtil::isSame($payable_supplier_list)) {
                return $this->setError("供应商必须相同");
            }
            $payable_purchase_uid_list = array_column($payable_list, 'purchase_uid');
            if (!ArrUtil::isSame($payable_purchase_uid_list)) {
                return $this->setError("采购员必须相同");
            }
            $payable_currency_list = array_column($payable_list, 'currency');
            if (!ArrUtil::isSame($payable_currency_list)) {
                return $this->setError("应付单币种必须相同");
            }
        } else {
            return $this->setError("应付单信息查询失败");
        }

        // 如果没有供应商付款信息，那么提示去供应商系统维护
        if ($payable_supplier_list) {
            $supplier_receipts = SupplierService::getSupplierReceipts($payable_supplier_list[0]);
            if (empty($supplier_receipts)) {
                $data = [
                    "can_make_payment" => 0,
                    "message"          => "缺乏有效付款信息，请去供应商系统维护财务信息"
                ];
                return $this->setSuccessData($data);
            }
        }
        $apply_item_list = PayableService::getAddPaymentItems($payable_ids);
        //验证有效合同
        try {
            PaymentService::checkPayableItemListValidContract($payable_list[0]['supplier_id'], $apply_item_list);
        } catch (\Throwable $throwable) {
            return $this->setError($throwable->getMessage());
        }
        // 如果之前已申请付款单，但未付款完成，那么不能继续申请
        foreach ($apply_item_list as $i => &$apply_item) {
            $apply_item['payable_sn'] = isset($payable_map[$apply_item['payable_id']]) ? $payable_map[$apply_item['payable_id']]['payable_sn'] : '';
            if ($apply_item['in_pay_amount'] != 0) {
                unset($apply_item_list[$i]);
            }

            // 如果关联的采购明细有付款中金额，那么不能申请付款
            if ($apply_item['purchase_in_pay_amount'] != 0) {
                unset($apply_item_list[$i]);
            }

            // 如果已全部付款，那么也不需要在申请付款了
            if ($apply_item['no_pay_amount'] == 0) {
                unset($apply_item_list[$i]);
            }
        }
        $apply_item_list = array_values($apply_item_list);
        $data = [
            "can_make_payment" => ($apply_item_list) ? 1 : 0,
            "message"          => ($apply_item_list) ? "" : "该应付单当前没有可付款明细"
        ];
        return $this->setSuccessData($data);
    }

    // 检测应付单明细是否可生成付款申请单
    public function checkPayableItemCanMakePayment(Request $request)
    {
        $payable_item_ids = $request->input("payable_item_ids");
        if (empty($payable_item_ids)) {
            return $this->setError("请传应付单item ids");
        }
        $payable_item_ids = explode(",", $payable_item_ids);
        $payable_item_ids = array_filter($payable_item_ids);

        // 基本信息中的 采购组织，供应商必须一致，所以取其中一个采购单的信息就可以获取到数据;
        // 供应商名称，采购组织，采购员必须为同一个
        // 币种也得同一个
        $payableItemList = PayableItemsModel::getItemsByIdsWithData($payable_item_ids);
        if (empty($payableItemList)) {
            return $this->setError("数据为空");
        }
        $payableInfo = $payableItemList[0]['payable'];
        $companyId = $payableInfo['company_id'];
        $supplierId = $payableInfo['supplier_id'];
        $purchaseUid = $payableInfo['purchase_uid'];
        $currency = $payableInfo['currency'];

        foreach ($payableItemList as $payableItem) {
            $payableInfo = $payableItem['payable'];
            if ($companyId != $payableInfo['company_id']) {
                return $this->setError("采购组织必须相同");
            }
            if ($supplierId != $payableInfo['supplier_id']) {
                return $this->setError("供应商必须相同");
            }
            if ($purchaseUid != $payableInfo['purchase_uid']) {
                return $this->setError("采购员必须相同");
            }
            if ($currency != $payableInfo['currency']) {
                return $this->setError("应付单币种必须相同");
            }
        }

        // 如果没有供应商付款信息，那么提示去供应商系统维护
        $supplier_receipts = SupplierService::getSupplierReceipts($supplierId);
        if (empty($supplier_receipts)) {
            $data = [
                "can_make_payment" => 0,
                "message"          => "缺乏有效付款信息，请去供应商系统维护财务信息",
                "error_payable_sn" => [],
            ];
            return $this->setSuccessData($data);
        }
        //验证有效合同
        try {
            PaymentService::checkPayableItemListValidContract($supplierId, $payableItemList);
        } catch (\Throwable $throwable) {
            return $this->setError($throwable->getMessage());
        }

        $errorPayableSn = [];
        // 如果之前已申请付款单，但未付款完成，那么不能继续申请
        //        var_dump($payableItemList);
        $apply_item_list = PayableService::getAddPaymentItemsByPayableItems($payableItemList);
        $errorMsgReasonArr = [];
        foreach ($apply_item_list as $i => &$apply_item) {
            if ($apply_item['in_pay_amount'] != 0) {
                $errorPayableSn[] = $apply_item['payable']['payable_sn'] ?? "";
                unset($apply_item_list[$i]);
                $errorMsgReasonArr[] = "应付单明细{$apply_item['payable_item_id']}已付款中金额不为0";
            }

            // 如果关联的采购明细有付款中金额，那么不能申请付款
            if ($apply_item['purchase_in_pay_amount'] != 0) {
                unset($apply_item_list[$i]);
                $errorMsgReasonArr[] = "应付单明细{$apply_item['payable_item_id']}关联的采购明细有付款中金额不为0";
            }

            // 如果已全部付款，那么也不需要在申请付款了
            if ($apply_item['no_pay_amount'] == 0) {
                unset($apply_item_list[$i]);
                $errorMsgReasonArr[] = "应付单明细{$apply_item['payable_item_id']}已全部付款";
            }
        }
        $errorPayableSn = array_unique($errorPayableSn);
        $apply_item_list = array_values($apply_item_list);
        $data = [
            "can_make_payment" => ($apply_item_list) ? 1 : 0,
            "message"          => ($apply_item_list) ? "" : "该应付单当前没有可付款明细:" . implode(";", $errorMsgReasonArr),
            "error_payable_sn" => $errorPayableSn,
            "payableItemList"  => $payableItemList
        ];
        return $this->setSuccessData($data);
    }


    // 检测采购单是否可生成退款申请单
    public function checkPurCanMakeRefund(Request $request)
    {
        $pur_ids = $request->input("pur_ids");
        if (empty($pur_ids)) {
            return $this->setError("请传递采购单ids");
        }
        $pur_ids = explode(",", $pur_ids);
        $pur_ids = array_filter($pur_ids);

        // 传递明细
        $selected_purchase_item_ids = $request->input("purchase_item_ids");
        $selected_purchase_item_ids = explode(",", $selected_purchase_item_ids);
        $selected_purchase_item_ids = array_filter($selected_purchase_item_ids);

        // 基本信息中的 采购组织，供应商必须一致，所以取其中一个采购单的信息就可以获取到数据;
        // 供应商名称，采购组织，采购员必须为同一个
        // 币种也得同一个
        $purchase_list = PurchaseOrderModel::getPurchasesByIds($pur_ids);
        if ($purchase_list && is_array($purchase_list)) {
            $company_list = array_column($purchase_list, 'company_id');
            if (!ArrUtil::isSame($company_list)) {
                return $this->setError("采购组织必须相同");
            }
            $supplier_list = array_column($purchase_list, 'supplier_id');
            if (!ArrUtil::isSame($supplier_list)) {
                return $this->setError("供应商必须相同");
            }
            $purchase_uid_list = array_column($purchase_list, 'purchase_uid');
            if (!ArrUtil::isSame($purchase_uid_list)) {
                return $this->setError("采购员必须相同");
            }
            $currency_list = array_column($purchase_list, 'currency');
            if (!ArrUtil::isSame($currency_list)) {
                return $this->setError("采购单币种必须相同");
            }
        } else {
            return $this->setError("采购单信息查询失败");
        }

        $refund_item_list = PaymentService::getCanRefundItemsByPurIds($pur_ids);
        $purchase_item_ids = array_column($refund_item_list, 'purchase_item_id');
        //获取已付金额
        $amountPaid = PaymentItemsModel::getAmountPaidFromPurItems($purchase_item_ids);
        $amountPaid = arrayChangeKeyByField($amountPaid, "purchase_item_id");//purchase_item_id,total_amount_paid
        $paymentMap = PaymentService::getPaymentMapByPurchaseItemIdArr($purchase_item_ids);
        //        var_dump($paymentMap);die;

        foreach ($refund_item_list as $i => $apply_item) {
            $purItem = $apply_item['purchase_item'];
            $stockAmount = decimal_number_format(($purItem["in_qty"] - $purItem["return_qty"]) * $purItem["price_in_tax"]);
            // 如果是指定采购单中的明细id，那么只有这些明细id可以申请付款
            if ($selected_purchase_item_ids) {
                if (!in_array($apply_item['purchase_item_id'], $selected_purchase_item_ids)) {
                    unset($refund_item_list[$i]);
                    continue;
                }
            }
            if (isset($paymentMap[$apply_item['purchase_item_id']])) {
                unset($refund_item_list[$i]);
                continue;
            }

            $apply_item['stock_amount'] = decimal_number_format(($purItem["in_qty"] - $purItem["return_qty"]) * $purItem["price_in_tax"]);
            //            $apply_item['amount_paid'] = decimal_number_format(($purItem["amount_paid"]));
            //                var_dump($payment_item['stock_amount'],$payment_item['amount_paid'],$payment_item['payment_amount'],
            //                    $payment_item['refund_amount']);die;
            $apply_item['apply_refund_amount'] = bcsub($apply_item['payment_amount'], $apply_item['refund_amount'], 2);
            if (($amountPaid[$apply_item['purchase_item_id']]['total_amount_paid'] ?? 0) < $stockAmount) {
                return $this->setError("请先退货，才能退款；");
            }
            if (($amountPaid[$apply_item['purchase_item_id']]['total_amount_paid'] ?? 0) == 0) {
                unset($refund_item_list[$i]);
            }
        }

        $data = [
            "can_make_refund" => (array_values($refund_item_list)) ? 1 : 0,
            "message"         => ($refund_item_list) ? "" : "该采购单当前没有可退款明细或存在待处理退款/付款明细"
        ];
        return $this->setSuccessData($data);
    }


    // 检测应付单是否可生成退款申请单
    public function checkPayableCanMakeRefund(Request $request)
    {
        $payable_ids = $request->input("payable_ids");
        if (empty($payable_ids)) {
            return $this->setError("请传递应付单ids");
        }
        $payable_ids = explode(",", $payable_ids);
        $payable_ids = array_filter($payable_ids);

        // 基本信息中的 采购组织，供应商必须一致，所以取其中一个采购单的信息就可以获取到数据;
        // 供应商名称，采购组织，采购员必须为同一个
        // 币种也得同一个
        $payable_list = PayableModel::getPayablesByIds($payable_ids);
        if ($payable_list && is_array($payable_list)) {
            $payable_company_list = array_column($payable_list, 'company_id');
            if (!ArrUtil::isSame($payable_company_list)) {
                return $this->setError("采购组织必须相同");
            }
            $payable_supplier_list = array_column($payable_list, 'supplier_id');
            if (!ArrUtil::isSame($payable_supplier_list)) {
                return $this->setError("供应商必须相同");
            }
            $payable_purchase_uid_list = array_column($payable_list, 'purchase_uid');
            if (!ArrUtil::isSame($payable_purchase_uid_list)) {
                return $this->setError("采购员必须相同");
            }
            $payable_currency_list = array_column($payable_list, 'currency');
            if (!ArrUtil::isSame($payable_currency_list)) {
                return $this->setError("应付单币种必须相同");
            }
        } else {
            return $this->setError("应付单信息查询失败");
        }

        $refund_item_list = PaymentService::getCanRefundItemsByPayableIds($payable_ids);
        $data = [
            "can_make_refund" => ($refund_item_list) ? 1 : 0,
            "message"         => ($refund_item_list) ? "" : "该采购单当前没有可退款明细或存在待处理退款/付款明细"
        ];
        return $this->setSuccessData($data);
    }

    // 添加付款申请单
    public function addPayment(Request $request)
    {
        // 检验请求参数
        $params = [
            'payment_type'      => $request->input("payment_type"),
            'company_id'        => $request->input("company_id"),
            'supplier_id'       => $request->input("supplier_id"),
            'receipt_id'        => $request->input("receipt_id"),
            'sign_company_id'   => $request->input("sign_company_id", 0),
            'sign_company_name' => $request->input("sign_company_name", ""),
            'remark'            => $request->input("remark"),
            'is_virtual'        => $request->input("is_virtual", PaymentModel::IS_NOT_VIRTUAL),
            'is_urgent'         => $request->input("is_urgent", 0),
            'pur_pay_name'      => $request->input("pur_pay_name"),
            'bank_charges'      => $request->input("bank_charges"),
            'extra_fees'        => $request->input("extra_fees"),
            'apply_item_list'   => $request->input("apply_item_list"),
            'upload_file_ids'   => $request->input("upload_file_ids"),
        ];
        $validator = Validator::make($params, [
            //            'company_id'      => 'required|in:1,2',
            'supplier_id'     => 'required|numeric',
            'receipt_id'      => 'required|numeric',
            'payment_type'    => 'required|in:1,2',
            'apply_item_list' => 'required|array',
            'bank_charges'    => 'nullable|numeric',
            'extra_fees'      => 'nullable|numeric',
        ]);
        if ($validator->fails()) {
            $errors = $validator->errors()->toArray();
            $err_msg = ValidatorMsg::getMsg($errors);
            return $this->setError($err_msg);
        }
        // 检验明细列表
        foreach ($params['apply_item_list'] as $apply_item) {
            $validator = Validator::make($apply_item, [
                'payment_amount' => 'required',
                'payment_type'   => 'required',
                'related_id'     => 'required',
            ]);
            if ($validator->fails()) {
                $errors = $validator->errors()->toArray();
                $err_msg = ValidatorMsg::getMsg($errors);
                return $this->setError($err_msg);
            }
        }
        try {
            //检验供应商实体名单状态
            PaymentService::checkSupplierUnitedStatus($params['supplier_id'], $params['company_id']);
            //获取关联map,以及获取一个采购单/应付单信息
            // 检验
            $related_ids = array_column($params['apply_item_list'], 'related_id');
            if ($params['payment_type'] == PaymentModel::PAYMENT_TYPE_PUR) {
                $related_item_map = PurOrderService::getPurchaseItemsMap($related_ids);
                foreach ($related_item_map as $purItemVal) {
                    if ($purItemVal["frq_id"] <= 0 || in_array($purItemVal["approve_status"], [PurchaseItemsModel::APPROVE_STATUS_PENDING, PurchaseItemsModel::APPROVE_STATUS_FINISH])) {
                        throw new \Exception(sprintf("型号:%s,已经申请了解绑，或已是解绑的，无法申请付款", $purItemVal["goods_name"]));
                    }
                }
                if (empty($related_item_map)) {
                    throw new \Exception("关联related_ids，没有数据，请检查错误");
                }

                $purchase_ids = array_column($related_item_map, 'purchase_id');
                $purchase_map = PurOrderService::getPurchaseMap($purchase_ids);
                $paid_amount_map = PaymentService::getPaidAmountMapByItemIds($related_ids, PaymentModel::PAYMENT_TYPE_PUR);
                $purchase_item = current($related_item_map);
                $purchase_info = PurchaseOrderModel::getPurOrderInfo($purchase_item['purchase_id']);
                $params['currency'] = $purchase_info['currency'];
                PaymentService::checkPurListValidContract($purchase_ids);

                $apply_amount_map = PaymentService::getApplyAmountMapByItemIds($related_ids, PaymentModel::PAYMENT_TYPE_PUR);
            } else {
                $related_item_map = PayableService::getPayableItemsMap($related_ids);
                if (empty($related_item_map)) {
                    throw new \Exception("关联related_ids，没有数据，请检查错误");
                }

                $paid_amount_map = PaymentService::getPaidAmountMapByItemIds($related_ids, PaymentModel::PAYMENT_TYPE_PAYABLE);
                $paid_item = current($related_item_map);
                $paid_info = PayableModel::getPayableById($paid_item['payable_id']);
                $params['currency'] = $paid_info['currency'];
                PaymentService::checkPayableItemListValidContract($params['supplier_id'], $related_item_map);

                $apply_amount_map = PaymentService::getApplyAmountMapByItemIds($related_ids, PaymentModel::PAYMENT_TYPE_PAYABLE);
            }
            // 检验明细列表
            foreach ($params['apply_item_list'] as $apply_item) {
                // 如果详情id没有获取到数据，那么提示错误
                $related_id = $apply_item['related_id'];
                if (!isset($related_item_map[$related_id])) {
                    throw new \Exception("关联related_id，没有数据，请检查错误");
                }

                if ($apply_item['payment_amount'] == 0) {
                    throw new \Exception("申请付款金额不能为0");
                }

                $apply_amount = isset($apply_amount_map[$related_id]) ? $apply_amount_map[$related_id] : 0;
                $paid_amount = isset($paid_amount_map[$related_id]) ? $paid_amount_map[$related_id] : 0;
                $in_pay_amount = ($apply_amount - $paid_amount);  // 付款中金额 = 申请付款金额-已付金额
                if ($in_pay_amount != 0) {
                    throw new \Exception("明细已生成付款申请单，未付款完成，请付款完成在生成新的付款申请单");
                }

                // 判断申请付款金额是否大于未付款金额，如果大于，那么不能申请
                if ($params['payment_type'] == PaymentModel::PAYMENT_TYPE_PUR) {
                    $purchase_item_info = $related_item_map[$related_id];
                    $purchase_info = $purchase_map[$purchase_item_info['purchase_id']];
                    if (empty($purchase_info)) {
                        throw new \Exception("关联采购单查询失败");
                    }

                    // 预付款和验货付款的，需要校验付款金额是否和付款比例，收款金额相同
                    if (in_array($purchase_info['pay_id'], [
                        PurchaseOrderModel::PAY_TYPE_ADVANCE_CHARGE,
                        PurchaseOrderModel::PAY_TYPE_INSPECTION_PAYMENT
                    ])) {
                        if ($purchase_info['first_pay_type'] == PurchaseOrderModel::FIRST_PAY_TYPE_RATIO) {
                            $no_pay_amount = round($purchase_item_info["purchase_qty"] * $purchase_item_info["price_in_tax"], 2) - $paid_amount;
                            //                            $no_pay_amount = $purchase_item_info['advance_payment'] - $paid_amount;
                            //金额比较一分钱误差的问题  第二种方案 转换成分来比较
                            if (bccomp(round($apply_item['payment_amount'], 2), round($no_pay_amount, 2), 2) == 1) {
                                $rate = $purchase_info['first_pay_amount'] * 100;
                                throw new \Exception("付款方式为：{$purchase_info['pay_name']}{$rate}%, 只能申请{$no_pay_amount}的付款, 剩余的金额必须要等到货入库后，在应付单页面申请付款；");
                            }
                        } else {
                            if ($purchase_info['first_pay_type'] == PurchaseOrderModel::FIRST_PAY_TYPE_NUMBER) {
                                //                                $no_pay_amount = $purchase_item_info['advance_payment'] - $paid_amount;
                                $no_pay_amount = round($purchase_item_info["purchase_qty"] * $purchase_item_info["price_in_tax"], 2) - $paid_amount;
                                if (bccomp(round($apply_item['payment_amount'], 2), round($no_pay_amount, 2), 2) == 1) {
                                    throw new \Exception("付款方式为：{$purchase_info['pay_name']}, 首款金额{$purchase_item_info['advance_payment']}; 只能申请{$no_pay_amount}的付款, 剩余的金额必须要等到货入库后，在应付单页面申请付款；");
                                }
                            }
                        }
                    } else {
                        $total_amount = PurOrderService::getItemTotalAmountByItemInfo($related_item_map[$related_id], $purchase_info['currency']);
                        $no_pay_amount = $total_amount - $paid_amount;
                        // 有可能申请付款金额是负数
                        // 如果是 预付款，那么申请付款金额不能大于未付款金额
                        // 如果是 退预付款，那么申请付款金额不能大于已付款金额
                        if ($apply_item['payment_type'] == PaymentItemsModel::PAY_TYPE_PRE_PAYMENT) {
                            if (bcsub(abs($apply_item['payment_amount']), abs($no_pay_amount), 2) > 0) {
                                throw new \Exception("申请付款金额不能大于未付款金额");
                            }
                        } else {
                            if (bcsub(abs($apply_item['payment_amount']), abs($paid_amount), 2) > 0) {
                                throw new \Exception("申请付款金额不能大于已付款金额");
                            }
                        }
                    }
                } else {
                    $no_pay_amount = $related_item_map[$related_id]['amounts_payable_unit'] - $paid_amount;
                    if (bcsub(abs($apply_item['payment_amount']), abs($no_pay_amount), 2) > 0) {
                        throw new \Exception("申请付款金额不能大于未付款金额");
                    }
                }
            }
        } catch (\Exception $exception) {
            Log::error(json_encode(ErrMsg::getExceptionInfo($exception)));
            return $this->setError($exception->getMessage());
        }

        try {
            DB::connection("mysql")->beginTransaction();

            // 添加付款申请单主表
            $payment_amount_total = array_sum(array_column($params['apply_item_list'], 'payment_amount'));
            $payment_id = $this->createPayment($params, $payment_amount_total);

            // 添加供应商收款信息
            $this->createPaymentReceivable($payment_id, $params['receipt_id']);


            // 检验明细列表
            foreach ($params['apply_item_list'] as $apply_item) {
                // 如果详情id没有获取到数据，那么提示错误
                $related_id = $apply_item['related_id'];
                // 添加付款申请单明细
                $payment_item_info = [
                    "payment_id"        => $payment_id,
                    "purchase_id"       => $related_item_map[$related_id]['purchase_id'],
                    "purchase_item_id"  => $related_item_map[$related_id]['purchase_item_id'],
                    "goods_id"          => $related_item_map[$related_id]['goods_id'],
                    "goods_sn"          => $related_item_map[$related_id]['goods_sn'],
                    "goods_name"        => $related_item_map[$related_id]['goods_name'],
                    "brand_id"          => $related_item_map[$related_id]['brand_id'],
                    "brand_name"        => $related_item_map[$related_id]['brand_name'],
                    "goods_unit"        => $related_item_map[$related_id]['goods_unit'],
                    "price_without_tax" => $related_item_map[$related_id]['price_without_tax'],
                    "price_in_tax"      => $related_item_map[$related_id]['price_in_tax'],
                    "tax_rate"          => $related_item_map[$related_id]['tax_rate'],
                    "payment_amount"    => $apply_item['payment_amount'],
                    "payment_type"      => $apply_item['payment_type']
                ];

                // 采购单和应付单，获取采购数量的字段不一样，另外应付单要记录应付单id和明细id
                if ($params['payment_type'] == PaymentModel::PAYMENT_TYPE_PUR) {
                    $payment_item_info['purchase_qty'] = $related_item_map[$related_id]['purchase_qty'];
                } else {
                    $payment_item_info['payable_id'] = $related_item_map[$related_id]['payable_id'];
                    $payment_item_info['payable_item_id'] = $related_item_map[$related_id]['payable_item_id'];
                    $payment_item_info['purchase_qty'] = $related_item_map[$related_id]['goods_number'];
                }

                $payment_item_id = PaymentItemsModel::addPaymentItem($payment_item_info);
                if (empty($payment_item_id)) {
                    throw new \Exception("付款申请单详细表添加失败");
                }
            }
            DB::connection("mysql")->commit();
        } catch (\Exception $exception) {
            Log::error(json_encode(ErrMsg::getExceptionInfo($exception)));
            DB::connection("mysql")->rollBack();
            return $this->setError($exception->getMessage());
        }

        // 应付单还需要更新状态
        if ($params['payment_type'] == PaymentModel::PAYMENT_TYPE_PAYABLE) {
            $QueueModel = new RabbitQueueModel();
            $payable_ids = array_column($related_item_map, 'payable_id');
            $payable_ids = array_unique($payable_ids);
            $payable_ids = array_values($payable_ids);
            $QueueModel->insertQueue("/queue/payable/checkStatus", [
                "payable_ids" => implode(",", $payable_ids),
            ], RabbitQueueModel::QUEUE_PUR_PAYABLE);

            // 添加日志
            foreach ($payable_ids as $payable_id) {
                ActionLogService::addLog(ActionLogService::TYPE_ACTION_PAYABLE_MAKE_PAY, $payable_id, ["message" => "推成了付款申请单"]);
            }
        }
        return $this->setSuccess("添加成功", ApiCode::API_CODE_SUCCESS, ['payment_id' => $payment_id]);
    }

    // 合并付款申请单
    public function joinPayment(Request $request)
    {
        // 检验请求参数
        $payment_type = $request->input("payment_type");
        $join_payment_id = $request->input("join_payment_id");
        $payable_item_ids = $request->input("payable_item_ids");
        $purchase_item_ids = $request->input("purchase_item_ids");

        $payable_item_ids = explode(",", $payable_item_ids);
        $payable_item_ids = array_filter(array_values($payable_item_ids));

        $purchase_item_ids = explode(",", $purchase_item_ids);
        $purchase_item_ids = array_filter(array_values($purchase_item_ids));


        if (empty($join_payment_id)) {
            return $this->setError("请传递合并付款申请单id");
        }

        if (empty($payment_type) || !in_array($payment_type, [PaymentModel::PAYMENT_TYPE_PUR, PaymentModel::PAYMENT_TYPE_PAYABLE])) {
            return $this->setError("付款申请单来源类型错误");
        }

        if (empty($purchase_item_ids) && empty($payable_item_ids)) {
            return $this->setError("请传递应付单明细ids, 或采购明细ids");
        }

        $join_payment_info = PaymentModel::getPaymentById($join_payment_id);
        if (empty($join_payment_info)) {
            return $this->setError("合并付款申请单信息查询失败");
        }

        $apply_item_list = [];
        try {
            //获取关联map,以及获取一个采购单/应付单信息
            // 检验
            if ($payment_type == PaymentModel::PAYMENT_TYPE_PUR) {
                $related_item_map = PurOrderService::getPurchaseItemsMap($purchase_item_ids);
                foreach ($related_item_map as $purItemVal) {
                    if ($purItemVal["frq_id"] <= 0 || in_array($purItemVal["approve_status"], [PurchaseItemsModel::APPROVE_STATUS_PENDING, PurchaseItemsModel::APPROVE_STATUS_FINISH])) {
                        throw new \Exception(sprintf("型号:%s,已经申请了解绑，或已是解绑的，无法申请付款", $purItemVal["goods_name"]));
                    }
                }
                if (empty($related_item_map)) {
                    throw new \Exception("关联related_ids，没有数据，请检查错误");
                }

                $purchase_ids = array_column($related_item_map, 'purchase_id');
                $purchase_map = PurOrderService::getPurchaseMap($purchase_ids);
                $paid_amount_map = PaymentService::getPaidAmountMapByItemIds($purchase_item_ids, PaymentModel::PAYMENT_TYPE_PUR);
                PaymentService::checkPurListValidContract($purchase_ids);

                $apply_amount_map = PaymentService::getApplyAmountMapByItemIds($purchase_item_ids, PaymentModel::PAYMENT_TYPE_PUR);
            } else {
                $related_item_map = PayableService::getPayableItemsMap($payable_item_ids);
                if (empty($related_item_map)) {
                    throw new \Exception("关联related_ids，没有数据，请检查错误");
                }

                $paid_amount_map = PaymentService::getPaidAmountMapByItemIds($payable_item_ids, PaymentModel::PAYMENT_TYPE_PAYABLE);
                $apply_amount_map = PaymentService::getApplyAmountMapByItemIds($payable_item_ids, PaymentModel::PAYMENT_TYPE_PAYABLE);
            }
            // 检验明细列表
            foreach ($related_item_map as $related_id => $related_info) {
                // 如果详情id没有获取到数据，那么提示错误
                if (!isset($related_item_map[$related_id])) {
                    throw new \Exception("关联related_id，没有数据，请检查错误");
                }

                $apply_amount = isset($apply_amount_map[$related_id]) ? $apply_amount_map[$related_id] : 0;
                $paid_amount = isset($paid_amount_map[$related_id]) ? $paid_amount_map[$related_id] : 0;
                $in_pay_amount = ($apply_amount - $paid_amount);  // 付款中金额 = 申请付款金额-已付金额
                if ($in_pay_amount != 0) {
                    throw new \Exception("明细 型号:{$related_info['goods_name']} 已生成付款申请单，未付款完成，请付款完成在生成新的付款申请单");
                }

                // 判断申请付款金额是否大于未付款金额，如果大于，那么不能申请
                if ($payment_type == PaymentModel::PAYMENT_TYPE_PUR) {
                    $purchase_item_info = $related_item_map[$related_id];
                    $purchase_info = $purchase_map[$purchase_item_info['purchase_id']];
                    if (empty($purchase_info)) {
                        throw new \Exception("关联采购单查询失败");
                    }

                    // 预付款和验货付款的，需要校验付款金额是否和付款比例，收款金额相同
                    if (in_array($purchase_info['pay_id'], [
                        PurchaseOrderModel::PAY_TYPE_ADVANCE_CHARGE,
                        PurchaseOrderModel::PAY_TYPE_INSPECTION_PAYMENT
                    ])) {
                        if ($purchase_info['first_pay_type'] == PurchaseOrderModel::FIRST_PAY_TYPE_RATIO) {
                            $no_pay_amount = round($purchase_item_info["purchase_qty"] * $purchase_item_info["price_in_tax"], 2) - $paid_amount;
                        } else {
                            if ($purchase_info['first_pay_type'] == PurchaseOrderModel::FIRST_PAY_TYPE_NUMBER) {
                                $no_pay_amount = round($purchase_item_info["purchase_qty"] * $purchase_item_info["price_in_tax"], 2) - $paid_amount;
                            }
                        }
                    } else {
                        $total_amount = PurOrderService::getItemTotalAmountByItemInfo($related_item_map[$related_id], $purchase_info['currency']);
                        $no_pay_amount = $total_amount - $paid_amount;
                    }
                } else {
                    $no_pay_amount = $related_item_map[$related_id]['amounts_payable_unit'] - $paid_amount;
                }
                $apply_item_list[] = [
                    'payment_amount' => $no_pay_amount,
                    'related_id'     => $related_id
                ];
            }
        } catch (\Exception $exception) {
            Log::error(json_encode(ErrMsg::getExceptionInfo($exception)));
            return $this->setError($exception->getMessage());
        }


        try {
            DB::connection("mysql")->beginTransaction();

            // 添加付款申请单主表
            $payment_amount_total = $join_payment_info['payment_amount_total'] + array_sum(array_column($apply_item_list, 'payment_amount'));
            // 更新付款申请单主单总金额
            PaymentModel::updateById(['payment_amount_total' => $payment_amount_total], $join_payment_info['payment_id']);

            // 检验明细列表
            foreach ($apply_item_list as $apply_item) {
                // 如果详情id没有获取到数据，那么提示错误
                $related_id = $apply_item['related_id'];
                // 添加付款申请单明细
                $payment_item_info = [
                    "payment_id"        => $join_payment_info['payment_id'],
                    "purchase_id"       => $related_item_map[$related_id]['purchase_id'],
                    "purchase_item_id"  => $related_item_map[$related_id]['purchase_item_id'],
                    "goods_id"          => $related_item_map[$related_id]['goods_id'],
                    "goods_sn"          => $related_item_map[$related_id]['goods_sn'],
                    "goods_name"        => $related_item_map[$related_id]['goods_name'],
                    "brand_id"          => $related_item_map[$related_id]['brand_id'],
                    "brand_name"        => $related_item_map[$related_id]['brand_name'],
                    "goods_unit"        => $related_item_map[$related_id]['goods_unit'],
                    "price_without_tax" => $related_item_map[$related_id]['price_without_tax'],
                    "price_in_tax"      => $related_item_map[$related_id]['price_in_tax'],
                    "tax_rate"          => $related_item_map[$related_id]['tax_rate'],
                    "payment_amount"    => $apply_item['payment_amount'],
                    "payment_type"      => ($payment_type == PaymentModel::PAYMENT_TYPE_PUR) ? PaymentItemsModel::PAY_TYPE_PRE_PAYMENT : PaymentItemsModel::PAY_TYPE_PUR_PAYMENT,
                ];

                // 采购单和应付单，获取采购数量的字段不一样，另外应付单要记录应付单id和明细id
                if ($payment_type == PaymentModel::PAYMENT_TYPE_PUR) {
                    $payment_item_info['purchase_qty'] = $related_item_map[$related_id]['purchase_qty'];
                } else {
                    $payment_item_info['payable_id'] = $related_item_map[$related_id]['payable_id'];
                    $payment_item_info['payable_item_id'] = $related_item_map[$related_id]['payable_item_id'];
                    $payment_item_info['purchase_qty'] = $related_item_map[$related_id]['goods_number'];
                }

                $payment_item_id = PaymentItemsModel::addPaymentItem($payment_item_info);
                if (empty($payment_item_id)) {
                    throw new \Exception("付款申请单详细表添加失败");
                }
            }
            DB::connection("mysql")->commit();
        } catch (\Exception $exception) {
            Log::error(json_encode(ErrMsg::getExceptionInfo($exception)));
            DB::connection("mysql")->rollBack();
            return $this->setError($exception->getMessage());
        }

        // 应付单还需要更新状态
        if ($payment_type == PaymentModel::PAYMENT_TYPE_PAYABLE) {
            $QueueModel = new RabbitQueueModel();
            $payable_ids = array_column($related_item_map, 'payable_id');
            $payable_ids = array_unique($payable_ids);
            $payable_ids = array_values($payable_ids);
            $QueueModel->insertQueue("/queue/payable/checkStatus", [
                "payable_ids" => implode(",", $payable_ids),
            ], RabbitQueueModel::QUEUE_PUR_PAYABLE);

            // 添加日志
            foreach ($payable_ids as $payable_id) {
                ActionLogService::addLog(ActionLogService::TYPE_ACTION_PAYABLE_MAKE_PAY, $payable_id, ["message" => "推成了付款申请单"]);
            }
        }


        return $this->setSuccess("合并付款申请单成功", ApiCode::API_CODE_SUCCESS);
    }

    // 获取可合并付款申请单列表
    public function getCanjoinPaymentList(Request $request)
    {
        $payment_type = $request->input("payment_type");
        $payable_item_ids = $request->input("payable_item_ids");
        $purchase_item_ids = $request->input("purchase_item_ids");

        $payable_item_ids = explode(",", $payable_item_ids);
        $payable_item_ids = array_filter(array_values($payable_item_ids));

        $purchase_item_ids = explode(",", $purchase_item_ids);
        $purchase_item_ids = array_filter(array_values($purchase_item_ids));

        if (empty($payment_type) || !in_array($payment_type, [PaymentModel::PAYMENT_TYPE_PUR, PaymentModel::PAYMENT_TYPE_PAYABLE])) {
            return $this->setError("付款申请单来源类型错误");
        }

        if (empty($purchase_item_ids) && empty($payable_item_ids)) {
            return $this->setError("请传递应付单明细ids, 或采购明细ids");
        }

        try {
            if ($payment_type == PaymentModel::PAYMENT_TYPE_PUR) {
                $purchase_item_list = PurchaseItemsModel::getItemsByIds($purchase_item_ids);
                if (empty($purchase_item_list)) {
                    return $this->setError("采购明细信息查询失败");
                }
                $pur_ids = array_column($purchase_item_list, 'purchase_id');
                $pur_list = PurchaseOrderModel::getPurchasesByIds($pur_ids);
                if (empty($pur_list)) {
                    return $this->setError("采购单信息查询失败");
                }

                $where = [
                    ['payment_type', '=', $payment_type],
                    ['supplier_id', '=', $pur_list[0]['supplier_id']],
                    ['company_name', '=', $pur_list[0]['company_name']],
                    ['status', '=', PaymentModel::STATUS_TO_AUDIT],
                    ['pay_id', '=', $pur_list[0]['pay_id']],
                    ['purchase_uid', '=', $pur_list[0]['purchase_uid']],
                    ['currency', '=', $pur_list[0]['currency']],
                    ['sign_company_id', '=', $pur_list[0]['sign_company_id']],
                ];

                $to_audit_payment_list = PaymentModel::getTop10ListByWhere($where);
            } else {
                // 基本信息中的 采购组织，供应商必须一致，所以取其中一个采购单的信息就可以获取到数据;
                // 供应商名称，采购组织，采购员必须为同一个
                // 币种也得同一个
                $payable_item_list = PayableItemsModel::getItemsByIds($payable_item_ids);
                if (empty($payable_item_list)) {
                    return $this->setError("应付单明细信息查询失败");
                }
                $payable_list = PayableModel::getPayablesByIds(array_column($payable_item_list, 'payable_id'));
                if (empty($payable_list)) {
                    return $this->setError("应付单信息查询失败");
                }
                $purchaseOrderInfo = PurchaseOrderModel::getPurOrderInfoById($payable_item_list[0]['purchase_id']);

                $where = [
                    ['payment_type', '=', $payment_type],
                    ['supplier_id', '=', $payable_list[0]['supplier_id']],
                    ['company_name', '=', $payable_list[0]['company_name']],
                    ['status', '=', PaymentModel::STATUS_TO_AUDIT],
                    ['purchase_uid', '=', $payable_list[0]['purchase_uid']],
                    ['currency', '=', $payable_list[0]['currency']],
                ];
                if (!empty($purchaseOrderInfo['sign_company_id'])) {
                    $where[] = ['sign_company_id', '=', $purchaseOrderInfo['sign_company_id']];
                }
                $to_audit_payment_list = PaymentModel::getTop10ListByWhere($where);
            }
        } catch (\Throwable $throwable) {
            Log::error(json_encode(ErrMsg::getExceptionInfo($throwable)));
            return $this->setError($throwable->getMessage());
        }

        $can_join_payment_list = [];
        foreach ($to_audit_payment_list as $payment) {
            $can_join_payment_list[] = [
                'payment_id' => $payment['payment_id'],
                'payment_sn' => $payment['payment_sn'],
            ];
        }
        return $this->setSuccessData(['can_join_payment_list' => $can_join_payment_list]);
    }

    // 添加退款申请单
    public function addRefund(Request $request)
    {
        // 检验请求参数
        $params = [
            'payment_type'      => $request->input("payment_type"),
            'company_id'        => $request->input("company_id"),
            'supplier_id'       => $request->input("supplier_id"),
            'remark'            => $request->input("remark"),
            'pur_pay_name'      => $request->input("pur_pay_name"),
            'bank_charges'      => $request->input("bank_charges"),
            'apply_item_list'   => $request->input("apply_item_list"),
            'currency'          => $request->input("currency"),
            'is_virtual'        => $request->input("is_virtual", PaymentModel::IS_NOT_VIRTUAL),
            'is_urgent'         => $request->input("is_urgent", 0),
            'sign_company_id'   => $request->input("sign_company_id", 0),
            'sign_company_id'   => $request->input("sign_company_id", 0),
            'sign_company_name' => $request->input("sign_company_name", ""),
        ];
        $validator = Validator::make($params, [
            'currency'        => 'required|numeric',
            'company_id'      => 'required',
            'supplier_id'     => 'required|numeric',
            'payment_type'    => 'required|in:1,2',
            'apply_item_list' => 'required|array',
            'bank_charges'    => 'nullable|numeric',
        ]);
        if ($validator->fails()) {
            $errors = $validator->errors()->toArray();
            $err_msg = ValidatorMsg::getMsg($errors);
            return $this->setError($err_msg);
        }
        // 检验明细列表
        foreach ($params['apply_item_list'] as $apply_item) {
            $validator = Validator::make($apply_item, [
                'payment_amount' => 'required',
                'payment_type'   => 'required',
                'related_id'     => 'required',
            ]);
            if ($validator->fails()) {
                $errors = $validator->errors()->toArray();
                $err_msg = ValidatorMsg::getMsg($errors);
                return $this->setError($err_msg);
            }
        }
        $payment_amount_total = array_sum(array_column($params['apply_item_list'], 'payment_amount'));
        try {
            DB::connection("mysql")->beginTransaction();
            //获取关联map,以及获取一个采购单/应付单信息
            $payment_items_ids = array_column($params['apply_item_list'], 'related_id');
            $payment_item_map = PaymentService::getPaymentItemsMap($payment_items_ids);

            // 添加付款申请单主表
            $payment_id = $this->createPayment($params, $payment_amount_total);

            $erp_related_ids = array_column($payment_item_map, 'related_id');
            if ($params['payment_type'] == PaymentModel::PAYMENT_TYPE_PUR) {
                $apply_refund_erp_ids = PaymentService::getApplyRefundErpIds($erp_related_ids, PaymentItemsModel::PAY_TYPE_UN_PRE_PAYMENT);
            } else {
                $apply_refund_erp_ids = PaymentService::getApplyRefundErpIds($erp_related_ids, PaymentItemsModel::PAY_TYPE_UN_PUR_PAYMENT);
            }
            $allPurItemIds = array_column($payment_item_map, "purchase_item_id");
            //获取已付金额
            $amountPaid = PaymentItemsModel::getAmountPaidFromPurItems($allPurItemIds);
            $amountPaid = arrayChangeKeyByField($amountPaid, "purchase_item_id");//purchase_item_id,total_amount_paid
            //            var_dump($amountPaid);die;
            $purRefundAmountMap = [];
            $refundTotalAmountMap = [];
            // 检验明细列表
            foreach ($params['apply_item_list'] as $apply_item) {
                // 如果详情id没有获取到数据，那么提示错误
                $related_id = $apply_item['related_id'];
                if (!isset($payment_item_map[$related_id])) {
                    throw new \Exception("关联related_id，没有数据，请检查错误");
                }

                $erp_related_id = $payment_item_map[$related_id]['related_id'];
                if (in_array($erp_related_id, $apply_refund_erp_ids)) {
                    throw new \Exception("明细已生成退款单，不能重复生成.");
                }

                if ($apply_item['payment_amount'] == 0) {
                    throw new \Exception("申请付款金额不能为0");
                }
                $purchase_item_id = $payment_item_map[$related_id]['purchase_item_id'];
                if (!isset($purRefundAmountMap[$purchase_item_id])) {
                    $purRefundAmountMap[$purchase_item_id] = 0;
                    $refundTotalAmountMap[$purchase_item_id] = 0;
                }
                //20230404 改动,退款金额可以自定义
                $origin_payment_amount = $payment_item_map[$related_id]['payment_amount'];
                //获取剩余退款金额
                $now_refunded_amount = $payment_item_map[$related_id]['refund_amount'];
                if (bccomp(bcsub($origin_payment_amount, $now_refunded_amount, 2), abs($apply_item['payment_amount']), 2) == -1) {
                    throw new \Exception("申请退款总金额不能大于付款金额,当前剩余退款金额:" . bcsub($origin_payment_amount, $now_refunded_amount, 2));
                }
                $refund_value = PaymentService::autoChoiceRefundItemId($payment_item_map[$related_id], abs($apply_item['payment_amount']));

                //最大可退款金额= 已付金额-退款金额-库存金额(采购明细已付金额-采购明细库存金额)
                //20240122改动,退款金额不能大于  采购明细已付金额-采购明细库存金额
                $purRefundAmountMap[$purchase_item_id] = abs($apply_item['payment_amount']);
                $pur_item = $payment_item_map[$related_id]['purchase_item'];
                $stockAmount = decimal_number_format(($pur_item["in_qty"] - $pur_item["return_qty"]) * $pur_item["price_in_tax"]);
                $refundTotalAmountMap[$purchase_item_id] += abs($apply_item['payment_amount']);

                if (bccomp(bcsub(($amountPaid[$purchase_item_id]['total_amount_paid'] ?? 0), $stockAmount, 2), $refundTotalAmountMap[$purchase_item_id], 2) == -1) {
                    throw new \Exception("型号:{$pur_item['goods_name']}申请退款金额不能大于最大可退款金额:" . bcsub(($amountPaid[$purchase_item_id]['total_amount_paid'] ?? 0), $stockAmount, 2));
                }
                //                var_dump($maxRefundAmountMap, $purRefundAmountMap,$stockAmount);die;

                // 添加付款申请单明细
                $payment_item_info = [
                    "payment_id"        => $payment_id,
                    "payable_id"        => $payment_item_map[$related_id]['payable_id'],
                    "payable_item_id"   => $payment_item_map[$related_id]['payable_item_id'],
                    "purchase_id"       => $payment_item_map[$related_id]['purchase_id'],
                    "purchase_item_id"  => $payment_item_map[$related_id]['purchase_item_id'],
                    "goods_id"          => $payment_item_map[$related_id]['goods_id'],
                    "goods_sn"          => $payment_item_map[$related_id]['goods_sn'],
                    "goods_name"        => $payment_item_map[$related_id]['goods_name'],
                    "brand_id"          => $payment_item_map[$related_id]['brand_id'],
                    "brand_name"        => $payment_item_map[$related_id]['brand_name'],
                    "goods_unit"        => $payment_item_map[$related_id]['goods_unit'],
                    "price_without_tax" => $payment_item_map[$related_id]['price_without_tax'],
                    "price_in_tax"      => $payment_item_map[$related_id]['price_in_tax'],
                    "tax_rate"          => $payment_item_map[$related_id]['tax_rate'],
                    "purchase_qty"      => $payment_item_map[$related_id]['purchase_qty'],
                    "related_id"        => $payment_item_map[$related_id]['related_id'],
                    "related_sn"        => $payment_item_map[$related_id]['related_sn'],
                    "payment_amount"    => $apply_item['payment_amount'],
                    "payment_type"      => $apply_item['payment_type'],
                    "refund_value"      => json_encode($refund_value),
                ];

                $payment_item_id = PaymentItemsModel::addPaymentItem($payment_item_info);
                if (empty($payment_item_id)) {
                    throw new \Exception("付款申请单详细表添加失败");
                }
            }
            DB::connection("mysql")->commit();
        } catch (\Exception $exception) {
            Log::error(json_encode(ErrMsg::getExceptionInfo($exception)));
            DB::connection("mysql")->rollBack();
            return $this->setError($exception->getMessage());
        }

        // 应付单还需要更新状态
        if ($params['payment_type'] == PaymentModel::PAYMENT_TYPE_PAYABLE) {
            $QueueModel = new RabbitQueueModel();
            $payable_ids = array_column($payment_item_map, 'payable_id');
            $payable_ids = array_unique($payable_ids);
            $payable_ids = array_values($payable_ids);
            $QueueModel->insertQueue("/queue/payable/checkStatus", [
                "payable_ids" => implode(",", $payable_ids),
            ], RabbitQueueModel::QUEUE_PUR_PAYABLE);

            // 添加日志
            foreach ($payable_ids as $payable_id) {
                ActionLogService::addLog(ActionLogService::TYPE_ACTION_PAYABLE_MAKE_PAY, $payable_id, ["message" => "推成了退款申请单"]);
            }
        }
        return $this->setSuccess("添加成功", ApiCode::API_CODE_SUCCESS, ['payment_id' => $payment_id]);
    }

    // 修改付款申请单
    public function updatePayment(Request $request)
    {
        // 检验请求参数
        $params = [
            'payment_id'      => $request->input("payment_id"),
            'receipt_id'      => $request->input("receipt_id"),
            'remark'          => $request->input("remark"),
            'bank_charges'    => $request->input("bank_charges"),
            'extra_fees'      => $request->input("extra_fees"),
            'apply_item_list' => $request->input("apply_item_list"),
        ];

        $validator = Validator::make($params, [
            'payment_id'                        => 'required|numeric',
            "remark"                            => 'nullable|string',
            "bank_charges"                      => 'nullable|numeric',
            "extra_fees"                        => 'nullable|numeric',
            'receipt_id'                        => 'required|numeric',
            'apply_item_list'                   => 'required|array',
            'apply_item_list.*.payment_item_id' => 'required|numeric',
            'apply_item_list.*.payment_amount'  => 'required|numeric',
        ]);
        if ($validator->fails()) {
            $errors = $validator->errors()->toArray();
            $err_msg = ValidatorMsg::getMsg($errors);
            return $this->setError($err_msg);
        }


        // 检验明细
        try {
            $payable_ids = [];  // 如果是应付单生成，需要更新应付单状态
            $payment_info = PaymentModel::getPaymentById($params['payment_id']);
            if (empty($payment_info)) {
                return $this->setError("付款申请单信息查询失败.");
            };

            if (!in_array($payment_info['status'], [PaymentModel::STATUS_WAIT_PAY, PaymentModel::STATUS_TO_AUDIT])) {
                throw new \InvalidArgumentException("付款单状态不是待付款或者待提审");
            }

            if (!PaymentService::checkKingdeeHasPayment($payment_info['erp_payment_sn'])) {
                throw new \InvalidArgumentException("该付款申请已经生成生成下游付款单");
            }

            // 查询付款申请单明细信息，关联单据的已付款金额
            $payment_item_ids = array_column($params['apply_item_list'], 'payment_item_id');
            $payment_item_list = PaymentItemsModel::getItemByIdsAndPaymentId($payment_item_ids, $payment_info['payment_id']);
            if (empty($payment_item_list)) {
                throw new \Exception("付款申请单明细信息查询失败，请检查错误");
            }
            $payment_item_map = array_combine(array_column($payment_item_list, 'payment_item_id'), $payment_item_list);
            if ($payment_info['payment_type'] == PaymentModel::PAYMENT_TYPE_PUR) {
                $related_ids = array_column($payment_item_list, 'purchase_item_id');
                $related_item_map = PurOrderService::getPurchaseItemsMap($related_ids);
                $paid_amount_map = PaymentService::getPaidAmountMapByItemIds($related_ids, PaymentModel::PAYMENT_TYPE_PUR);
                if ($related_item_map) {
                    $purchase_item = current($related_item_map);
                }
            } else {
                $payable_ids = array_column($payment_item_list, 'payable_id');
                $related_ids = array_column($payment_item_list, 'payable_item_id');
                $related_item_map = PayableService::getPayableItemsMap($related_ids);
                $paid_amount_map = PaymentService::getPaidAmountMapByItemIds($related_ids, PaymentModel::PAYMENT_TYPE_PAYABLE);
            }

            // 检验明细列表
            foreach ($params['apply_item_list'] as $apply_item) {
                if ($apply_item['payment_amount'] == 0) {
                    throw new \Exception("申请付款金额不能为0");
                }

                $payment_item_info = isset($payment_item_map[$apply_item['payment_item_id']]) ? $payment_item_map[$apply_item['payment_item_id']] : [];
                if (empty($payment_item_info)) {
                    throw new \Exception("关联payment_item_id，没有数据，请检查错误");
                }

                // 判断申请付款金额是否大于未付款金额，如果大于，那么不能申请
                if ($payment_info['payment_type'] == PaymentModel::PAYMENT_TYPE_PUR) {
                    $purchase_item_info = $related_item_map[$payment_item_info['purchase_item_id']];
                    $purchase_info = PurchaseOrderModel::getPurOrderInfo($purchase_item['purchase_id']);
                    if (empty($purchase_info)) {
                        throw new \Exception("关联采购单查询失败");
                    }

                    $paid_amount = isset($paid_amount_map[$purchase_item_info['purchase_item_id']]) ? $paid_amount_map[$payment_item_info['purchase_item_id']] : 0;
                    // 预付款和验货付款的，需要校验付款金额是否和付款比例，收款金额相同
                    if (in_array($purchase_info['pay_id'], [
                        PurchaseOrderModel::PAY_TYPE_ADVANCE_CHARGE,
                        PurchaseOrderModel::PAY_TYPE_INSPECTION_PAYMENT
                    ])) {
                        if ($purchase_info['first_pay_type'] == PurchaseOrderModel::FIRST_PAY_TYPE_RATIO) {
                            //                            $no_pay_amount = $purchase_item_info['advance_payment'] - $paid_amount;
                            $no_pay_amount = round($purchase_item_info["purchase_qty"] * $purchase_item_info["price_in_tax"], 2) - $paid_amount;
                            if ($apply_item['payment_amount'] > $no_pay_amount) {
                                $rate = $purchase_info['first_pay_amount'] * 100;
                                throw new \Exception("付款方式为：{$purchase_info['pay_name']}{$rate}%, 只能申请{$no_pay_amount}的付款, 剩余的金额必须要等到货入库后，在应付单页面申请付款；");
                            }
                        } else {
                            if ($purchase_info['first_pay_type'] == PurchaseOrderModel::FIRST_PAY_TYPE_NUMBER) {
                                //                                $no_pay_amount = $purchase_item_info['advance_payment'] - $paid_amount;
                                $no_pay_amount = round($purchase_item_info["purchase_qty"] * $purchase_item_info["price_in_tax"], 2) - $paid_amount;
                                if ($apply_item['payment_amount'] > $no_pay_amount) {
                                    throw new \Exception("付款方式为：{$purchase_info['pay_name']}, 首款金额{$purchase_item_info['advance_payment']}; 只能申请{$no_pay_amount}的付款, 剩余的金额必须要等到货入库后，在应付单页面申请付款；");
                                }
                            }
                        }
                    } else {
                        $related_id = $payment_item_info['purchase_item_id'];
                        $total_amount = PurOrderService::getItemTotalAmountByItemInfo($related_item_map[$related_id], $purchase_info['currency']);
                        $paid_amount = isset($paid_amount_map[$related_id]) ? $paid_amount_map[$related_id] : 0;
                        $no_pay_amount = $total_amount - $paid_amount;
                        // 有可能申请付款金额是负数
                        // 如果是 预付款，那么申请付款金额不能大于未付款金额
                        // 如果是 退预付款，那么申请付款金额不能大于已付款金额
                        if ($payment_item_info['payment_type'] == PaymentItemsModel::PAY_TYPE_PRE_PAYMENT) {
                            if (bcsub(abs($apply_item['payment_amount']), abs($no_pay_amount), 2) > 0) {
                                throw new \Exception("申请付款金额不能大于未付款金额");
                            }
                        } else {
                            if (bcsub(abs($apply_item['payment_amount']), abs($paid_amount), 2) > 0) {
                                throw new \Exception("申请付款金额不能大于已付款金额");
                            }
                        }
                    }
                } else {
                    $related_id = $payment_item_info['payable_item_id'];
                    $paid_amount = isset($paid_amount_map[$related_id]) ? $paid_amount_map[$related_id] : 0;
                    $no_pay_amount = $related_item_map[$related_id]['amounts_payable_unit'] - $paid_amount;
                    if (abs($apply_item['payment_amount']) > abs($no_pay_amount)) {
                        throw new \Exception("申请付款金额不能大于未付款金额");
                    }
                }
            }
        } catch (\Exception $exception) {
            //            var_dump((string)$exception);
            Log::error(json_encode(ErrMsg::getExceptionInfo($exception)));
            return $this->setError($exception->getMessage());
        }

        $log_message = '';
        try {
            $old_payment_item_list = PaymentItemsModel::getItemsByPaymentId($params['payment_id']);

            DB::connection("mysql")->beginTransaction();
            $payment_amount_total = array_sum(array_column($params['apply_item_list'], 'payment_amount'));
            $res = PaymentModel::updateById([
                'remark'               => $params['remark'],
                'bank_charges'         => $params['bank_charges'] ? $params['bank_charges'] : 0,
                'extra_fees'           => $params['extra_fees'] ? $params['extra_fees'] : 0,
                'payment_amount_total' => $payment_amount_total,
                'receipt_id'           => $params['receipt_id'],
            ], $payment_info['payment_id']);
            if (!$res) {
                throw new \Exception("更新付款申请单remark失败，请检查错误");
            }
            $log_message = "";
            // 记录日志
            if ($params['remark'] != $payment_info['remark']) {
                $log_message .= "备注由{$payment_info['remark']} 修改为 {$params['remark']};";
            }

            if ($params['bank_charges'] != $payment_info['bank_charges']) {
                $log_message .= "银行手续费{$payment_info['bank_charges']} 修改为 {$params['bank_charges']};";
            }
            if ($params['extra_fees'] != $payment_info['extra_fees']) {
                $log_message .= "运杂费{$payment_info['extra_fees']} 修改为 {$params['extra_fees']};";
            }

            // 更新供应商收款信息
            PaymentService::updatePaymentReceivable($payment_info['payment_id'], $params['receipt_id']);

            // 检验明细列表
            foreach ($params['apply_item_list'] as $apply_item) {
                $payment_item_info = $payment_item_map[$apply_item['payment_item_id']];
                // 修改付款申请单明细
                $update_payment_item_info = [
                    "payment_amount" => $apply_item['payment_amount'],
                ];

                PaymentItemsModel::updateById($update_payment_item_info, $payment_item_info['payment_item_id']);

                // 记录修改日志
                if ($payment_item_info['payment_amount'] != $apply_item['payment_amount']) {
                    $log_message .= "申请付款金额由 {$payment_item_info['payment_amount']} 修改为 {$apply_item['payment_amount']}; ";
                }
            }

            // 明细可能删除；例，原有明细5个，删除了4个，修改了1个，那么需要把那4个明细删除
            $old_payment_item_ids = array_column($old_payment_item_list, 'payment_item_id');
            $del_payment_item_ids = array_diff($old_payment_item_ids, $payment_item_ids);
            if ($del_payment_item_ids) {
                PaymentItemsModel::delItemsByIdsAndPaymentId($del_payment_item_ids, $params['payment_id']);
                Log::warning("del_payment_item|del_payment_item_ids:" . implode(",", $del_payment_item_ids));

                $del_payment_id_goods_name_map = array_column($old_payment_item_list, "goods_name", 'payment_item_id');
                foreach ($del_payment_item_ids as $del_payment_id) {
                    $log_message .= "付款类型单删除型号 {$del_payment_id_goods_name_map[$del_payment_id]} 明细; ";
                }
            }
            if (!empty($log_message)) {
                // 添加修改日志
                ActionLogService::addLog(ActionLogService::TYPE_ACTION_PAYMENT_UPDATE, $payment_info['payment_id'], ["message" => $log_message]);
            }

            $old_payable_ids = array_column($old_payment_item_list, 'payable_id');
            $old_payable_ids = array_values(array_unique(array_filter($old_payable_ids)));
            $payable_ids = array_merge($payable_ids, $old_payable_ids);

            //同步金蝶
            PaymentService::syncKingdeeReceipt($payment_info['erp_payment_sn'], $params['receipt_id'], $this->getSupplierName($payment_info['supplier_id']));

            DB::connection("mysql")->commit();
        } catch (\Exception $exception) {
            //            var_dump((string)$exception);
            Log::error(json_encode(ErrMsg::getExceptionInfo($exception)));
            DB::connection("mysql")->rollBack();
            return $this->setError($exception->getMessage());
        }

        // 应付单还需要更新状态
        if ($payable_ids) {
            $QueueModel = new RabbitQueueModel();
            $payable_ids = array_unique($payable_ids);
            $payable_ids = array_values($payable_ids);
            $QueueModel->insertQueue("/queue/payable/checkStatus", [
                "payable_ids" => implode(",", $payable_ids),
            ], RabbitQueueModel::QUEUE_PUR_PAYABLE);
        }
        return $this->setSuccess("修改成功");
    }

    public function updatePaymentRemark(Request $request)
    {
        $payment_id = $request->input("payment_id");
        $remark = $request->input("remark");
        $payment_info = PaymentModel::getPaymentById($payment_id);
        if (empty($payment_info)) {
            return $this->setError("付款申请单信息查询失败.");
        }
        if (!in_array($payment_info['status'], [PaymentModel::STATUS_TO_AUDIT, PaymentModel::STATUS_AUDIT])) {
            return $this->setError("付款申请单状态不正确，不能修改备注");
        }
        //如果存在金蝶单号，那么需要同步金蝶
        if ($payment_info['erp_payment_sn'] || $payment_info['erp_bp_sn']) {
            PaymentService::syncKingdeeRemark($payment_info['erp_payment_sn'], $payment_info['erp_bp_sn'], $remark);
        }

        $res = PaymentModel::updateById([
            'remark' => $remark,
        ], $payment_info['payment_id']);
        if (!$res) {
            return $this->setError("更新付款申请单remark失败，请检查错误");
        }
        return $this->setSuccess("修改成功");
    }

    // 根据条件创建付款申请单主表
    private function createPayment($params, $payment_amount_total)
    {
        // 添加付款申请单主表
        $payment_info = [
            "payment_sn"           => IdSender::getSn(IdSender::TYPE_PAYMENT, $params['company_id']),
            "company_id"           => $params['company_id'],
            "company_name"         => $this->getCompanyName($params['company_id']),
            "supplier_id"          => $params['supplier_id'],
            "sign_company_id"      => $params['sign_company_id'],
            "sign_company_name"    => $params['sign_company_name'],
            "supplier_name"        => $this->getSupplierName($params['supplier_id']),
            'pur_pay_name'         => ($params['pur_pay_name']) ? $params['pur_pay_name'] : "",
            "receipt_id"           => isset($params['receipt_id']) ? $params['receipt_id'] : 0,
            "pay_id"               => ($params['payment_type'] == PaymentModel::PAYMENT_TYPE_PUR) ? 1 : 0,
            "pay_name"             => ($params['payment_type'] == PaymentModel::PAYMENT_TYPE_PUR) ? "预付款" : "",
            "payment_amount_total" => $payment_amount_total,
            "currency"             => $params['currency'],
            "status"               => PaymentModel::STATUS_TO_AUDIT,
            "purchase_uid"         => request()->user->userId,
            "purchase_name"        => request()->user->name,
            "payment_type"         => $params['payment_type'],
            "remark"               => (string)$params['remark'],
            "bank_charges"         => $params['bank_charges'] ?? 0,
            "extra_fees"           => $params['extra_fees'] ?? 0,
            "create_uid"           => request()->user->userId,
            "create_name"          => request()->user->name,
            "create_time"          => time(),
            "upload_file_ids"      => $params['upload_file_ids'] ?? "",
            "is_virtual"           => $params['is_virtual'] ?? PaymentModel::IS_VIRTUAL,
            "is_urgent"            => $params['is_urgent'] ?? 0,
        ];
        $payment_id = PaymentModel::addPayment($payment_info);
        if (empty($payment_id)) {
            throw new \Exception("付款申请单主表添加失败");
        }
        return $payment_id;
    }

    // 添加供应商收款信息
    private function createPaymentReceivable($payment_id, $receipt_id)
    {
        $receipt_info = SupplierReceiptModel::getReceiptById($receipt_id);
        if (empty($receipt_info)) {
            throw new \Exception("供应商收款信息查询失败");
        }

        // 需要校验信息凭证是否上传，如果没有上传，无法生成付款申请单
        if (empty($receipt_info['certificate'])) {
            throw new \Exception("请去供应商系统完善供应商财务信息-信息凭证！");
        }

        $receivable_info = [
            "payment_id"           => $payment_id,
            "bank_name"            => $receipt_info['bank_name'],
            "branch_name"          => $receipt_info['bank_adderss'],
            "bank_sn"              => $receipt_info['account_no'],
            "account_name"         => $receipt_info['bank_name'],           // 供应商系统以bank_name作为开户名称
            "wire_transfer_number" => $receipt_info['swift_code'],
            "credential_info"      => $receipt_info['certificate'],
            "remark"               => $receipt_info['remark'],
        ];

        $receivable_id = PaymentReceivableModel::addReceivable($receivable_info);
        if (empty($receivable_id)) {
            throw new \Exception("供应商收款信息添加失败");
        }
        return $receivable_id;
    }

    // 获取公司名
    private function getCompanyName($company_id)
    {
        $company_name = config("field.PurchaseCompany")[$company_id] ?? "";
        return $company_name;
    }

    // 获取公司名
    private function getCompanyId($company_name)
    {
        $company_id = 0;
        if ($company_name == '深圳市猎芯科技有限公司') {
            $company_id = PurchaseOrderModel::LIEXINKEJI;
        }
        if ($company_name == '深贸电子有限公司') {
            $company_id = PurchaseOrderModel::SHENMAO;
        }
        if ($company_name == '深圳华云数智工业科技有限公司') {
            $company_id = PurchaseOrderModel::HUAYUN;
        }
        return $company_id;
    }

    // 获取小计金额
    private function getSaleAmount($company_name, $payment_item)
    {
        $sale_amount = $payment_item['purchase_qty'] * $payment_item['price_in_tax'];

        return $sale_amount;
    }

    // 获取供应商名
    private function getSupplierName($supplier_id)
    {
        $supplier_name = '';
        $supplier_info = SupplierService::getSupplierBySupplierId($supplier_id);
        if ($supplier_info && is_array($supplier_info)) {
            $supplier_name = $supplier_info['supplier_name'];
        }
        return $supplier_name;
    }

    //付款申请单详情
    public function paymentDetail(Request $request)
    {
        $payment_id = $request->input("id");
        $payment_info = PaymentModel::getPaymentById($payment_id);
        if (empty($payment_info)) {
            return $this->setError("付款申请单信息查询失败");
        }

        $payment_info['create_time'] = NameConvert::getDateTime($payment_info['create_time']);
        $payment_info['payment_amount'] = $payment_info['payment_amount_total'];
        $payment_info['payment_all_amount_total'] = NameConvert::getFmtPrice($payment_info['payment_amount_total'] + $payment_info['bank_charges'] + $payment_info['extra_fees'], $payment_info['currency']);
        $payment_info['payment_amount_total'] = NameConvert::getFmtPrice($payment_info['payment_amount_total'], $payment_info['currency']);
        $payment_info['currency_val'] = NameConvert::getCurrencyName($payment_info['currency']);


        $receivable_info = PaymentReceivableModel::getReceivableByPaymentId($payment_id);

        $payment_item_list = PaymentItemsModel::getItemsByPaymentId($payment_id);
        $payment_qc_map = PaymentService::getPurchaseItemStockInQcResult(array_column($payment_item_list, 'purchase_item_id'));

        $supplierMap = SupplierService::getSupplierBySupplierIds([$payment_info['supplier_id']]);
        $qc_result_map = PaymentService::getPaymentItemListStockInQcResult($payment_item_list);
        $payment_info['supplier_group_name'] = $supplierMap[$payment_info['supplier_id']]['supplier_group_name'] ?? '';
        $payment_info['is_qc'] = $qc_result_map[$payment_info['payment_id']]['is_qc'] ?? "";
        $payment_info['qc_result'] = $qc_result_map[$payment_info['payment_id']]['qc_result'] ?? "";
        $payment_info['qc_qualified'] = $qc_result_map[$payment_info['payment_id']]['qc_qualified'] ?? 1; // 质检是否合格：1合格，2不合格
        // 毛利
        $total_gross_profit_map = PaymentService::getTotalGrossProfitMap([$payment_info]);
        $payment_info['total_gross_profit'] = $total_gross_profit_map[$payment_info['payment_id']] ?? 0;

        //        var_dump($payment_qc_map);die;
        // 如果是应付单，还要展示付款方式；付款方式是关联应付单查询
        $payable_item_map = [];
        $payable_map = [];

        $purchase_item_ids = array_column($payment_item_list, 'purchase_item_id');
        $purchase_item_map = PurOrderService::getPurchaseItemsMap($purchase_item_ids);
        $purchase_ids = array_column($payment_item_list, 'purchase_id');
        $purchase_map = PurOrderService::getPurchaseMap($purchase_ids);
        if ($payment_info['payment_type'] == PaymentModel::PAYMENT_TYPE_PAYABLE) {
            $payable_item_ids = array_column($payment_item_list, 'payable_item_id');
            $payable_item_map = PayableService::getPayableItemsMap($payable_item_ids);
            $payable_ids = array_column($payment_item_list, 'payable_id');
            $payable_map = PayableService::getPayableMap($payable_ids);
        } else {
            $paid_amount_map = PaymentService::getPaidAmountMapByItemIds($purchase_item_ids, PaymentModel::PAYMENT_TYPE_PUR);
        }
        $contractFileUrlGroupMap = PurContractService::getContractFileUrlGroupMapByPurIds(array_column($payment_item_list, 'purchase_id'));
        foreach ($payment_item_list as &$payment_item) {
            $payment_info['pur_pay_name'] = (isset($purchase_map[$payment_item['purchase_id']])) ? $purchase_map[$payment_item['purchase_id']]['pay_name'] : '';

            $payment_item['payment_type_name'] = NameConvert::getPaymentPayTypeName($payment_item['payment_type']);
            $payment_item['no_pay_amount'] = NameConvert::getNoSignFmtPrice(($payment_item['payment_amount'] - $payment_item['amount_paid']));
            $payment_item['sale_amount'] = NameConvert::getNoSignFmtPrice(self::getSaleAmount($payment_info['company_name'], $payment_item));
            $payment_item['pay_name'] = isset($payable_item_map[$payment_item['payable_item_id']]) ? $payable_item_map[$payment_item['payable_item_id']]['pay_name'] : '预付款';

            // 获取关联毛利
            // **针对采购单为代购类型，毛利显示“-”
            $payment_item['gross_profit'] = isset($purchase_item_map[$payment_item['purchase_item_id']]) ? $purchase_item_map[$payment_item['purchase_item_id']]['gross_profit'] : 0;
            $purchase_type = (isset($purchase_map[$payment_item['purchase_id']])) ? $purchase_map[$payment_item['purchase_id']]['purchase_type'] : 0;
            if ($purchase_type == PurchaseOrderModel::PURCHASE_TYPE_PURCHASING) {
                $payment_item['gross_profit'] = '-';
            }

            if ($payment_info['payment_type'] == PaymentModel::PAYMENT_TYPE_PUR) {
                $payment_item['rela_sn'] = (isset($purchase_map[$payment_item['purchase_id']])) ? $purchase_map[$payment_item['purchase_id']]['purchase_sn'] : '';
            } else {
                $payment_item['rela_sn'] = (isset($payable_map[$payment_item['payable_id']])) ? $payable_map[$payment_item['payable_id']]['payable_sn'] : '';
            }

            $payment_item['enContractFileList'] = isset($contractFileUrlGroupMap[$payment_item['purchase_id']]) ? $contractFileUrlGroupMap[$payment_item['purchase_id']]['enContractFileList'] : [];
            $payment_item['cnContractFileList'] = isset($contractFileUrlGroupMap[$payment_item['purchase_id']]) ? $contractFileUrlGroupMap[$payment_item['purchase_id']]['cnContractFileList'] : [];
            $payment_item['is_qc'] = $payment_qc_map[$payment_item['purchase_item_id']]['is_qc'] ?? "";
            $payment_item['qc_result'] = $payment_qc_map[$payment_item['purchase_item_id']]['qc_result'] ?? "";
            $payment_item['qc_qualified'] = $payment_qc_map[$payment_item['purchase_item_id']]['qc_qualified'] ?? 1; // 质检是否合格：1合格，2不合格

            if (($purchase_item_map[$payment_item['purchase_item_id']]['in_qty'] ?? 0) == 0) {
                $payment_item['stock_in_status'] = "未入库";
            } elseif (($purchase_item_map[$payment_item['purchase_item_id']]['in_qty'] ?? 0) < ($purchase_item_map[$payment_item['purchase_item_id']]['purchase_qty'] ?? 0)) {
                $payment_item['stock_in_status'] = "部分入库";
            } else {
                $payment_item['stock_in_status'] = "已入库";
            }
            // 获取原始关联单据明细id的 未付金额，已付金额
            if ($payment_info['payment_type'] == PaymentModel::PAYMENT_TYPE_PAYABLE) {
                $payment_item['origin_amount_paid'] = isset($payable_item_map[$payment_item['payable_item_id']]) ? $payable_item_map[$payment_item['payable_item_id']]['amount_paid'] : 0;
                $amounts_payable_unit = isset($payable_item_map[$payment_item['payable_item_id']]) ? $payable_item_map[$payment_item['payable_item_id']]['amounts_payable_unit'] : 0;
                $payment_item['origin_no_pay_amount'] = ($amounts_payable_unit - $payment_item['origin_amount_paid']);      // 未付款金额 = 应付金额-已付金额
                // 格式化 未付金额，已付金额
                $payment_item['origin_amount_paid'] = NameConvert::getNoSignFmtPrice($payment_item['origin_amount_paid']);
                $payment_item['origin_no_pay_amount'] = NameConvert::getNoSignFmtPrice($payment_item['origin_no_pay_amount']);
            } else {
                $payment_item['origin_amount_paid'] = isset($paid_amount_map[$payment_item['purchase_item_id']]) ? $paid_amount_map[$payment_item['purchase_item_id']] : 0;
                $purchase_currency = (isset($purchase_map[$payment_item['purchase_id']])) ? $purchase_map[$payment_item['purchase_id']]['currency'] : PriceService::CURRENCY_TYPE_RMB;
                $purchase_item_total_amount = PurOrderService::getItemTotalAmountByItemInfo($purchase_item_map[$payment_item['purchase_item_id']], $purchase_currency);
                $payment_item['origin_no_pay_amount'] = ($purchase_item_total_amount - $payment_item['origin_amount_paid']);
                // 格式化 未付金额，已付金额
                $payment_item['origin_amount_paid'] = NameConvert::getNoSignFmtPrice($payment_item['origin_amount_paid']);
                $payment_item['origin_no_pay_amount'] = NameConvert::getNoSignFmtPrice($payment_item['origin_no_pay_amount']);
                $payment_item['pay_name'] = $purchase_map[$payment_item['purchase_id']]['pay_name'] ?? "";
            }
        }
        if ($payment_info['payment_type'] == PaymentModel::PAYMENT_TYPE_PUR) {
            $payment_type_map = [
                1 => '预付款',
                2 => '退预付款'
            ];
        } else {
            $payment_type_map = [
                3 => '采购付款',
                4 => '退采购付款'
            ];
        }

        $supplier_receipts = SupplierService::getSupplierReceipts($payment_info['supplier_id']);

        // 打印预览信息
        $payment_print_preview_info = PaymentService::getPaymentPrintPreviewInfo($payment_id, $payment_info, $receivable_info, $supplier_receipts);
        if ($payment_info['status'] == PaymentModel::STATUS_AUDIT) {
            $auditInfo = AuditService::getAuditInfoByIdAndType($payment_id, AuditPaymentService::AUDIT_TYPE);
            $auditInfo['current_node_info'] = AuditPaymentService::handleNodeInfo($auditInfo['current_node_info'] ?? []);
        } else {
            $auditInfo = [];
        }
        //        var_dump(PaymentService::checkKingdeeHasPayment($payment_info['erp_payment_sn']),$payment_info['status']);die;
        $data = [
            "payment_info"           => $payment_info,
            "is_kingdee_has_payment" => PaymentService::checkKingdeeHasPayment($payment_info['erp_payment_sn']),
            "receivable_info"        => $receivable_info,
            "supplier_receipts"      => $supplier_receipts,
            "action_log_list"        => ActionLogService::getLogsByObjId(ActionLogService::TYPE_PAYMENT, $payment_info['payment_id']),
            "payment_item_list"      => $payment_item_list,
            "payment_type_map"       => $payment_type_map,
            "print_preview_info"     => $payment_print_preview_info,
            "auditInfo"              => $auditInfo,
        ];
        //        dd($data);

        //        if(getAdminUserId()==1000){
        //            var_dump($supplier_receipts);
        //        }
        //var_dump($supplier_receipts,$receivable_info);die;
        // 兼容小程序，如果是小程序调用，那么返回json数据
        $return_format = $request->input("return_format");
        if ($return_format == "json") {
            return $this->setSuccessData($data);
        } else {
            return view('payment.paymentDetail', $data);
        }
    }

    public function printPaymentList(Request $request)
    {
        $paymentIds = $request->input("payment_ids");
        $paymentIds = explode(",", $paymentIds);
        $printList = PaymentService::getPrintPaymentList($paymentIds);
        if (!empty($_GET['debug'])) {
            dd($printList);
            die;
        }
        //
        //        $str =  json_encode(\App\Http\Utils\Currency::getCurrencySign($printList[0]['payment_currency']));
        //        $str = str_replace('\u','&#x',$str);
        //        $str = str_replace('"','',$str);
        //        $str.=";";
        //&#x20AC;
        //        dd($str,\App\Http\Utils\Currency::getCurrencySign($printList[0]['payment_currency']));
        $html = view('payment.printPayment', [
            'print_list' => $printList
        ])->render();

        $pdf = \App::make('dompdf.wrapper');
        $name = 'service_' . time() . '.pdf';
        $pdf->loadHTML($html)->setPaper('c5', 'landscape')//横列 ,  竖列 portrait
        ->setOptions(['enable_font_subsetting' => true])->setWarnings(false);
        return $pdf->stream($name);
    }

    // 付款申请单添加审核
    public function addAudit(Request $request)
    {
        $payment_id = $request->input("id");
        if (empty($payment_id)) {
            return $this->setError("请传递付款申请单id");
        }
        $payment_info = PaymentModel::getPaymentById($payment_id);
        if (empty($payment_info)) {
            return $this->setError("查询付款申请单信息失败");
        }

        AuditPaymentService::addApprove($payment_info, "提交审核");
        // 加入审核
        PaymentModel::updateById(["status" => PaymentModel::STATUS_AUDIT], $payment_id);
        // 添加日志
        ActionLogService::addLog(ActionLogService::TYPE_ACTION_PAYMENT_APPROVE, $payment_id, [
            "message" => "提交了申请审核"
        ]);
        return $this->setSuccess("添加审核成功");
    }

    // 付款申请单删除
    public function delPayments(Request $request)
    {
        $payment_ids = $request->input("ids");
        if (empty($payment_ids)) {
            return $this->setError("请传递付款申请单ids");
        }
        $payment_ids = explode(",", $payment_ids);
        $payment_ids = array_filter($payment_ids);
        $payment_list = PaymentModel::getPaymentsByIdsAndStatus($payment_ids, PaymentModel::STATUS_TO_AUDIT);
        if (count($payment_list) !== count($payment_ids)) {
            return $this->setError("付款申请单状态必须全部为待提审");
        }

        $res = PaymentModel::updateByIds(["status" => PaymentModel::STATUS_DEL], $payment_ids);
        if ($res) {
            foreach ($payment_ids as $payment_id) {
                $payment_info = PaymentModel::getPaymentById($payment_id);
                if ($payment_info) {
                    //如果作废，那么需要更新应付单状态
                    // 删除后，如果有退款申请单，需要把erp关联d设置为空，这样下一次申请退款才不受影响
                    $paymeny_item_list = PaymentItemsModel::getListByPaymentId($payment_info['payment_id']);
                    foreach ($paymeny_item_list as $payment_item) {
                        if (in_array($payment_item['payment_type'], [PaymentItemsModel::PAY_TYPE_UN_PRE_PAYMENT, PaymentItemsModel::PAY_TYPE_UN_PUR_PAYMENT])) {
                            PaymentItemsModel::updateById(['related_id' => "", "related_sn" => ""], $payment_item['payment_item_id']);
                        }
                    }

                    ActionLogService::addLog(ActionLogService::TYPE_ACTION_PAYMENT_DELETE, $payment_id, ["message" => "删除付款申请单"]);
                    //如果作废，那么需要更新应付单状态
                    if ($payment_info['payment_type'] == PaymentModel::PAYMENT_TYPE_PAYABLE) {
                        $paymeny_item_list = PaymentItemsModel::getListByPaymentId($payment_info['payment_id']);
                        if ($paymeny_item_list) {
                            $QueueModel = new RabbitQueueModel();
                            $payable_ids = array_column($paymeny_item_list, 'payable_id');
                            $QueueModel->insertQueue("/queue/payable/checkStatus", [
                                "payable_ids" => implode(",", $payable_ids),
                            ], RabbitQueueModel::QUEUE_PUR_PAYABLE);
                        }
                    }
                }
            }

            return $this->setSuccess("删除成功");
        } else {
            return $this->setSuccess("删除失败");
        }
    }

    // 付款申请单作废
    public function closePayment(Request $request)
    {
        $params = [
            'id'             => $request->input("id"),
            'disable_reason' => $request->input("disable_reason")
        ];
        $validator = Validator::make($params, [
            'id'             => 'required|numeric',
            'disable_reason' => 'required|string',
        ]);
        if ($validator->fails()) {
            $errors = $validator->errors()->toArray();
            $err_msg = ValidatorMsg::getMsg($errors);
            return $this->setError($err_msg);
        }

        $payment_info = PaymentModel::getPaymentById($params['id']);
        if (empty($payment_info)) {
            return $this->setError("查询付款申请单信息失败");
        }

        if (!in_array($payment_info['status'], [PaymentModel::STATUS_TO_AUDIT, PaymentModel::STATUS_AUDIT, PaymentModel::STATUS_WAIT_PAY])) {
            return $this->setError("当前状态不能作废");
        }

        // 如果金蝶有下游单据，那么不能删除; 否则作废单据的同时，需要删除金蝶对应单据
        if ($this->hasErpRela($payment_info['payment_id'])) {
            return $this->setError("已有下游单据，不能作废");
        } else {
            if (in_array($payment_info['status'], [PaymentModel::STATUS_AUDIT, PaymentModel::STATUS_WAIT_PAY])) {
                $is_del = PaymentService::delErpPayment($payment_info['payment_id']);
                if (!$is_del) {
                    return $this->setError("erp删除付款申请单失败，不能作废");
                }
                if (!empty($payment_info['erp_bp_sn'])) {
                    PaymentService::delErpBPPayment($payment_info['erp_bp_sn'], 1);
                }
            }
        }

        $res = PaymentModel::updateById([
            "disable_reason" => $params['disable_reason'],
            "status"         => PaymentModel::STATUS_COLSE
        ], $params['id']);
        if ($res) {
            ActionLogService::addLog(ActionLogService::TYPE_ACTION_PAYMENT_CLOSE, $params['id'], ["message" => "关闭付款申请单"]);

            // 如果付款申请单状态为待审核，那么需要删除审核单
            if ($payment_info['status'] == PaymentModel::STATUS_AUDIT) {
                ApproveService::delApproveByOriginId($payment_info['payment_id'], ApproveService::APPROVE_TYPE_PAY);
            }

            //如果作废，那么需要更新应付单状态
            // 作废后，如果有退款申请单，需要把erp关联d设置为空，这样下一次申请退款才不受影响
            $paymeny_item_list = PaymentItemsModel::getListByPaymentId($payment_info['payment_id']);
            foreach ($paymeny_item_list as $payment_item) {
                if (in_array($payment_item['payment_type'], [PaymentItemsModel::PAY_TYPE_UN_PRE_PAYMENT, PaymentItemsModel::PAY_TYPE_UN_PUR_PAYMENT])) {
                    PaymentItemsModel::updateById(['related_id' => "", "related_sn" => ""], $payment_item['payment_item_id']);
                }
            }

            if ($payment_info['payment_type'] == PaymentModel::PAYMENT_TYPE_PAYABLE) {
                if ($paymeny_item_list) {
                    $QueueModel = new RabbitQueueModel();
                    $payable_ids = array_column($paymeny_item_list, 'payable_id');
                    $QueueModel->insertQueue("/queue/payable/checkStatus", [
                        "payable_ids" => implode(",", $payable_ids),
                    ], RabbitQueueModel::QUEUE_PUR_PAYABLE);
                }
            }
            return $this->setSuccess("作废成功");
        } else {
            return $this->setError("作废失败");
        }
    }

    // erp 取消付款(反审核)付款单
    public function erpUnPayPayment(Request $request)
    {
        $params = [
            'payment_id' => $request->input("payment_id"),
        ];
        $validator = Validator::make($params, [
            'payment_id' => 'required|numeric',
        ]);
        if ($validator->fails()) {
            $errors = $validator->errors()->toArray();
            $err_msg = ValidatorMsg::getMsg($errors);
            return $this->setError($err_msg);
        }

        $payment_info = PaymentModel::getPaymentById($params['payment_id']);
        if (empty($payment_info)) {
            return $this->setError("查询付款申请单信息失败");
        }

        $res = PaymentModel::updateById([
            "status" => PaymentModel::STATUS_TO_AUDIT,
        ], $params['payment_id']);

        $updateData = [
            'amount_paid'         => 0,
            "kingdee_pay_item_id" => "",//金蝶付款单明细id
        ];
        PaymentItemsModel::updateByPaymentId($params['payment_id'], $updateData);
        $QueueModel = new RabbitQueueModel();
        if ($res) {
            ActionLogService::addLog(ActionLogService::TYPE_ACTION_PAYMENT_APPROVE, $payment_info['payment_id'], ["message" => "erp反审核付款单"]);
            $payment_item_list = PaymentItemsModel::getListByPaymentId($payment_info['payment_id']);
            //需要更新应付单状态
            if ($payment_info['payment_type'] == PaymentModel::PAYMENT_TYPE_PAYABLE) {
                if ($payment_item_list) {
                    $QueueModel = new RabbitQueueModel();
                    $payable_ids = array_column($payment_item_list, 'payable_id');
                    $QueueModel->insertQueue("/queue/payable/checkStatus", [
                        "payable_ids" => implode(",", $payable_ids),
                    ], RabbitQueueModel::QUEUE_PUR_PAYABLE);
                }
            }
            $purchaseIdArr = array_column($payment_item_list, 'purchase_id');

            // 采购单更新付款状态
            if (!empty($purchaseIdArr)) {
                $QueueModel->insertQueue("/sync/purOrder/checkPayStatusCausePay", [
                    "pur_ids" => implode(",", $purchaseIdArr),
                ]);
            }
            return $this->setSuccess("改为待付款成功");
        } else {
            return $this->setError("改为待付款失败");
        }
    }

    private function hasErpRela($payment_id)
    {
        $data = SoapRequester::request(SoapRequester::SOAP_NAME_ERP, "deleteOrder", ["TYPE" => 9, "NUMBER" => $payment_id]);
        return ($data && isset($data['is_rela']) && $data['is_rela']) ? true : false;
    }

    // 付款申请单绑定erp
    public function bindErp(Request $request)
    {
        $params = [
            'payment_id'     => $request->input("payment_id"),
            'erp_payment_sn' => $request->input("erp_payment_sn"),
            'item_bind_list' => $request->input("item_bind_list"),
        ];
        $validator = Validator::make($params, [
            'payment_id'                   => 'required|numeric',
            'erp_payment_sn'               => 'required|string',
            'item_bind_list.*.item_id'     => 'required',
            'item_bind_list.*.erp_item_id' => 'required'
        ]);
        if ($validator->fails()) {
            $errors = $validator->errors()->toArray();
            $err_msg = ValidatorMsg::getMsg($errors);
            return $this->setError($err_msg);
        }

        $payment_info = PaymentModel::getPaymentById($params['payment_id']);
        if ($payment_info) {
            PaymentModel::updateById(["erp_payment_sn" => $params['erp_payment_sn']], $payment_info['payment_id']);
        } else {
            return $this->setError("绑定失败，付款申请单信息查询失败");
        }

        $item_bind_list_arr = $params['item_bind_list'];
        if ($item_bind_list_arr && is_array($item_bind_list_arr)) {
            foreach ($item_bind_list_arr as $item_bind_info) {
                if (isset($item_bind_info['item_id']) && isset($item_bind_info['erp_item_id'])) {
                    $payment_item_info = PaymentItemsModel::getItemById($item_bind_info['item_id']);
                    if ($payment_item_info) {
                        PaymentItemsModel::updateById([
                            'related_id' => $item_bind_info['erp_item_id'],
                            'related_sn' => $item_bind_info['erp_item_id']
                        ], $payment_item_info['payment_item_id']);
                    } else {
                        return $this->setError("绑定失败，付款申请单明细信息查询失败");
                    }
                }
            }
        }
        return $this->setSuccess("绑定成功");
    }

    // 退款申请单绑定erp  bp单号
    public function bindErpBpSn(Request $request)
    {
        $params = [
            'erp_bp_sn'  => $request->input("erp_bp_sn"),
            'payment_sn' => $request->input("payment_sn"),
        ];
        $validator = Validator::make($params, [
            'erp_bp_sn'  => 'required|string',
            'payment_sn' => 'required|string',
        ]);
        if ($validator->fails()) {
            $errors = $validator->errors()->toArray();
            $err_msg = ValidatorMsg::getMsg($errors);
            return $this->setError($err_msg);
        }

        $payment_info = PaymentModel::getPaymentBySn($params['payment_sn']);
        if ($payment_info) {
            PaymentModel::updateById(["erp_bp_sn" => $params['erp_bp_sn']], $payment_info['payment_id']);
        } else {
            return $this->setError("绑定失败，付款申请单信息查询失败");
        }
        return $this->setSuccess("绑定成功");
    }


    // 更新已付金额
    public function updatePaidAmount(Request $request)
    {
        $params = [
            'paid_amount_list' => $request->input("paid_amount_list"),
        ];
        $validator = Validator::make($params, [
            'paid_amount_list'               => 'required|array',
            'paid_amount_list.*.erp_item_id' => 'required',
            'paid_amount_list.*.pay_item_id' => 'string',
            'paid_amount_list.*.amount_paid' => 'required|numeric',
        ]);
        if ($validator->fails()) {
            $errors = $validator->errors()->toArray();
            $err_msg = ValidatorMsg::getMsg($errors);
            return $this->setError($err_msg);
        }

        $QueueModel = new RabbitQueueModel();
        $payable_ids = [];

        foreach ($params['paid_amount_list'] as $paid_amount_info) {
            // 如果金额大于0，那么是付款；如果小于0，那么是退款
            if (floatval($paid_amount_info['amount_paid']) > 0) {
                $payment_item_info = PaymentItemsModel::getItemByErpIdAndPaymentTypes($paid_amount_info['erp_item_id'], $paid_amount_info['amount_paid'], [
                    PaymentItemsModel::PAY_TYPE_PRE_PAYMENT,
                    PaymentItemsModel::PAY_TYPE_PUR_PAYMENT
                ]);
            } else {
                $payment_item_info = PaymentItemsModel::getItemByErpIdAndPaymentTypes($paid_amount_info['erp_item_id'], $paid_amount_info['amount_paid'], [
                    PaymentItemsModel::PAY_TYPE_UN_PRE_PAYMENT,
                    PaymentItemsModel::PAY_TYPE_UN_PUR_PAYMENT
                ]);
            }

            if ($payment_item_info) {
                $updateData = [
                    'amount_paid'         => $paid_amount_info['amount_paid'],
                    "kingdee_pay_item_id" => $paid_amount_info['pay_item_id'] ?? "",//金蝶付款单明细id
                ];
                //如果是退款,那么需要更新原来订单的退款金额
                if (floatval($paid_amount_info['amount_paid']) < 0 && $payment_item_info['payable_item_id'] == 0) {
                    //获取原来的订单
                    //退款逻辑
                    //优先根据item_info->refund_value获取到数据
                    $refund_value = $payment_item_info['refund_value'];
                    $refund_value = $refund_value ? json_decode($refund_value, true) : [];
                    if (empty($refund_value)) {
                        $refund_value = PaymentService::getRefundValueByRefundInfo($payment_item_info);
                        $refund_value = $refund_value ? json_decode($refund_value, true) : [];
                    }
                    if (empty($refund_value)) {
                        return $this->setError("退款的付款申请单信息查询失败:{$payment_item_info['related_id']}");
                    }

                    PaymentItemsModel::updateById([
                        "refund_amount" => DB::raw("refund_amount + " . abs($paid_amount_info['amount_paid'])),
                    ], $refund_value['payment_item_id']);
                }
                PaymentItemsModel::updateById($updateData, $payment_item_info['payment_item_id']);
                // 如果是应付单，那么需要更新应付单的已付款金额
                if ($payment_item_info['payable_item_id']) {
                    // 更新应付单状态
                    array_push($payable_ids, $payment_item_info['payable_id']);
                }

                // 采购单更新付款状态
                if ($payment_item_info['purchase_id']) {
                    $QueueModel->insertQueue("/sync/purOrder/checkPayStatusCausePay", [
                        "pur_ids" => $payment_item_info['purchase_id'],
                    ], RabbitQueueModel::QUEUE_PUR_PAYABLE);
                }

                // 更新付款申请单状态
                $QueueModel->insertQueue("/queue/payment/checkStatusCausePay", [
                    "payment_id" => $payment_item_info['payment_id'],
                ], RabbitQueueModel::QUEUE_PUR_PAYMENT);

                // 更新付款申请单主单付款时间
                PaymentModel::updateById(['pay_time' => time()], $payment_item_info['payment_id']);
            }
        }

        $payable_ids = array_unique(array_filter($payable_ids));
        $payable_ids = array_values($payable_ids);
        if ($payable_ids) {
            // 更新应付单状态
            $QueueModel->insertQueue("/queue/payable/checkStatus", [
                "payable_ids" => implode(",", $payable_ids),
            ], RabbitQueueModel::QUEUE_PUR_PAYABLE);
        }
        return $this->setSuccess("successful.");
    }

    /**
     * 更新付款单附件
     * updateUploadFiles
     * @param Request $request
     * @param         $upload_file_ids
     * @param         $payment_id
     * <AUTHOR>
     * Time: 9:40 上午
     */
    public function updateUploadFiles(Request $request)
    {
        // 检验请求参数
        $params = [
            'payment_id'      => $request->input("payment_id"),
            'upload_file_ids' => $request->input("upload_file_ids"),
        ];
        if (empty($params['payment_id'])) {
            return $this->setError("请传递payment_id");
        }
        $payment_info = PaymentModel::getPaymentById($params['payment_id']);
        if (empty($payment_info)) {
            return $this->setError("付款单数据不存在");
        }
        if ($payment_info['status'] != PaymentModel::STATUS_TO_AUDIT) {
            return $this->setError("付款单状态不是待提审");
        }
        PaymentModel::updateById(["upload_file_ids" => $params['upload_file_ids']], $params['payment_id']);
        return $this->setSuccess("successful.");
    }

    // 数据同步到erp
    public function asyncToErp(Request $request)
    {
        $payment_id = $request->input("payment_id");
        if (empty($payment_id)) {
            return $this->setError("请传递payment_id");
        }
        $payment_info = PaymentModel::getPaymentById($payment_id);
        if (empty($payment_info)) {
            return $this->setError("付款申请单信息查询失败");
        }
        //先sleep 20秒,等待审核日志完成(为什么不改审核日志?因为太难改了app/Http/Controllers/ApproveController.php:1208)
        sleep(20);
        $ENTRYS = [];       // 付款
        $UN_ENTRYS = [];    // 退款
        $payment_item_list = PaymentItemsModel::getListByPaymentId($payment_id);
        $purchase_ids = array_column($payment_item_list, 'purchase_id');
        $contractFileIdMap = PurContractService::getContractFileIdMapByPurIds($purchase_ids);

        if ($payment_info['payment_type'] == PaymentModel::PAYMENT_TYPE_PUR) {
            // 采购单
            $purchase_item_ids = array_column($payment_item_list, 'purchase_item_id');
            $purchase_item_map = PurOrderService::getPurchaseItemsMap($purchase_item_ids);
            $purchase_map = PurOrderService::getPurchaseMap($purchase_ids);
            foreach ($payment_item_list as $item_info) {
                if ($item_info['payment_type'] == PaymentItemsModel::PAY_TYPE_UN_PRE_PAYMENT) {
                    //退款逻辑
                    //优先根据item_info->refund_value获取到数据
                    $refund_value = $item_info['refund_value'];
                    $refund_value = $refund_value ? json_decode($refund_value, true) : [];
                    if (empty($refund_value)) {
                        $refund_value = PaymentService::getRefundValueByRefundInfo($item_info);
                        $refund_value = $refund_value ? json_decode($refund_value, true) : [];
                    }
                    if (empty($refund_value)) {
                        return $this->setError("退款的付款申请单信息查询失败:{$item_info['related_id']}");
                    }

                    // 退采购付款
                    $UN_ENTRYS[] = [
                        "ERPENTRYID" => (string)$item_info['related_id'],
                        "AMOUNT"     => (string)$item_info['payment_amount'],
                    ];
                } else {
                    $ENTRYS[] = [
                        "ERPBILLID"  => (string)(isset($purchase_map[$item_info['purchase_id']])) ? $purchase_map[$item_info['purchase_id']]['erp_purchase_id'] : 0,
                        "ERPENTRYID" => (string)(isset($purchase_item_map[$item_info['purchase_item_id']])) ? $purchase_item_map[$item_info['purchase_item_id']]['erp_purchase_item_id'] : 0,
                        "AMOUNT"     => (string)$item_info['payment_amount'],
                        "FILEID"     => $contractFileIdMap[$item_info['purchase_id']] ?? [],
                        "PYITEMID"   => (string)$item_info['payment_item_id']
                    ];
                }
            }
        } else {
            // 应付单
            $payable_ids = array_column($payment_item_list, 'payable_id');
            $payable_item_ids = array_column($payment_item_list, 'payable_item_id');
            $payable_map = PayableService::getPayableMap($payable_ids);
            $payable_item_map = PayableService::getPayableItemsMap($payable_item_ids);
            foreach ($payment_item_list as $item_info) {
                $ENTRYS[] = [
                    "ERPBILLID"  => (string)(isset($payable_map[$item_info['payable_id']])) ? $payable_map[$item_info['payable_id']]['erp_payable_id'] : 0,
                    "ERPENTRYID" => (string)(isset($payable_item_map[$item_info['payable_item_id']])) ? $payable_item_map[$item_info['payable_item_id']]['erp_payable_item_id'] : 0,
                    "AMOUNT"     => (string)$item_info['payment_amount'],
                    "FILEID"     => $contractFileIdMap[$item_info['purchase_id']] ?? "",
                    "PYITEMID"   => (string)$item_info['payment_item_id']
                ];
            }
        }
        // 付款
        if ($ENTRYS) {
            $user_info = CmsUserInfoModel::getUserInfoById($payment_info['create_uid']);
            $supplier_receipt = SupplierReceiptModel::getReceiptById($payment_info['receipt_id']);
            $reviewProcess = [];
            $action_log_list = ActionLogService::getLogsByObjId(ActionLogService::TYPE_PAYMENT, $payment_info['payment_id'], [
                ActionLogService::TYPE_ACTION_PAYMENT_APPROVE,
                ActionLogService::TYPE_ACTION_PAYMENT_CLOSE
            ]);
            foreach ($action_log_list as $action_log) {
                $reviewProcess[] = [
                    "operator"    => $action_log['create_name'],
                    "operateDate" => $action_log['create_time'],
                    "operate"     => $action_log['log_data']['message'],
                ];
            }

            $queue_data = [
                "TYPE"               => (string)$payment_info['payment_type'],
                "PTNUMBER"           => (string)$payment_info['payment_id'],
                "APPLYER"            => ($user_info) ? $user_info['code_sn'] : "",
                "BANKCHARGES"        => (string)$payment_info['bank_charges'],
                "CHARGES"            => (string)$payment_info['extra_fees'],
                "ISVIRTUAL"          => (string)$payment_info['is_virtual'],
                "ISURHENT"           => (string)$payment_info['is_urgent'],
                "BANK_NAME"          => (string)$supplier_receipt['bank_adderss'] ?? "",
                "BANK_ID"            => (string)$supplier_receipt['receipt_id'] ?? "",
                "BANK_ACCOUNT_NO"    => (string)$supplier_receipt['account_no'] ?? "",
                "BANK_SUPPLIER_NAME" => (string)$supplier_receipt['bank_name'],//开户行,
                "ENTRYS"             => $ENTRYS,
                "reviewProcess"      => $reviewProcess,
                "DES"                => $payment_info['remark'],//备注,传开户行 ********,改为传备注
                "__search_key"       => $payment_info['payment_sn']
            ];
            $QueueModel = new RabbitQueueModel();
            $QueueModel->insertQueue("createPayRequestBill", $queue_data, RabbitQueueModel::QUEUE_ERP, RabbitQueueModel::FORWARD_TYPE_SOAP);

            // 记录同步日志
            ActionLogService::addSyncLog(ActionLogService::TYPE_ACTION_PAYMENT_SYNC_CREATE, $payment_id, "同步付款申请单到erp");
        }

        // 退款
        if ($UN_ENTRYS) {
            $queue_data = [
                "TYPE"         => (string)PaymentService::ERP_REFUND_TYPE,
                "ENTRYS"       => $UN_ENTRYS,
                "refundType"   => ($payment_info['payment_type'] == PaymentModel::PAYMENT_TYPE_PUR) ? "1" : "2",
                "PTNUMBER"     => $payment_info['payment_sn'],
                "__search_key" => $payment_info['payment_sn'],
                "DES"          => $payment_info['remark'],//备注,传开户行 ********,改为传备注
                "ISVIRTUAL"    => (string)$payment_info['is_virtual'],
                "ISURHENT"     => (string)$payment_info['is_urgent'],
            ];
            //                        var_dump($queue_data);
            //                        die;
            $QueueModel = new RabbitQueueModel();
            $QueueModel->insertQueue("updateOrderStatus", $queue_data, RabbitQueueModel::QUEUE_ERP, RabbitQueueModel::FORWARD_TYPE_SOAP);

            // 记录同步日志
            ActionLogService::addSyncLog(ActionLogService::TYPE_ACTION_PAYMENT_SYNC_CREATE, $payment_id, "同步付款申请单-退款到erp");
        }

        return $this->setSuccess("successful.");
    }

    // 同步到erp生成付款单
    public function asyncToErpPayment(Request $request)
    {
        $payment_id = $request->input("payment_id");
        if (empty($payment_id)) {
            return $this->setError("请传递payment_id");
        }
        $payment_info = PaymentModel::getPaymentById($payment_id);
        if (empty($payment_info)) {
            return $this->setError("付款申请单信息查询失败");
        }

        $ENTRYS = [];       // 付款
        $payment_item_list = PaymentItemsModel::getListByPaymentId($payment_id);
        foreach ($payment_item_list as $item_info) {
            $ENTRYS[] = [
                "ERP_PAYENTRYID" => (string)$item_info['related_sn'],
                "RECID"          => (string)$item_info['payment_item_id'],
            ];
        }
        // 付款
        if ($ENTRYS) {
            $queue_data = [
                "TYPE"      => "24",
                "PTID"      => (string)$payment_info['payment_id'] ?? "",
                "BANK_ID"   => (string)$payment_info['receipt_id'] ?? "",
                "ISVIRTUAL" => (string)$payment_info['is_virtual'],
                "ISURHENT"  => (string)$payment_info['is_urgent'],
                "ENTRYS"    => $ENTRYS,
            ];
            $QueueModel = new RabbitQueueModel();
            $QueueModel->insertQueue("updateOrderStatus", $queue_data, RabbitQueueModel::QUEUE_ERP, RabbitQueueModel::FORWARD_TYPE_SOAP);

            // 记录同步日志
            ActionLogService::addSyncLog(ActionLogService::TYPE_ACTION_PAYMENT_SYNC_CREATE, $payment_id, "同步付款单到erp");
        }

        return $this->setSuccess("successful.");
    }

    // 检测付款状态，付款的时候，都要检测付款状态；如果已付款金额=申请金额，那么状态为完成，否则保持原有不变
    public function checkStatusCausePay(Request $request)
    {
        $payment_id = $request->input("payment_id");
        if (empty($payment_id)) {
            return $this->setError("请传递付款申请单id");
        }

        $payment_info = PaymentModel::getPaymentById($payment_id);
        if ($payment_info && is_array($payment_info)) {
            $paid_amount = PaymentService::getPaidAmountByPaymentId($payment_info['payment_id']);
            // 已全部付款，如果未全部付款，同时状态为已完成的话，重新更新为待付款
            if (bccomp($paid_amount, $payment_info['payment_amount_total'], 2) == 0) {
                PaymentModel::updateById(['status' => PaymentModel::STATUS_FINISH], $payment_info['payment_id']);
            } else {
                if ($payment_info['status'] == PaymentModel::STATUS_FINISH) {
                    PaymentModel::updateById(['status' => PaymentModel::STATUS_WAIT_PAY], $payment_id);
                    Log::warning("payment_id:{$payment_id} status is finish,but reset to wait_pay");
                }
            }
        }
        return $this->setSuccess("successful.");
    }

    //获取应付款列表
    public function getPaymentList(Request $request)
    {
        $params = [
            'payment_sn'      => $request->input("payment_sn"),
            'purchase_sn'     => $request->input("purchase_sn"),
            'supplier_name'   => $request->input("supplier_name"),
            'sign_company_id' => $request->input("sign_company_id"),
            'create_name'     => $request->input("create_name"),
            'purchase_name'   => $request->input("purchase_name"),
            'erp_payment_sn'  => $request->input("erp_payment_sn"),
            'is_virtual'      => $request->input("is_virtual"),//是否为虚拟付款
            'status'          => $request->input("status"),    //单据状态
            'page'            => $request->input("page"),
            'limit'           => $request->input("limit"),
        ];

        $create_time = $request->input("create_time");
        if ($create_time) {
            $time_range_info = explode("~", $create_time);
            $params['start_create_time'] = $time_range_info[0];
            $params['end_create_time'] = $time_range_info[1];
        }
        $pay_time = $request->input("pay_time");
        if ($pay_time) {
            $time_range_info = explode("~", $pay_time);
            $params['start_pay_time'] = $time_range_info[0];
            $params['end_pay_time'] = $time_range_info[1];
        }
        $review_time = $request->input("review_time");
        if ($review_time) {
            $time_range_info = explode("~", $review_time);
            $params['start_review_time'] = $time_range_info[0];
            $params['end_review_time'] = $time_range_info[1];
        }

        $validator = Validator::make($params, [
            'page'  => 'required|numeric',
            'limit' => 'required|numeric',
        ]);
        if ($validator->fails()) {
            $errors = $validator->errors()->toArray();
            $err_msg = ValidatorMsg::getMsg($errors);
            return $this->setError($err_msg);
        }

        $where = self::buildWhere($params);

        // 权限控制，默认只能查看自己的，有查看全部和查看下级的权限
        $purchase_uids = $this->getPermPurchaseIds();

        $rela_payment_ids = [];
        // 根据采购单号，查询关联的付款申请单; 如果没有关联的付款申请单，返回空
        if ($params['purchase_sn']) {
            $rela_payment_ids = PaymentService::getRelaPaymentIdsByPurchaseSn($params['purchase_sn']);
            if (empty($rela_payment_ids)) {
                return $this->setSuccessData(['list' => [], 'total' => 0]);
            }
        }

        $payment_list = PaymentModel::getListByWhere($where, $params['page'], $params['limit'], $purchase_uids, $rela_payment_ids);

        if ($payment_list['data'] && is_array($payment_list['data'])) {
            $payment_ids = array_column($payment_list['data'], 'payment_id');
            $approve_list = PurchaseApproveModel::getApproveListByOriginIdsAndType($payment_ids, PurchaseApproveModel::APPROVE_TYPE_PAY);
            $approve_origin_id_map = array_column($approve_list, null, 'origin_id');
            $user_name_map = [];
            if ($approve_list) {
                $audit_uids = array_column($approve_list, 'curr_audit_uid');
                $user_name_map = UserService::getCmsUserNameMap($audit_uids);
            }
            $supplierMap = SupplierService::getSupplierBySupplierIds(array_column($payment_list['data'], 'supplier_id'));

            $payment_item_list = PaymentItemsModel::getListByPaymentIds(array_column($payment_list['data'], 'payment_id'));
            $qc_result_map = PaymentService::getPaymentItemListStockInQcResult($payment_item_list);

            $auditList = AuditPaymentService::getAuditList($payment_ids);
            foreach ($payment_list['data'] as &$payment) {
                $payment['status_val'] = NameConvert::getPaymentStatusName($payment['status']);
                $payment['create_time'] = NameConvert::getDateTime($payment['create_time']);
                $payment['pay_time'] = NameConvert::getDateTime($payment['pay_time']);
                $payment['review_time'] = NameConvert::getDateTime($payment['review_time']);
                $payment['currency_val'] = NameConvert::getCurrencyCode($payment['currency']);
                $payment['payment_all_amount_total'] = NameConvert::getFmtPrice($payment['payment_amount_total'] + $payment['bank_charges'] + $payment['extra_fees'], $payment['currency']);
                $payment['payment_amount_total'] = NameConvert::getFmtPrice($payment['payment_amount_total'], $payment['currency']);
                $payment['bank_charges'] = NameConvert::getFmtPrice($payment['bank_charges'], $payment['currency']);
                $payment['payment_type_val'] = NameConvert::getPaymentTypeName($payment['payment_type']);
                $payment['curr_audit_name'] = "";
                $payment['supplier_group_name'] = $supplierMap[$payment['supplier_id']]['supplier_group_name'] ?? '';
                $payment['is_qc'] = $qc_result_map[$payment['payment_id']]['is_qc'] ?? "";
                $payment['qc_result'] = $qc_result_map[$payment['payment_id']]['qc_result'] ?? "";
                $payment['qc_qualified'] = $qc_result_map[$payment['payment_id']]['qc_qualified'] ?? 1; // 质检是否合格：1合格，2不合格
                $payment['bank_pay_status_name'] = PaymentModel::BANK_PAY_STATUS_MAP[$payment['bank_pay_status']] ?? "";

                // 获取审核信息
                $payment['audit_info'] = $auditList[$payment['payment_id']] ?? [];
                $payment['audit_status'] = $payment['audit_info']['approval_status'] ?? 0;
                $payment['curr_audit_name'] = $payment['audit_info']['current_node_info']['approval_names'] ?? "";
            }
        }

        $data = [
            "list"  => $payment_list['data'],
            "total" => isset($payment_list['total']) ? $payment_list['total'] : 0
        ];
        return $this->setSuccessData($data);
    }

    // 获取付款申请单明细列表
    public function getPaymentItems(Request $request)
    {
        $params = [
            'payment_id' => $request->input("payment_id"),
            'page'       => $request->input("page"),
            'limit'      => $request->input("limit"),
        ];
        $validator = Validator::make($params, [
            'payment_id' => 'required|numeric',
            'page'       => 'required|numeric',
            'limit'      => 'required|numeric',
        ]);
        if ($validator->fails()) {
            $errors = $validator->errors()->toArray();
            $err_msg = ValidatorMsg::getMsg($errors);
            return $this->setError($err_msg);
        }

        $where = [
            "payment_id" => $params['payment_id']
        ];
        $payment_item_list = PaymentItemsModel::getListByWhere($where, $params['page'], $params['limit']);
        if ($payment_item_list['data'] && is_array($payment_item_list['data'])) {
            $payment_info = PaymentModel::getPaymentById($params['payment_id']);
            // 如果是应付单，还要展示付款方式；付款方式是关联应付单查询
            $payable_item_map = [];
            $payable_map = [];
            $purchase_map = [];
            if ($payment_info['payment_type'] == PaymentModel::PAYMENT_TYPE_PAYABLE) {
                $payable_item_ids = array_column($payment_item_list['data'], 'payable_item_id');
                $payable_item_map = PayableService::getPayableItemsMap($payable_item_ids);
                $payable_ids = array_column($payment_item_list['data'], 'payable_id');
                $payable_map = PayableService::getPayableMap($payable_ids);
            } else {
                $purchase_ids = array_column($payment_item_list['data'], 'purchase_id');
                $purchase_map = PurOrderService::getPurchaseMap($purchase_ids);
            }

            foreach ($payment_item_list['data'] as &$payment_item) {
                $payment_item['payment_type_name'] = NameConvert::getPaymentPayTypeName($payment_item['payment_type']);
                $payment_item['no_pay_amount'] = NameConvert::getNoSignFmtPrice(($payment_item['payment_amount'] - $payment_item['amount_paid']));
                $payment_item['sale_amount'] = NameConvert::getNoSignFmtPrice(self::getSaleAmount($payment_info['company_name'], $payment_item));
                $payment_item['pay_name'] = isset($payable_item_map[$payment_item['payable_item_id']]) ? $payable_item_map[$payment_item['payable_item_id']]['pay_name'] : '预付款';

                if ($payment_info['payment_type'] == PaymentModel::PAYMENT_TYPE_PUR) {
                    $payment_item['rela_sn'] = (isset($purchase_map[$payment_item['purchase_id']])) ? $purchase_map[$payment_item['purchase_id']]['purchase_sn'] : '';
                } else {
                    $payment_item['rela_sn'] = (isset($payable_map[$payment_item['payable_id']])) ? $payable_map[$payment_item['payable_id']]['payable_sn'] : '';
                }
            }
        }

        $data = [
            "list"  => $payment_item_list['data'],
            "total" => isset($payment_item_list['total']) ? $payment_item_list['total'] : 0
        ];
        return $this->setSuccessData($data);
    }

    // 搜索付款申请单用户名
    public function searchCreateName(Request $request)
    {
        $keyword = $request->input('keyword');
        if (empty($keyword)) {
            return $this->setError("请传递搜索用户名");
        }

        $user_list = [];
        $list = PaymentModel::getCreateNames($keyword);
        foreach ($list as $create_name) {
            $user_list[] = [
                "id"   => $create_name,
                "name" => $create_name,
            ];
        }
        $data = [
            "list" => $user_list,
        ];
        return $this->setSuccessData($data);
    }

    // 搜索付款申请单采购用户名
    public function searchPurName(Request $request)
    {
        $keyword = $request->input('keyword');
        if (empty($keyword)) {
            return $this->setError("请传递搜索用户名");
        }

        $user_list = [];
        $list = PaymentModel::getPurchaseNames($keyword);
        foreach ($list as $pur_name) {
            $user_list[] = [
                "id"   => $pur_name,
                "name" => $pur_name,
            ];
        }
        $data = [
            "list" => $user_list,
        ];
        return $this->setSuccessData($data);
    }

    // 搜索付款申请单供应商名
    public function searchSupplierName(Request $request)
    {
        $keyword = $request->input('keyword');
        if (empty($keyword)) {
            return $this->setError("请传递搜索用户名");
        }

        $supplier_list = [];
        $purchase_uids = $this->getPermPurchaseIds();
        $list = PaymentModel::getSupplierNames($keyword, $purchase_uids);
        foreach ($list as $supplier_name) {
            $supplier_list[] = [
                "id"   => $supplier_name,
                "name" => $supplier_name,
            ];
        }
        $data = [
            "list" => $supplier_list,
        ];
        return $this->setSuccessData($data);
    }

    private function buildWhere($params)
    {
        $where = [];
        if (isset($params['payment_sn']) && $params['payment_sn']) {
            $where[] = ['payment_sn', '=', $params['payment_sn']];
        }

        if (isset($params['erp_payment_sn']) && $params['erp_payment_sn']) {
            $where[] = [
                function ($query) use ($params) {
                    $query->where('erp_payment_sn', '=', $params['erp_payment_sn'])->orWhere('erp_bp_sn', '=', $params['erp_payment_sn']);
                }
            ];
        }

        if (isset($params['supplier_name']) && $params['supplier_name']) {
            $where[] = ['supplier_name', '=', $params['supplier_name']];
        }

        if (isset($params['status']) && $params['status']) {
            $where[] = ['status', '=', $params['status']];
        }
        if (isset($params['sign_company_id']) && $params['sign_company_id']) {
            $where[] = ['sign_company_id', '=', $params['sign_company_id']];
        }

        if (isset($params['is_virtual']) && ($params['is_virtual'] != "")) {
            $where[] = ['is_virtual', '=', $params['is_virtual']];
        }

        if (isset($params['create_name']) && $params['create_name']) {
            $where[] = ['create_name', '=', $params['create_name']];
        }

        if (isset($params['purchase_name']) && $params['purchase_name']) {
            $where[] = ['purchase_name', '=', $params['purchase_name']];
        }

        // 创建时间
        if (isset($params['start_create_time']) && $params['start_create_time']) {
            $where[] = ['create_time', '>=', strtotime($params['start_create_time'])];
        }

        if (isset($params['end_create_time']) && $params['end_create_time']) {
            $where[] = ['create_time', '<=', strtotime($params['end_create_time']) + 86399];
        }

        // 付款时间
        if (isset($params['start_pay_time']) && $params['start_pay_time']) {
            $where[] = ['pay_time', '>=', strtotime($params['start_pay_time'])];
        }

        if (isset($params['end_pay_time']) && $params['end_pay_time']) {
            $where[] = ['pay_time', '<=', strtotime($params['end_pay_time']) + 86399];
        }

        // 审核时间
        if (isset($params['start_review_time']) && $params['start_review_time']) {
            $where[] = ['review_time', '>=', strtotime($params['start_review_time'])];
        }

        if (isset($params['end_review_time']) && $params['end_review_time']) {
            $where[] = ['review_time', '<=', strtotime($params['end_review_time']) + 86399];
        }


        return $where;
    }

    // 获取采购用户ids，判断用户权限，默认只能查看自己的付款申请单，如果有查看下级，那么查询下级人的。
    private function getPermPurchaseIds()
    {
        $purchase_uids = [request()->user->userId];
        if (PermService::hasPerm(PermService::PUR_PAYMENT_VIEW_ALL_LIST)) {
            $purchase_uids = [];
        } else {
            if (PermService::hasPerm(PermService::PUR_PAYMENT_VIEW_SUB_LIST)) {
                $purchase_uids = PermService::getSubUserId(request()->user->userId);
            }
        }
        return $purchase_uids;
    }

    /**
     * 同步付款申请单供应商信息
     * syncSupplierData
     * @param Request $request
     * @param         $payment_id
     * <AUTHOR>
     * Time: 9:47 AM
     */
    public function syncSupplierData(Request $request)
    {
        $paymentId = $request->input('payment_id', 0);
        if (empty($paymentId)) {
            return $this->setError('付款申请单id错误');
        }
        try {
            PaymentService::syncSupplierData($paymentId);
        } catch (\Throwable $e) {
            return $this->setError($e->getMessage());
        }
        return $this->setSuccess('同步供应商信息成功');
    }

    /**
     * 同步付款申请单合同
     * syncContractFileId
     * @param Request $request
     * @param         $payment_id
     * <AUTHOR>
     * Time: 9:47 AM
     */
    public function syncContractFileId(Request $request)
    {
        $paymentId = $request->input('payment_id', 0);
        if (empty($paymentId)) {
            return $this->setError('付款申请单id错误');
        }
        try {
            PaymentService::updateContractFileIdByPaymentIdArr([$paymentId]);
        } catch (\Throwable $e) {
            return $this->setError($e->getMessage());
        }
        return $this->setSuccess('同步erp成功');
    }
}
