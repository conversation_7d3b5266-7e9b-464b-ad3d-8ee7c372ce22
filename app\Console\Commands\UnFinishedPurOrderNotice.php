<?php

namespace app\Console\Commands;


use App\Exceptions\InvalidRequestException;
use App\Http\Models\CmsUserDepartmentModel;
use App\Http\Models\CmsUserInfoModel;
use App\Http\Models\FrqModel;
use App\Http\Models\Order\GoodsModel;
use App\Http\Models\PaymentItemsModel;
use App\Http\Models\PurchaseContractModel;
use App\Http\Models\PurchaseItemsModel;
use App\Http\Models\PurchaseOrderEndCustExtendModel;
use App\Http\Models\PurchaseOrderModel;
use App\Http\Models\Qc\QcModel;
use App\Http\Models\ScmOrderModel;
use App\Http\Models\ScmOrderTemporaryModel;
use App\Http\Models\StockInItemModel;
use App\Http\Models\StockInModel;
use App\Http\Models\Wms\WmsStockInItemModel;
use App\Http\Queue\RabbitQueueModel;
use App\Http\Services\ActionLogService;
use App\Http\Services\ApproveService;
use App\Http\Services\EndCustService;
use App\Http\Services\FrqService;
use App\Http\Services\MessageService;
use App\Http\Services\PurOrderService;
use App\Http\Services\ScmOrderService;
use App\Http\Services\SupplierDeliveryService;
use App\Http\Services\SupplierService;
use App\Http\Services\Sync\DeliveryLogSyncOrderService;
use App\Http\Services\Sync\PurchaseOrderSyncService;
use App\Http\Services\Sync\ScmOrderSyncService;
use App\Http\Services\Sync\StockInSyncWMSService;
use App\Http\Services\WarehouseReceiptSnService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;

class UnFinishedPurOrderNotice extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:un_finished_pur_order_notice';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '未完成采购单提醒';


    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle($arguments = [])
    {
        $departmentIdArr = [
            47 => "联营一部",
            51 => "联营三部",
            72 => "联营二部",
        ];
        //        财务主管：<EMAIL> 、财务总监：<EMAIL>
        $financeEmailArr = [
            "<EMAIL>",
            "<EMAIL>",
        ];
        $template = "un_finished_pur_order_notice";
        echo "开始发送未完成采购单提醒邮件\n";
        foreach ($departmentIdArr as $departmentId => $departmentName) {
            //获取部门总监
            $userList = CmsUserInfoModel::getUsersByDepartmentIdAndPositionNames($departmentId, ["采购总监"]);
            $directorEmail = [];
            foreach ($userList as $user) {
                $directorEmail[] = $user['email'];
            }
            $emailArr = array_merge($directorEmail, $financeEmailArr);
            $emailArr = array_unique($emailArr);
            $msg = "链接为{$departmentName}已付款且超过预计交货时间7天未到货入库的采购单明细列表，请下载查收~<br>";
            $msg .= "链接：".config('website.pur_domain')."/sync/purOrder/exportUnfinishedPurOrder?department_id={$departmentId}";
            $title = "邮件为{$departmentName}在预计交期内没有到货的采购明细，请注意到货情况";
            foreach ($emailArr as $email) {
//                $email = "<EMAIL>";
                MessageService::sendMail($template, ["msg" => $msg, "title" => $title], $email);
                echo "发送邮件给{$email}\n";
            }
            self::purchaseUserNotice($departmentId);
        }
        echo "发送未完成采购单提醒邮件结束\n";
    }

    public function purchaseUserNotice($departmentId)
    {
        //采购员通知
        $purchaseUserIdList = CmsUserInfoModel::getSalesByPrefixName("",$departmentId);
        $purchaseUserList = CmsUserInfoModel::getUsers(array_column($purchaseUserIdList, 'id'));
        foreach ($purchaseUserList as $purchaseUser) {
            if (!in_array($purchaseUser['position_name'], [CmsUserInfoModel::PURCHASING_MANAGER, CmsUserInfoModel::PURCHASING_STAFF])) {
                continue;
            }
            $managerList = CmsUserInfoModel::getUsersByDepartmentIdAndPositionNames($purchaseUser['department_id'], [CmsUserInfoModel::PURCHASING_MANAGER]);
//            var_dump($purchaseUser);
            $emailList = [
                $purchaseUser['email']
            ];
            $purchaseUid = $purchaseUser['userId'];
            if ($purchaseUser['position_name'] == CmsUserInfoModel::PURCHASING_STAFF) {
                foreach ($managerList as $manager) {
                    $emailList[] = $manager['email'];
                }
            }
            $title = "邮件为采购员[{$purchaseUser['name']}]在预计交期内没有到货的采购明细，请注意到货情况";
            $msg = "链接为您已付款但未到货入库的采购单明细列表，请查收~<br>";
            $msg .= "链接：".config('website.pur_domain')."/web/purDemand/exportUnfinishedPurOrder?purchase_uid={$purchaseUid}&department_id={$departmentId}";
            foreach ($emailList as $email) {
                //                $email = "<EMAIL>";
                MessageService::sendMail("un_finished_pur_order_notice", ["msg" => $msg, "title" => $title], $email);
                echo "发送采购员的邮件给{$email}\n";
            }
        }

    }


}
