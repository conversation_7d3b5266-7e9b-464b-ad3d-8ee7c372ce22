@include('css')

<style>

</style>


<div class="lay-box">
    <form class="layui-form layui-box" lay-filter="ListForm">
        <div class="layui-form-item">
            <div class="layui-inline">
				<label class="layui-form-label">周：</label>
				<div class="layui-input-inline">
					<select id="week"  lay-filter="week">
						<?php for($i=1;$i<=53;$i++) { ?>
					    	<option value="<?=$i?>" ><?=$i."周"?></option>
                        <?php } ?>
					</select>
				</div>
			</div>
            <div class="layui-inline">
                <label class="layui-form-label">接单时间</label>
                <div class="layui-input-inline ">
                    <input type="text" name="order_time" value="" autocomplete="off"  class="layui-input" id="create_time" readonly>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">采购组：</label>
                <div class="layui-input-inline">
                   <div id="department_option"></div>
                </div>
            </div>
            <div class="layui-inline">
                <div class="layui-input-inline" style="width:110px;">
                    <input type="radio" name="performance_level" value="" title="全部数据">
                </div>
                <div class="layui-input-inline"  style="width:140px;">
                    <input type="radio" name="performance_level" value="0,1,2" title="SKU履约数据" > 
                </div>
            </div>
            <div class="layui-inline">
                <div class="layui-btn layui-btn-sm pur-search" lay-submit lay-filter="formDemo">查询</div>
                <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm czbtn">重置</button>
            </div>
        </div>
    </form>
    <table id="list" lay-filter="list"></table>
</div>


<!-- 工具栏 -->
<script type="text/html" id="toolbar">
    <div class="layui-btn-container">
        <a class="layui-btn layui-btn-sm" lay-event="export">导出</a>
    </div>
</script>

@include('js')
<script>
   let createUserDepartmentList = {!! json_encode($departmentList) !!};
   createUserDepartmentList.forEach(item=>{
       item.value=item.id
       if(item.children&&item.children.length>0){
           item.children.forEach(item1=>{
               item1.value=item1.department_id
           })
       }
   })
</script>
<script type="text/javascript" src="/assets/js/purchaseSalesStatistics/weekReport.js?v={{time()}}"></script>