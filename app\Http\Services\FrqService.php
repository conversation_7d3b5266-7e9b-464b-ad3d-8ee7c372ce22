<?php

namespace App\Http\Services;


use App\Enum\PurCommonEnum;
use App\Exceptions\InvalidRequestException;
use App\Http\Caches\RateCache;
use App\Http\IdSender\IdSender;
use App\Http\Models\CmsUserInfoModel;
use App\Http\Models\EndCustCateModel;
use App\Http\Models\EndCustInfoModel;
use App\Http\Models\FrqModel;
use App\Http\Models\Order\OrderItemsModel;
use App\Http\Models\OriginalPlaceModel;
use App\Http\Models\PaymentItemsModel;
use App\Http\Models\PurchaseContractModel;
use App\Http\Models\PurchaseItemsModel;
use App\Http\Models\PurchaseOrderModel;
use App\Http\Models\PurchasePlanItemsModel;
use App\Http\Models\PurchasePlanModel;
use App\Http\Models\ReturnMaterialItemModel;
use App\Http\Models\ScmOrderModel;
use App\Http\Models\StandardBrandModel;
use App\Http\Models\StockInItemModel;
use App\Http\Models\SupplierChannelModel;
use App\Http\Models\TransferWarehouseOrderItemsModel;
use App\Http\Models\TransferWarehouseOrderModel;
use App\Http\Queue\RabbitQueueModel;
use App\Http\Services\Api\Consignment\ConsignmentOrderService;
use App\Http\Services\Api\EarlyWarningApiService;
use App\Http\Services\Api\Frq\FrqApiService;
use App\Http\Services\Api\Frq\FrqRequestService;
use App\Http\Services\Api\Frq\FrqShareApiService;
use App\Http\Services\Api\LogisticsApiService;
use App\Http\Services\Api\PurContractService;
use App\Http\Services\Api\PurOrderApiService;
use App\Http\Services\Api\TransferWarehouse\TransferOrderApiService;
use App\Http\Services\Sync\CrmService;
use App\Http\Services\Sync\DeliveryLogSyncOrderService;
use App\Http\Services\Sync\MROSyncService;
use App\Imports\FrqImport;
use App\Imports\FrqImportPurOrderAndStock;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Http;

class FrqService extends BaseService
{

    const ASingleProduct = 1;   //一单一品：一条明细生成一个采购单  一条明细生成一个发货通知单
    const MoreSingleProduce = 2;//一单多品   所有明细生成一个采购单  所有明细生成一个发货通知单
    //履约级别：0：弱履约，1：强履约B；2：强履约A -1：无履约
    const STRONG_PERFORMANCE_A = 2;
    const STRONG_PERFORMANCE_B = 1;
    const WEAK_PERFORMANCE = 0;
    const NO_PERFORMANCE = -1;

    const PERFORMANCE_LEVEL_MAP = [
        self::STRONG_PERFORMANCE_A => "强履约A",
        self::STRONG_PERFORMANCE_B => "强履约B",
        self::WEAK_PERFORMANCE     => "弱履约",
        self::NO_PERFORMANCE       => ""
    ];

    public function __construct()
    {
        $this->model = new FrqModel;
    }

    /**
     * Notes:新增采购需求
     * User: sl
     * Date: 2022/5/25 17:24
     * @param $data
     * @return bool
     */
    public function addFrq($data, $lockKey = "")
    {
        $purSupplierMap = self::getSupplierMapBySkuIdArr(array_column($data, 'sku_id'));

        //        if (($_GET['debug']??0)==1){
        //            var_dump($purSupplierMap);
        //        }
        foreach ($data as $k => $v) {
            $purSupplierInfo = $purSupplierMap[(string)($v['sku_id'] ?? "")] ?? [];
            $data[$k]["create_time"] = time();
            $data[$k]["is_add_order"] = 1;
            $data[$k]["sku_id"] = (string)($v['sku_id'] ?? "") ?? "";
            $data[$k]["is_sku_offer"] = $v['is_sku_offer'] ?? FrqModel::IS_SKU_OFFER_NO;
            $data[$k]["frq_sku_id"] = $v['frq_sku_id'] ?? "";
            $data[$k]["frq_supplier_id"] = $v['frq_supplier_id'] ?? "";
            $data[$k]["frq_supplier_list"] = $v['frq_supplier_list'] ?? "";
            $data[$k]["pack_requeire"] = $v['pack_requeire'] ?? "";
            $data[$k]["pur_supplier_id"] = "";
            $data[$k]["pur_supplier_name"] = "";

            if (($_GET['debug'] ?? 0) == 1) {
                var_dump($purSupplierInfo, $data[$k]['supplier_id'], !in_array($data[$k]['company_id'], PurCommonEnum::$HUAYUN_COMPANY));
            }
            if (($data[$k]['supplier_name'] ?? "") == "猎芯专营" && !in_array($data[$k]['company_id'], PurCommonEnum::$HUAYUN_COMPANY) && $data[$k]['performance_level'] != FrqService::NO_PERFORMANCE) {
                $data[$k]["pur_supplier_id"] = $purSupplierInfo['supplier_id'] ?? "";
                $data[$k]["pur_supplier_name"] = $purSupplierInfo['supplier_name'] ?? "";
            }

            //判断需求单是否已经存在 存在就提示不能创建
            if (in_array($v['frq_type'], [FrqModel::FRQ_TYPE_ORDER, FrqModel::FRQ_TYPE_CONSIGNMENT])) {
                $isExists = FrqModel::checkHasOrderItemsId($v["order_item_id"]);
                if ($isExists) {
                    //修改销售明细关联的erp信息
                    $this->model::updateFrqInfo([
                        "order_item_id" => $v["order_item_id"]
                    ], [
                        "erp_order_id" => $v["erp_order_id"],
                        "erp_rec_id"   => $v["erp_rec_id"],
                    ]);
                    unset($data[$k]);
                    continue;
                    //                    throw new InvalidRequestException(sprintf("销售明细%s数据已经存在,请勿重复添加", $v["goods_name"]));
                }
            }
            //判断公司是否不为空
            if (isset($v["com_name"]) && $v["com_name"]) {
                //异步拉取终端公司
                try {
                    CrmService::pullComInfoToLocal(trim($v["com_name"]));
                } catch (\Throwable $e) {
                    \Log::info(sprintf("class FrqService function name addFrq 异步拉取终端公司错误 %s  ", $e->getMessage()));
                }
            }

            //判断是否为寄售类型
            if ($data[$k]['frq_type'] == FrqModel::FRQ_TYPE_CONSIGNMENT) {
                //走寄售需求相关逻辑
                ConsignmentOrderService::addFrqByConsignmentType($data[$k]);
                unset($data[$k]);
            }
            //判断是否为自动转仓类型
            //交货地是大陆+供应商
            if (($data[$k]['supplier_id'] ?? 0) == 1690 && ($data[$k]['delivery_place'] ?? 1) == 1) {
                //走自动转仓需求相关逻辑
                $RabbitQueueModel = new RabbitQueueModel();
                $result = $RabbitQueueModel->insertQueue('/sync/transferWarehouse/addOrderByFrq', [
                    'sku_id'        => (string)$data[$k]['sku_id'] ?? "",
                    'order_item_id' => $data[$k]['order_item_id'],
                    'frq_qty'       => $data[$k]['frq_qty'],
                    'purchase_uid'  => $data[$k]['purchase_uid'],
                    'purchase_name' => $data[$k]['purchase_name'],
                ], RabbitQueueModel::DEFAULT_QUEUE);
                unset($data[$k]);
            }


            //            var_dump($data);
            //判断汇率是否小于等于0
            if (floatval($v["exchange_rate"]) <= 0) {
                unset($data[$k]);
            }
        }
        //        var_dump($data);
        if (!empty($data)) {
            foreach ($data as $k => $item) {
                if ($item["supplier_name"] && in_array(strtolower(trim($item["supplier_name"])), config("config.maoze_supname"))) {
                    $data[$k]["is_add_order"] = 0;
                }
            }
            if (($_GET['debug'] ?? 0) == 1) {
                var_dump($data);
            }
            $bk = $this->model->addFrq($data);
            foreach ($data as $item) {
                if ($item["supplier_name"] && in_array(strtolower(trim($item["supplier_name"])), config("config.maoze_supname"))) {
                    $this->syncReqMaoZeISCanAddOrder($item["order_item_id"], 1, 0);
                }
            }
            return $bk;
        }

        PurLockService::delLockPurAddFrq($lockKey ?? "");

        return true;
    }

    protected function buildgetListQuery($reqParams)
    {
        $query = $this->model->newQuery();
        $where = [];
        //构建左匹配查询
        $buildLeftLikeQueryData["goods_name"] = $reqParams["goods_name"] ?? "";
        $buildLeftLikeQueryData["com_name"] = arrayGet($reqParams, "com_id", null, "trim");

        $buildLeftLikeQueryData["brand_name"] = arrayGet($reqParams, "brand_name", null, "trim");
        //构建等于查询
        $buildEqualQueryData["brand_id"] = arrayGet($reqParams, "brand_id", null, "intval");

        $buildEqualQueryData["company_id"] = arrayGet($reqParams, "company_id", null, "intval");
        $buildEqualQueryData["status"] = arrayGet($reqParams, "status", null, "intval");
        $buildEqualQueryData["seller_uid"] = arrayGet($reqParams, "seller_uid", null, "intval");
        $buildEqualQueryData["purchase_uid"] = arrayGet($reqParams, "purchase_uid", null, "intval");
        $buildEqualQueryData["pm_uid"] = arrayGet($reqParams, "pm_uid", null, "intval");
        $buildEqualQueryData["currency"] = arrayGet($reqParams, "currency", null, "intval");
        $buildEqualQueryData["erp_sn"] = arrayGet($reqParams, "erp_sn", null, "trim");
        $buildEqualQueryData["source_sn"] = arrayGet($reqParams, "source_sn", null, "trim");
        $buildEqualQueryData["sales_order_source"] = arrayGet($reqParams, "sales_order_source", null, "trim");
        $purchase_sn = arrayGet($reqParams, "purchase_sn", null, "trim");
        $buildEqualQueryData["frq_type"] = arrayGet($reqParams, "frq_type", null, "intval");
        $sales_currency = arrayGet($reqParams, "sales_currency", null);
        $is_add_order = arrayGet($reqParams, "is_add_order", null);
        $buildEqualQueryData["supplier_id"] = arrayGet($reqParams, "supplier_id", null, "intval");
        $where["is_delete"] = FrqModel::IS_DELETE_NO;
        //关闭需求单过滤筛选
        if (!empty($reqParams['is_delete'])) {
            $where["is_delete"] = FrqModel::IS_DELETE_YES;
        }
        //delete_uid
        if (!empty($reqParams['delete_uid'])) {
            $where["delete_uid"] = $reqParams['delete_uid'];
        }
        //delete_time
        if (!empty($reqParams['delete_time'])) {
            $time = explode("~", $reqParams['delete_time']);
            $where[] = ["delete_time", ">=", strtotime($time[0])];
            $where[] = ["delete_time", "<=", strtotime($time[1])];
            //            $buildTimeQueryData["delete_time"]["begin_time"] = isset($time[0]) ? $time[0] : "";
            //            $buildTimeQueryData["delete_time"]["end_time"] = isset($time[1]) ? $time[1] : "";
        }
        if (isset($reqParams['performance_level']) && $reqParams['performance_level'] !== "") {
            $where[] = [
                function ($query) use ($reqParams) {
                    $arr = explode(",", $reqParams['performance_level']);
                    $query->whereIn("performance_level", $arr);
                }
            ];
        }


        //时间检索
        $create_time = arrayGet($reqParams, "create_time", null, "trim");
        $purchase_time = arrayGet($reqParams, "purchase_time", null, "trim");

        $buildTimeQueryData = [];
        if ($create_time) {
            $create_time = explode("~", $create_time);
            $buildTimeQueryData["create_time"]["begin_time"] = isset($create_time[0]) ? $create_time[0] : "";
            $buildTimeQueryData["create_time"]["end_time"] = isset($create_time[1]) ? $create_time[1] : "";
        }

        if ($purchase_time) {
            $purchase_time = explode("~", $purchase_time);
            $buildTimeQueryData["purchase_time"]["begin_time"] = isset($purchase_time[0]) ? $purchase_time[0] : "";
            $buildTimeQueryData["purchase_time"]["end_time"] = isset($purchase_time[1]) ? $purchase_time[1] : "";
        }

        $query->exceptField([
            "create_uid",
            "create_name",
            "purchase_plan_id",
            "purchase_id"
        ])->buildLeftLikeQuery($buildLeftLikeQueryData)->buildEqualQuery($buildEqualQueryData)->buildTimeQuery($buildTimeQueryData)->where($where)->rule(FrqModel::$ruleViewList)->whereRaw("if(frq_qty >= after_sales_qty,frq_qty-after_sales_qty > 0,1)")->orderBy("frq_id", "desc");

        if ($sales_currency != "" && in_array($sales_currency, [0, 1, 2, 12, 13])) {
            //            dd($sales_currency);
            $query = $query->where("sales_currency", intval($sales_currency));
        }

        if ($is_add_order != "" && in_array($is_add_order, [-1, 0, 1])) {
            //            dd($sales_currency);
            $query = $query->where("is_add_order", intval($is_add_order));
        }

        if ($purchase_sn) {//采购单号
            $purId = PurchaseOrderModel::getPurOrderIdByPursn(trim($purchase_sn));
            if (!$purId) {
                $purId = -99999;
            }
            $query = $query->whereHasIn("purchaseItem", function ($q) use ($purId) {
                $q->where("purchase_id", $purId)->where("status", PurchaseItemsModel::STATUS_ENABLE);
            });
        }
        return $query;
    }

    /*
     *获取列表数据
     */
    public function getFrqlist($reqParams)
    {
        $page = !empty($reqParams["page"]) ? intval($reqParams["page"]) : 1;
        $limit = !empty($reqParams["limit"]) ? intval($reqParams["limit"]) : static::_LIMIT_;
        $list = $this->buildgetListQuery($reqParams)->getlist($page, $limit);
        $supplierList = (new SupplierService())->getSupplierBySupplierIds(array_column($list->toArray()['data'], "frq_supplier_id"));
        //预处理数据
        foreach ($list as $k => $item) {
            $item->company_name = Arr::get(PurCommonEnum::$COMPANY_LIST, $item->company_id, "");
            $item->status_cn = Arr::get(FrqModel::$STATUS_CN, $item->status, "-");
            $item->frq_type_cn = Arr::get(FrqModel::$FRQ_TYPE, $item->frq_type, "-");
            $item->sales_currency_cn = Arr::get(FrqModel::$SALES_CURRENCY, $item->sales_currency, "-");
            $item->currency_cn = Arr::get(FrqModel::$SALES_CURRENCY, $item->currency, "-");
            $item->frq_supplier_name = $supplierList[$item->frq_supplier_id]['supplier_name'] ?? "";
            if ($item->frq_type == FrqModel::FRQ_TYPE_ORDER || $item->frq_type == FrqModel::FRQ_TYPE_MRO || $item->frq_type == FrqModel::FRQ_TYPE_CONSIGNMENT) {//以销定购
                //销售总价 含税
                $item->sales_total_price = $item->sales_price * $item->frq_qty;
                $item->sales_total_price = decimal_number_format($item->sales_total_price);
                $item->sales_total_price_format = price_format($item->sales_price * $item->frq_qty, $item->sales_currency, 2);
                $item->sales_price_format = price_format($item->sales_price, $item->sales_currency, 6);

                //销售总价  未税
                $item->sales_without_tax_total_price = $item->sales_without_tax_price * $item->frq_qty;
                $item->sales_without_tax_total_price = decimal_number_format($item->sales_without_tax_total_price);
                $item->sales_without_tax_total_price_format = price_format($item->sales_without_tax_price * $item->frq_qty, $item->sales_currency, 2);
                $item->sales_without_tax_price_format = price_format($item->sales_without_tax_price, $item->sales_currency, 6);


                $item->apply_price_cn = "-";      //申请单价
                $item->apply_total_price_cn = "-";//申请单价

                $item->delivery_place = arrayGet(PurCommonEnum::$DELIVERY_PLACE, $item->delivery_place, "");
            } elseif ($item->frq_type == FrqModel::FRQ_TYPE_PLAN || $item->frq_type == FrqModel::FRQ_TYPE_HY) {
                $item->delivery_place = "-";
                $item->sales_total_price = "-";
                $item->sales_total_price_format = "-";
                $item->sales_price_format = "-";

                $item->sales_without_tax_total_price = "-";
                $item->sales_without_tax_total_price_format = "-";
                $item->sales_without_tax_price_format = "-";

                if ($item->currency == PurCommonEnum::CurrencyRMB) {//人明币显示含税
                    $item->apply_price_cn = price_format($item->frq_price_unit * $item->tax_rate, $item->currency, 6);
                    $item->apply_total_price_cn = price_format($item->frq_price_unit * $item->tax_rate * $item->frq_qty, $item->currency, 2);
                } else {//美金显示未税
                    $item->apply_price_cn = price_format($item->frq_price_unit, $item->currency, 6);
                    $item->apply_total_price_cn = price_format($item->frq_price_unit * $item->frq_qty, $item->currency, 2);
                }
            }

            $item->no_procured_qty = $item->frq_qty - $item->procured_qty - $item->after_sales_qty + $item->return_qty;
            if ($item->no_procured_qty <= 0) {
                $item->no_procured_qty = 0;
            }
            $item->default_pur_qty = $item->frq_qty - $item->procured_qty - $item->after_sales_qty + $item->return_qty;
            if ($item->default_pur_qty <= 0) {
                $item->default_pur_qty = 0;
            }
            //无法提取到循环外查询
            $pursn = $this->getPurSnByFrqId($item->frq_id);
            $item->purchase_sn_arr = $pursn;
            $item->purchase_sn = "";
            if ($pursn) {
                $purSnUrlArr = [];
                foreach ($pursn as $id => $sn) {
                    $purSnUrlArr[] = "<a class='alink' ew-href='/web/purDemand/purOrderDetail?id={$id}' ew-title='{$sn}'>$sn</a>";
                }
                $item->purchase_sn = implode(",", $purSnUrlArr);
            }


            //采购需求数量 = 采购需求数量-售后数量
            $item->frq_qty = $item->frq_qty - $item->after_sales_qty;

            if (!$item->com_name || !$item->supplier_name == config("field.SupplierNameDgk")) {
                $item->com_name = "-";
            }
            $item->is_maoze = 0; //是否是贸泽供应商
            if ($item->supplier_name && in_array(strtolower(trim($item->supplier_name)), config("config.maoze_supname"))) {
                $item->is_maoze = 1;
            }
            $item->sales_order_source_name = arrayGet(FrqModel::SALES_ORDER_SOURCE_MAP, $item->sales_order_source, "");
            $item->brand_area_name = arrayGet(StandardBrandModel::BRAND_AREA_MAPPING, $item->brand_area, "");
            $item->order_pf_second_level_name = arrayGet(FrqModel::ORDER_PF_SECOND_LEVEL_MAP, $item->order_pf_second_level, "");
            //组合转让原因字段
            //需求转让至xxx，转让原因：xxx；未转让给线上采购员原因：xxx
            $remark = $item->share_remark;
            if ($item->status == FrqModel::TransferStatus) {
                $remark = "需求转让至{$item->share_name}，转让原因：{$item->share_remark}；未转让给线上采购员原因：{$item->no_share_sku_pur_user_remark}";
            }
            $item->share_remark = $remark;
        }
        $list = $list->toArray();
        $listData = $list["data"] ?? [];
        $purIds = Arr::pluck($listData, "frq_id");
        $goodsNameArr = array_column($listData, 'goods_name');
        $goodsNameArr = array_filter_unique($goodsNameArr);
        $isControlMap = FrqApiService::checkGoodsIsControl($goodsNameArr);
        $rQty = self::getReturnQty($purIds);
        $wQty = self::getTransWhouseQty($purIds);
        //        dd($rQty,$wQty);
        foreach ($list["data"] as $k => $item) {
            $list["data"][$k]["r_qty"] = $item["return_qty"];
            $list["data"][$k]["w_qty"] = !empty($wQty[$item["frq_id"]]) ? $wQty[$item["frq_id"]] : "";
            $list['data'][$k]['is_warning'] = FrqApiService::checkFrqWarningHandle($item);
            $list['data'][$k]['is_control'] = $isControlMap[$item['goods_name']]??0;
        }


        return $list;
    }

    //采购退货数量
    //取值该明细，对应的发货单，在采购系统推的退货单
    // PS：退货来源为：采购单新增；退货状态为：待付款退款，完成
    public static function getReturnQty($frqIds)
    {
        $purItems = PurchaseItemsModel::getPurItemsIdsByFrqIds($frqIds);
        $purchaseItemIds = Arr::pluck($purItems, "purchase_item_id");
        $purchaseItemIds = array_filter_unique($purchaseItemIds);
        $purItems = arrayManyChangeKeysByField($purItems, "frq_id");

        $rQty = ReturnMaterialItemModel::whereIn("purchase_item_id", $purchaseItemIds)->whereHasIn("return_material", function ($q) {
            $q->where("rma_type", 2)->whereIn("status", [2, 3])->where("is_b_order_return", 0);
        })->select(\DB::raw("sum(return_qty) as return_qtys"), "purchase_item_id")->groupBy("purchase_item_id")->get()->toArray();

        $rQty = arrayChangeKeyByField($rQty, "purchase_item_id");
        $arr = [];
        foreach ($purItems as $frqId => $frqPurItemsIds) {
            if (empty($arr[$frqId])) {
                $arr[$frqId] = 0;
            }
            foreach ($frqPurItemsIds as $item) {
                $arr[$frqId] += $rQty[$item["purchase_item_id"]]["return_qtys"] ?? 0;
            }
        }
        return $arr;
    }

    public static function getTransWhouseQty($frqIds)
    {
        $stockInItemList = StockInItemModel::whereIn("frq_id", $frqIds)->whereHasIn("stock_in", function ($q) {
            $q->where("status", "<>", -3)->whereIn("stock_in_type", [1, 2, 3]);
        })->select("stock_in_item_id", "frq_id")->get()->toArray();
        $stockInItemIds = Arr::pluck($stockInItemList, "stock_in_item_id");

        // 转自营仓数量-仅取转仓类型为：深圳现货转自营、香港转深圳现货类型的转仓数量；  跨境转仓后转自营类型的转仓数量不参与计算
        $stockInItemWList = TransferWarehouseOrderItemsModel::whereIn("source_stock_in_item_id", $stockInItemIds)->where("item_status", 2)->whereHasIn("lie_transfer_warehouse_order", function ($q) {
            $q->where("status", 2)->whereIn('transfer_type', [
                TransferWarehouseOrderModel::TRANSFER_TYPE_SPOT_TO_SELF,
                TransferWarehouseOrderModel::TRANSFER_TYPE_HK_TO_SPOT
            ]);
        })->select(\DB::raw("sum(transfer_num) as transfer_nums"), "source_stock_in_item_id")->groupBy("source_stock_in_item_id")->get()->toArray();
        $arr = [];
        foreach ($stockInItemList as $frqPurItemsIds) {
            if (empty($arr[$frqPurItemsIds["frq_id"]])) {
                $arr[$frqPurItemsIds["frq_id"]] = 0;
            }
            foreach ($stockInItemWList as $item) {
                if ($item["source_stock_in_item_id"] == $frqPurItemsIds["stock_in_item_id"]) {
                    $arr[$frqPurItemsIds["frq_id"]] += $item["transfer_nums"] ?? 0;
                }
            }
        }
        return $arr;
    }

    public static function getNOPurQty($orderItemIds)
    {
        $orderItemIds = array_filter_unique($orderItemIds);
        $frqlist = FrqModel::getFrqInfoByOrderItemidArr($orderItemIds);
        $frqlist = arrayManyChangeKeysByField($frqlist, "order_item_id");
        $noPurQty = [];
        foreach ($frqlist as $k => $item) {
            $saleOrderQty = array_sum(array_column($item, "frq_qty"));
            $purOrderQty = array_sum(array_column($item, "procured_qty"));
            $noPurQty[$k] = $saleOrderQty - $purOrderQty;
            $noPurQty[$k] = $noPurQty[$k] ?: 0;
        }
        return $noPurQty;
    }

    /*
     * 导出
     */
    public function getFrqlistByImport($reqParams)
    {
        if (isset($reqParams["admin_id"])) {
            request()->offsetSet("user", (object)["userId" => $reqParams["admin_id"]]);
        }
        $page = isset($reqParams["page"]) && $reqParams["page"] ? intval($reqParams["page"]) : 1;
        $limit = isset($reqParams["limit"]) && $reqParams["limit"] ? intval($reqParams["limit"]) : static::_LIMIT_;
        $list = $this->buildgetListQuery($reqParams)->getlist($page, $limit);
        $list = $list->toArray();
        $arr = [];
        foreach ($list["data"] as $k => $item) {
            $arr[$k]["frq_id"] = $item["frq_id"];
            $arr[$k]["goods_sn"] = $item["goods_sn"];
            $arr[$k]["goods_name"] = $item["goods_name"];
            $arr[$k]["brand_name"] = $item["brand_name"];
            $arr[$k]["frq_qty"] = $item["frq_qty"] - $item["after_sales_qty"];
            $no_pur_qty = $item["frq_qty"] - $item["after_sales_qty"] - $item["procured_qty"] + $item["return_qty"];
            $arr[$k]["no_pur_qty"] = $no_pur_qty > 0 ? $no_pur_qty : 0;
            $arr[$k]["customer_delivery"] = $item["customer_delivery"];
            $arr[$k]["date_code"] = $item["date_code"];
            $arr[$k]["status_cn"] = Arr::get(FrqModel::$STATUS_CN, $item["status"], "");
            $arr[$k]["remark"] = $item["remark"];
            $arr[$k]["seller_name"] = $item["seller_name"];
            $arr[$k]["sales_without_tax_price"] = $item["sales_without_tax_price"];
            $arr[$k]["sales_price"] = $item["sales_price"];
            $arr[$k]["source_sn"] = $item["source_sn"];
            $arr[$k]["erp_sn"] = $item["erp_sn"];
        }

        return ["count" => $list["total"], "data" => $arr];
    }


    /*
     * 通过采购需求id获取采购单号  多个单号
     */
    public function getPurSnByFrqId($frqId)
    {
        $purids = PurchaseItemsModel::getPurchaseSnByFrqId(intval($frqId));
        $purOrderList = PurchaseOrderModel::getPurOrderSns($purids);
        if (empty($purOrderList)) {
            return [];
        }
        return $purOrderList;
    }

    /*
     * 后台页面罗盘数据
     */
    public function getStatusNums()
    {
        $quan_bu = FrqModel::getStatusNums([], "frq_id");
        $dai_cai_gou = FrqModel::getStatusNums([["status", FrqModel::WaitForTheProcureStatus]], "frq_id"); //待采购(1)
        $bufen_cai_gou = FrqModel::getStatusNums([["status", FrqModel::PartOfTheProcureStatus]], "frq_id");//部分采购(2)
        $suo_ding = FrqModel::getStatusNums([["status", FrqModel::InvalidStatus]], "frq_id");              //锁定(3)
        $jihua_beihuo = FrqModel::getStatusNums([["frq_type", FrqModel::FRQ_TYPE_PLAN]], "frq_id");        //计划备货(4)
        $delete_frq = FrqModel::getDeleteNums();                                                           //已删除
        return [
            "quan_bu"       => $quan_bu,
            "dai_cai_gou"   => $dai_cai_gou,
            "bufen_cai_gou" => $bufen_cai_gou,
            "suo_ding"      => $suo_ding,
            "jihua_beihuo"  => $jihua_beihuo,
            "delete_frq"    => $delete_frq,
        ];
    }

    /*
     * 分享需求
     */
    public function shareFrqService($reqParams)
    {
        $pur_user_id = isset($reqParams["pur_user_id"]) ? $reqParams["pur_user_id"] : 0;
        $frq_ids = isset($reqParams["frq_ids"]) ? $reqParams["frq_ids"] : "";
        if (!$pur_user_id) {
            throw new InvalidRequestException("请选择采购员");
        }

        if (!$frq_ids) {
            throw new InvalidRequestException("请勾选需要分享的需求");
        }

        if ($this->model->checkHasoOperationPerm($frq_ids) <= 0) {
            throw new InvalidRequestException("该采购需求和您没有绑定关系");
        }
        return DB::transaction(function () use ($pur_user_id, $reqParams) {
            return $this->shareFrq($pur_user_id, $reqParams);
        });
    }

    public static function deleteFrq($frq_ids)
    {
        $frq_ids = explode(",", $frq_ids);
        $frqList = FrqModel::getFrqListByIds($frq_ids);
        if (empty($frqList)) {
            throw new InvalidRequestException("采购需求不存在");
        }
        $orderItemLogArr = [];
        foreach ($frqList as $frq) {
            if (!in_array($frq['status'], [FrqModel::WaitForTheProcureStatus, FrqModel::TransferStatus])) {
                throw new InvalidRequestException("采购需求ID:{$frq['frq_id']}状态不是待采购或已转让状态");
            }
            $message = "采购员15min内取消需求，无法履约";
            if (strtotime($frq['create_time']) + 15 * 60 < time()) {
                $message = "采购员超过15min取消需求，无法履约";
            }
            $orderItemLogArr[] = [
                "id"      => $frq['order_item_id'],
                "message" => $message,
            ];
        }
        $where = [];
        $where[] = [
            function ($query) use ($frq_ids) {
                $query->whereIn("frq_id", $frq_ids);
            }
        ];
        $updateData = [
            "is_delete"   => FrqModel::IS_DELETE_YES,
            "delete_uid"  => request()->get("user")->userId,
            "delete_name" => request()->get("user")->name,
            "delete_time" => time(),
            "update_time" => time(),
            "handle_time" => time(),
        ];
        FrqModel::updateFrqInfo($where, $updateData);
        DeliveryLogSyncOrderService::addLogByLogDataArr($orderItemLogArr, "采购需求确认", "采购系统-" . request()->user->name);

        return true;
    }

    public static function recycleFrq($frq_ids)
    {
        $frq_ids = explode(",", $frq_ids);
        $frqList = FrqModel::getFrqListByIds($frq_ids);
        if (empty($frqList)) {
            throw new InvalidRequestException("采购需求不存在");
        }
        $orderItemLogArr = [];
        foreach ($frqList as $frq) {
            if ($frq['status'] != FrqModel::WaitForTheProcureStatus) {
                throw new InvalidRequestException("采购需求ID:{$frq['frq_id']}状态不是待采购状态");
            }
            $orderItemLogArr[] = [
                "id"      => $frq['order_item_id'],
                "message" => "采购员撤回了取消需求的操作，需求待确认",
            ];
        }
        $where = [];
        $where[] = [
            function ($query) use ($frq_ids) {
                $query->whereIn("frq_id", $frq_ids);
            }
        ];
        $updateData = [
            "is_delete"   => FrqModel::IS_DELETE_NO,
            "delete_uid"  => 0,
            "delete_name" => "",
            "delete_time" => 0,
            "update_time" => time(),
            "handle_time" => time(),
        ];
        FrqModel::updateFrqInfo($where, $updateData);
        DeliveryLogSyncOrderService::addLogByLogDataArr($orderItemLogArr, "采购需求确认", "采购系统-" . request()->user->name);

        return true;
    }

    /*
     * 分享需求
     * 新增
     */
    public function shareFrq($pur_user_id, $reqParams)
    {
        $frq_ids = isset($reqParams["frq_ids"]) ? $reqParams["frq_ids"] : "";
        $frq_id_arr = explode(",", $frq_ids);
        $remark = arrayGet($reqParams, "share_remark", null, "trim");
        $isNoShareSkuPurUser = arrayGet($reqParams, "is_no_share_sku_pur_user", null, "intval");
        $noShareSkuPurUserRemark = arrayGet($reqParams, "no_share_sku_pur_user_remark", null, "trim");
        //SKU的供应商名称、线上采购员、履约程度、SKUID覆盖到新需求里
        $insertArr = [];
        $purchase_name_info = PurchaseUserService::getPurchaseUser($pur_user_id);
        $purchase_name = $purchase_name_info ? $purchase_name_info["name"] : "";
        $frqModel = new FrqModel;
        foreach ($frq_id_arr as $k => $frq_id) {
            //            $info = $this->where("purchase_uid",$currentAdmin->userId)->find($frq_id);
            $info = FrqModel::find($frq_id);
            if (!$info) {
                throw new InvalidRequestException(sprintf("第%s条记录不存在", $k + 1));
            }
            if ($info->status == FrqModel::TransferStatus) {
                throw new InvalidRequestException(sprintf("第%s条需求已转让,不能重复转让", $k + 1));
            }
            
            // 检查计划备货类型的需求只能转让给线下采购员
            if ($info->frq_type == FrqModel::FRQ_TYPE_PLAN && $isNoShareSkuPurUser != 1) {
                throw new InvalidRequestException("计划备货类型的需求只能选择转让给线下采购员");
            }

            
            $oldFrqUpdateData = [
                "status"       => FrqModel::TransferStatus,
                "share_remark" => $remark,
                "handle_time"  => time(),
            ];
            if ($isNoShareSkuPurUser == 1) {
                $oldFrqUpdateData["no_share_sku_pur_user_remark"] = $noShareSkuPurUserRemark;
            }
            $oldFrqUpdateData['share_uid'] = $pur_user_id;
            $oldFrqUpdateData['share_name'] = $purchase_name;
            //更新为已共享需求
            $frqModel::updateFrq($frq_id, $oldFrqUpdateData);
            FrqShareApiService::sendShareFrqMessage(array_merge($info->toArray(), $oldFrqUpdateData));
            //记录销售订单日志
            $message = "需求转让至新采购员{$purchase_name}，转让原因：{$remark}";
            DeliveryLogSyncOrderService::addLogByLogDataArr([["id" => $info['order_item_id'], "message" => $message]], "采购需求确认", "采购系统-" . request()->user->name);


            $insertArr[$k] = $info->toArray();
            if ($insertArr[$k]["frq_pid"] <= 0) {
                $insertArr[$k]["frq_pid"] = $frq_id;
            }
            $insertArr[$k]["purchase_uid"] = $pur_user_id;
            $insertArr[$k]["purchase_name"] = $purchase_name;
            unset($insertArr[$k]["frq_id"]);
            $insertArr[$k]["create_time"] = time();
            //设置PM信息
            $insertArr[$k]["pm_uid"] = arrayGet($reqParams, "pm_uid", 0, "intval");
            $insertArr[$k]["pm_name"] = arrayGet($reqParams, "pm_name", "", "trim");
            if ($isNoShareSkuPurUser == 1) {
                $insertArr[$k]["no_share_sku_pur_user_remark"] = $noShareSkuPurUserRemark;
            }
            //清空履约程度,采购供应商信息
            $insertArr[$k]["performance_level"] = FrqService:: NO_PERFORMANCE;
            $insertArr[$k]["pur_supplier_id"] = $reqParams['pur_supplier_id'] ?? 0;
            $insertArr[$k]["pur_supplier_name"] = $reqParams['pur_supplier_name'] ?? "";
            $insertArr[$k]["sku_id"] = $reqParams['sku_id'] ?? "";
            if (isset($reqParams['performance_level']) && $reqParams['performance_level'] !== "") {
                $insertArr[$k]["performance_level"] = $reqParams['performance_level'];
            }


            if ($frqModel->isExistsShareFrq($pur_user_id, $frq_id) > 0) {
                throw new InvalidRequestException(sprintf("采购需求id:%s,商品编码：%s，与该采购员存在绑定关系，请勿重复分享需求!", $insertArr[$k]["frq_pid"], $insertArr[$k]["goods_sn"]));
            }
        }
        return $frqModel->addFrq($insertArr);
    }

    /*
     * 检索品牌
     */
    public function getFrqBrandList($brand_name)
    {
        $list = $this->model->getFrqBrandList($brand_name);

        return $list->toArray();
    }

    /*
     * 检索供应商
     */
    public function searchSupList($supplier_name)
    {
        $list = $this->model->searchSupList($supplier_name);

        return $list->toArray();
    }

    /*
     * 检索供应商
     */
    public function searchComList($name)
    {
        $list = $this->model->searchComName($name);

        return $list->toArray();
    }


    /*
     * 新增采购单-》获取型号列表
     * 前置条件:代采购，部分采购，需求类型必须为同一种
     */
    public function getFrqListByAddPur($ids)
    {
        $list = FrqModel::getManyFrqList($ids);
        if (empty($list)) {
            throw new InvalidRequestException("没找到和您相关的需求数据");
        }
        $supplierIds = array_column($list, 'supplier_id');
        $supplierList = SupplierChannelModel::getSupplierByIds($supplierIds);
        $supplierIdGroupMap = array_column($supplierList, 'supplier_group', 'supplier_id');
        //预先获取终端公司信息
        $end_cust_info = null;
        if (!empty($list[0]['com_name'])) {
            $where = [];
            $where['end_cust_cn'] = $list[0]['com_name'];
            $where['end_cust_status'] = EndCustInfoModel::STATUS_NORMAL;
            $end_cust_info = EndCustInfoModel::getInfoByWhere($where);
            if ($end_cust_info) {
                $empty_cate_info = [
                    'end_cust_cid'    => '',
                    'end_cust_pid'    => '',
                    'end_cust_cat_cn' => '',
                    'end_cust_cat_en' => '',
                    'end_cust_source' => '',
                ];
                $end_cust_info['level1CateInfo'] = EndCustCateModel::getEndCustCateInfo($end_cust_info['end_cust_pcid']) ?? $empty_cate_info;
                $end_cust_info['level2CateInfo'] = EndCustCateModel::getEndCustCateInfo($end_cust_info['end_cust_cid']) ?? $empty_cate_info;
            } else {
                $end_cust_info = null;
            }
        }


        $frq_type = [];
        $companyIds = [];
        foreach ($list as $k => $v) {
            if (!in_array($v["status"], [1, 2])) {
                throw new InvalidRequestException("只有待采购或者部分采购的才能生成采购单");
            }
            array_push($frq_type, $v["frq_type"]);
            array_push($companyIds, $v["company_id"]);

            //需求数量 -  已采购数量 - 售后数量
            $pur_qty = $v["frq_qty"] - $v["procured_qty"] - $v["after_sales_qty"] + $v["return_qty"];
            $pur_qty = $pur_qty <= 0 ? 0 : $pur_qty;
            $list[$k]["pur_qty"] = $pur_qty;
            $list[$k]["default_pur_qty"] = $pur_qty;
            $list[$k]["default_pur_price"] = decimal_number_format(0, DIGITS_SIX);
            $list[$k]["default_pur_without_tax_price"] = decimal_number_format(0, DIGITS_SIX);
            $list[$k]["warehouse_id"] = 0;
            $list[$k]["warehouse_name"] = "";
            $list[$k]["estimat_delivery_time"] = "";
            $list[$k]["date_code"] = $v["date_code"] ?? "";
            $list[$k]["order_remark"] = "";
            $list[$k]["end_cust_info"] = $end_cust_info;
            $list[$k]["delivery_place_format"] = arrayGet(FrqModel::$PLACE_DELIVERY, $v["delivery_place"]);
            $list[$k]["supplier_group"] = isset($supplierIdGroupMap[$v['supplier_id']]) ? $supplierIdGroupMap[$v['supplier_id']] : 0;
        }
        if (count(array_unique(array_filter($frq_type))) != 1) {
            throw new InvalidRequestException("需求类型不一致");
        }


        if (!self::checkSameCompanyId($companyIds)) {
            throw new InvalidRequestException("采购需求归属的采购组织不一致");
        }

        return $list;
    }

    /**
     * Notes: 猎芯和深茂可以一起下单， 华云的只能下华云组织的单据
     * User: sl
     * Date: 2023-11-06 16:30
     * @param $companyIds
     */
    public static function checkSameCompanyId($companyIds)
    {
        $companyIds = array_filter_unique($companyIds);
        $newCompanyIds = [];
        foreach ($companyIds as $companyId) {
            if ($companyId == 1 || $companyId == 2) {
                $newCompanyIds[] = 1;
            } else {
                $newCompanyIds[] = $companyId;
            }
        }
        $newCompanyIds = array_filter_unique($newCompanyIds);
        if (count($newCompanyIds) > 1) {
            return false;
        }
        return true;
    }


    /*
     * 通过商品型号查询需求列表
     */
    public function getfqrlistByGoodsSn($goods_sn)
    {
        $list = FrqModel::getfqrlistByGoodsSn($goods_sn);
        return $list;
    }

    /*
     * 同步采购明细的采购数量到需求数量
     */
    public static function syncPurItemQtyToFrqByFrqId($frq_id)
    {
        $frpInfo = FrqModel::getFrqItemById($frq_id);
        if (!$frpInfo) {
            throw new InvalidRequestException("更新采购需求已采购数量和状态失败");
        }


        //获取采购需求的所有分享和被分享的采购需求id
        $shareFrqIdArr = FrqModel::getFrqIdArrByFromShare($frq_id);
        if (empty($shareFrqIdArr)) {
            throw new InvalidRequestException("更新采购需求已采购数量和状态失败");
        }
        $total_purchase_qty = PurchaseItemsModel::getPurItemByFrqIds($shareFrqIdArr);//需求单已采购数量总和

        $frq_qty = $frpInfo->frq_qty;                //需求数量
        $after_sales_qty = $frpInfo->after_sales_qty;//售后数量
        $return_qty = $frpInfo->return_qty;          //退货数量
        $procured_qty = $total_purchase_qty;         //已采购数量
        $procured_qty = $procured_qty - $return_qty; //采购数量-退货数量
        $procured_qty = $procured_qty <= 0 ? 0 : $procured_qty;
        $status = $frpInfo->status;
        if ($frpInfo->status != FrqModel::InvalidStatus) {//锁定  锁定状态交给销售解锁
            $_frq_qty = $frq_qty - $after_sales_qty;
            if ($procured_qty > 0 && $procured_qty < $_frq_qty) {
                $status = FrqModel::PartOfTheProcureStatus;//部分采购
            } elseif ($procured_qty >= $_frq_qty) {
                $status = FrqModel::CompleteProcureStatus;//完全采购
            } else {
                $status = FrqModel::WaitForTheProcureStatus;//待采购
            }
        }
        $updateData = [
            "status"       => $status,
            "update_time"  => time(),
            "procured_qty" => $total_purchase_qty,//采购数量保持不变
        ];
        return FrqModel::updateFrq($frq_id, $updateData);
    }


    /*
     * 采购单 导入需求
     * 前置条件：采购需求状态为：代采购/部分采购  需求类型必须相同
     */
    public function importFrq($requestParams)
    {
        $com_id = arrayGet($requestParams, "com_id", 0, "intval");
        $file = arrayGet($requestParams, "file");
        if (!$file) {
            throw new InvalidRequestException("文件错误");
        }
        $tables = Excel::toArray(new FrqImport, $file);
        $rows = isset($tables[0]) ? $tables[0] : [];
        if (!$rows) {
            throw new InvalidRequestException("文件错误");
        }
        if ($rows[0][0] != "采购需求ID(*)" || $rows[0][4] != "采购数量(*)") {
            throw new InvalidRequestException("请确认上传的文件是否正确");
        }
        unset($rows[0]);
        $loadData = [];
        foreach ($rows as $row) {
            $i = 0;
            $data = [];
            foreach ($row as $val) {
                if (!isset(FrqImport::$fieldArr[$i])) {
                    break;
                }

                if ($val === null) {
                    $val = "";
                }

                $data[FrqImport::$fieldArr[$i]] = $val;
                $i++;
            }

            $loadData[] = $data;
        }
        $errMsgArr = [];
        foreach ($loadData as $k => $v) {
            if (!$v["frq_id"] || !$v["goods_sn"]) {
                unset($loadData[$k]);
            }
            if (isset($v["estimat_delivery_time"]) && $v["estimat_delivery_time"]) {
                try {
                    $loadData[$k]["estimat_delivery_time"] = trim($v["estimat_delivery_time"]);
                    $estimat_delivery_time = \Carbon\Carbon::instance(\PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($v["estimat_delivery_time"]));
                    if (is_object($estimat_delivery_time)) {
                        $loadData[$k]["estimat_delivery_time"] = $estimat_delivery_time->toDateString();
                    } else {
                        $loadData[$k]["estimat_delivery_time"] = "";
                    }
                } catch (\Exception $e) {
                    $patten = '/^\d{4}(\-|\/|.)\d{1,2}\1\d{1,2}$/';
                    if (preg_match($patten, trim($v["estimat_delivery_time"]))) {
                        $loadData[$k]["estimat_delivery_time"] = $v["estimat_delivery_time"];
                    }
                }
                $loadData[$k]["estimat_delivery_time"] = strtotime($loadData[$k]["estimat_delivery_time"]);
                if ($loadData[$k]["estimat_delivery_time"] <= strtotime("2021-01-01")) {
                    $loadData[$k]["estimat_delivery_time"] = "";
                } else {
                    $loadData[$k]["estimat_delivery_time"] = date("Y-m-d", $loadData[$k]["estimat_delivery_time"]);
                }
            }
        }
        $frqIdList = arrayChangeKeyByField($loadData, "frq_id");
        $frqIds = Arr::pluck($loadData, "frq_id");

        $checkHasoOperationPerm = FrqModel::checkHasoOperationPerms($frqIds);
        foreach ($frqIds as $k => $frq_id) {
            if (!in_array($frq_id, $checkHasoOperationPerm)) {
                throw new InvalidRequestException(sprintf("采购需求id:%s,商品型号：%s,第%s条记录和您没有绑定关系", $frq_id, $frqIdList[$frq_id]["goods_name"], $k + 1));
            }
        }
        $list = $this->getFrqListByAddPur($frqIds);
        $frq_type = null;
        //        $comNameList = [];
        $companyIds = \Arr::pluck($list, "company_id");
        array_push($companyIds, $com_id);
        $companyIds = array_filter_unique($companyIds);
        if (!self::checkSameCompanyId($companyIds)) {
            throw new InvalidRequestException("导入采购需求归属的采购组织不一致");
        }

        $orgCurrencyRateAndTaxInfo = CommonService::getOrgCurrencyRateAndTaxInfo($com_id);
        $taxRate = (float)$orgCurrencyRateAndTaxInfo["tax_rate"];
        $standCurrency = arrayGet(PurCommonEnum::$standardCurrency, $com_id);
        foreach ($list as $k => $item) {
            //            array_push($comNameList, $item["com_name"]);
            foreach ($loadData as $load_k => $load_item) {
                if ($item["frq_id"] == $load_item["frq_id"]) {
                    if ($frq_type && $frq_type != $item["frq_type"]) {
                        throw new InvalidRequestException(sprintf("型号明细:%s,第%s条记录需求类型和前面不一致，不能导入哦", $item["goods_name"], $load_k));
                    }

                    if (!$frq_type) {
                        $frq_type = $item["frq_type"];
                    }


                    if (!in_array($item["status"], [1, 2])) {//1:待采购,2:部分采购
                        throw new InvalidRequestException(sprintf("型号明细:%s,第%s条记录状态不是待采购或者部分采购，不能导入哦", $item["goods_name"], $load_k));
                    }

                    if (in_array($com_id, PurCommonEnum::$TAX_COMPANY)) {//1:猎芯
                        //                        $list[$k]["default_pur_price"] = decimal_number_format($load_item["price"], DIGITS_SIX);
                        //                        $list[$k]["default_pur_without_tax_price"] = decimal_number_format($load_item["price"] / config("field.TaxRate"), DIGITS_SIX);
                        $list[$k]["default_pur_price"] = decimal_number_format($load_item["price"], DIGITS_SIX);
                        $list[$k]["default_pur_without_tax_price"] = decimal_number_format($load_item["price"] / $taxRate, DIGITS_SIX);
                        //                        $list[$k]["default_pur_without_tax_price"]
                        //如果组织是猎芯,又是香港仓库,则过滤这条数据
                        //                        if ($list[$k]['sales_currency'] == 2) {
                        //                            $companyName = PurCommonEnum::$COMPANY_LIST[$list[$k]['company_id']];
                        //                            unset($list[$k]);
                        //                            $errMsgArr[] = "表格第3行型号，交货地为香港，不能使用{$companyName}下单；";
                        //
                        //                            continue;
                        //                        }


                    } else {//2：深贸
                        $list[$k]["default_pur_price"] = decimal_number_format($load_item["price"], DIGITS_SIX);
                        $list[$k]["default_pur_without_tax_price"] = decimal_number_format($load_item["price"], DIGITS_SIX);
                    }

                    $list[$k]["is_gift"] = 0;
                    if ($list[$k]["default_pur_price"] < 0.*********) {
                        $list[$k]["is_gift"] = 1;//赠品
                    }

                    //仓库自动选择
                    $warehouse_id = 0;
                    $warehouse_name = "";
                    if ($com_id == "1") {
                        $warehouse_id = config("field.LieXinDefaultWarehouse.id");
                        $warehouse_name = config("field.LieXinDefaultWarehouse.name");
                    } elseif ($com_id == "2") {
                        if ($item["sales_currency"] == 1) {
                            $warehouse_id = config("field.ShenMaoDefaultDefaultWarehouseCh.id");;
                            $warehouse_name = config("field.ShenMaoDefaultDefaultWarehouseCh.name");;
                        } else {
                            $warehouse_id = config("field.ShenMaoDefaultDefaultWarehouse.id");;
                            $warehouse_name = config("field.ShenMaoDefaultDefaultWarehouse.name");;
                        }
                    } elseif ($com_id == "3") {
                        $warehouse_id = config("field.HuaYunDefaultDefaultWarehouse.id");;
                        $warehouse_name = config("field.HuaYunDefaultDefaultWarehouse.name");;
                    } elseif ($com_id == "9") {
                        $warehouse_id = config("field.YFDefaultWarehouse.id");;
                        $warehouse_name = config("field.YFDefaultWarehouse.name");;
                    } elseif ($com_id == "10") {
                        $warehouse_id = config("field.GDDefaultWarehouse.id");;
                        $warehouse_name = config("field.GDDefaultWarehouse.name");;
                    } elseif ($com_id == "11") {
                        $warehouse_id = config("field.TGDefaultWarehouse.id");;
                        $warehouse_name = config("field.TGDefaultWarehouse.name");;
                    } elseif ($com_id == "12") {
                        $warehouse_id = config("field.YNDefaultWarehouse.id");;
                        $warehouse_name = config("field.YNDefaultWarehouse.name");;
                    }


                    $import_frq_qty = intval($load_item["procured_qty"]);                                                    //导入的采购数量
                    $max_pur_qty = $item["frq_qty"] - $item["after_sales_qty"] - $item["procured_qty"] + $item["return_qty"];//最大采购数量
                    $max_pur_qty = $max_pur_qty <= 0 ? 0 : $max_pur_qty;
                    $list[$k]["import_frq_qty"] = $import_frq_qty <= $max_pur_qty ? $import_frq_qty : $max_pur_qty;
                    $list[$k]["warehouse_id"] = $warehouse_id;
                    $list[$k]["warehouse_name"] = $warehouse_name;
                    $list[$k]["default_pur_qty"] = $import_frq_qty <= $max_pur_qty ? $import_frq_qty : $max_pur_qty;
                    $list[$k]["estimat_delivery_time"] = trim($load_item["estimat_delivery_time"]);
                    $list[$k]["date_code"] = trim($load_item["date_code"]);
                    $list[$k]["order_remark"] = trim($load_item["order_remark"]);
                    // $list[$k]["tax_rate"] = ($taxRate - 1) * 100;
                    $list[$k]["tax_rate"] = bcsub($taxRate, 1, 2)*100;
                    $list[$k]["total_amount"] = decimal_number_format($list[$k]["default_pur_price"] * $list[$k]["default_pur_qty"], 2, $standCurrency);
                }
            }
        }


        //        $comNameList = array_unique($comNameList);
        //        if (count($comNameList) > 1) {
        //            throw new InvalidRequestException("导入失败,导入明细的客户名称不一致");
        //        }
        return [$list, $errMsgArr];
    }


    /*
     *导出采购需求
     */
    public function exportFrq($requestParams)
    {
        return (new ThirdExportService)->frqExport($requestParams);
    }

    /*
    * 删除采购需求
    */
    public function delFrq($order_item_id_arr, $requestData)
    {
        $erpParams = arrayGet($requestData, "erp_params", "");
        //type:1    销售反审核删       销售取消关闭10
        $type = arrayGet($requestData, "type", "", "intval");
        if (!in_array($type, [1, 10])) {
            throw new InvalidRequestException("参数type类型错误，请联系产研技术进行处理");
        }
        $where = [];
        if ($type == 10) {
            //查询非作废状态采购订单
            $where["status"] = PurchaseItemsModel::STATUS_ENABLE;
        }
        $has_order_item_id_arr = PurchaseItemsModel::checkIsExistsOrderItemIdsV2($order_item_id_arr, $where);
        $frqList = FrqModel::getFrqInfoByOrderItemIds($order_item_id_arr);
        $frqList = arrayChangeKeyByField($frqList, "order_item_id");
        $purOrderBuyer = PurchaseItemsModel::getPurOrderAndItemInfoByOrderItemIds($order_item_id_arr);
        $purOrderBuyer = arrayChangeKeyByField($purOrderBuyer, "order_item_id");

        //先直接走一遍取消转仓单逻辑
        $transferOrderItemIdArr = TransferOrderApiService::cancelByOrderItemId($order_item_id_arr);
        $order_item_id_arr = array_diff($order_item_id_arr, $transferOrderItemIdArr);

        $this->startTransaction();
        try {
            foreach ($order_item_id_arr as $order_item_id) {
                if ($type == 1 && in_array($order_item_id, $has_order_item_id_arr)) {
                    throw new InvalidRequestException("此订单已经关联生成采购订单，请操作【取消】功能");
                } elseif ($type == 10 && in_array($order_item_id, $has_order_item_id_arr)) {
                    $purchaseName = $purOrderBuyer[$order_item_id]["purchase_order"]["purchase_name"] ?? "";
                    $purchaseSn = $purOrderBuyer[$order_item_id]["purchase_order"]["purchase_sn"] ?? "";
                    throw new InvalidRequestException(sprintf("请找%s采购同学作废%s采购订单后，再操作【取消】功能", $purchaseName, $purchaseSn));
                }

                if (!isset($frqList[$order_item_id])) {
                    throw new InvalidRequestException(sprintf("商品明细%s,没找到该采购需求", $order_item_id));
                }
                if (!in_array($frqList[$order_item_id]["frq_type"], [1, 3])) {
                    throw new InvalidRequestException(sprintf("商品明细%s,不是以销订购类型，无法删除", $order_item_id));
                }
                if (!in_array($frqList[$order_item_id]["status"], [FrqModel::WaitForTheProcureStatus, FrqModel::TransferStatus]) || $frqList[$order_item_id]["procured_qty"] > 0) {
                    throw new InvalidRequestException(sprintf("商品明细%s,已发起采购，无法删除", $order_item_id));
                }
                FrqModel::delFrq($order_item_id);
            }

            if ($erpParams) {
                $res = (new ThirdErpService)->push(ThirdErpService::PUSH_TYPE_SOAP, 'deleteOrder', $erpParams);
                if (empty($res)) {
                    throw new InvalidRequestException("请求erp删除销售明细需求失败");
                }
                if ($res['code'] != 0) {
                    throw new InvalidRequestException($res['msg']);
                }
            }
        } catch (\Throwable $e) {
            $this->rollBackTransaction();
            throw new InvalidRequestException($e->getMessage());
        }
        $this->commitTransaction();
    }

    /*
    * 删除采购需求
    */
    public function delFrqForMes($mes_order_item_id_arr)
    {
        $frqList = FrqModel::getFrqInfoByMesOrderItemIds($mes_order_item_id_arr);
        $frqList = arrayChangeKeyByField($frqList, "order_item_id");
        try {
            $this->startTransaction();
            foreach ($frqList as $frqInfo) {
                if ($frqInfo['status'] != 1) {
                    throw new InvalidRequestException(sprintf("明细id%s,状态不是待采购,无法删除", $frqInfo['order_item_id']));
                }
            }
            FrqModel::delFrqByFrqId(array_column($frqList, 'frq_id'));
            $this->commitTransaction();
        } catch (\Throwable $e) {
            $this->rollBackTransaction();
            throw new InvalidRequestException($e->getMessage());
        }
    }

    /*
     * 修改采购-接收修改采购员、批次
     */
    public function editFrqBypurUserBatch($requestData)
    {
        $orderItemsList = arrayGet($requestData, "items", "");
        $erpParams = arrayGet($requestData, "erp_params", "");
        if (empty($erpParams)) {
            throw new InvalidRequestException(sprintf("参数错误，erp_params为空"));
        }

        $order_item_id_arr = Arr::pluck($orderItemsList, "order_item_id");
        $has_order_item_id_arr = PurchaseItemsModel::checkIsExistsOrderItemIds($order_item_id_arr);
        $frqList = FrqModel::getFrqInfoByOrderItemIds($order_item_id_arr);
        $frqList = arrayChangeKeyByField($frqList, "order_item_id");

        $syncReqMaoZe = [];
        $this->startTransaction();
        try {
            foreach ($orderItemsList as $item) {
                if (!$item["order_item_id"]) {
                    throw new InvalidRequestException(sprintf("参数明细id是必须的"));
                }

                if (in_array($item["order_item_id"], $has_order_item_id_arr)) {
                    throw new InvalidRequestException(sprintf("商品明细%s,已发起采购，无法修改", $item["order_item_id"]));
                }

                //目前只有单条数据修改的需求
                $frqInfo = $frqList[$item["order_item_id"]] ?? [];
                if (!$frqInfo) {
                    throw new InvalidRequestException(sprintf("商品明细%s,没找到该采购需求", $item["order_item_id"]));
                }
                if ($frqInfo["frq_type"] != 1) {
                    throw new InvalidRequestException(sprintf("商品明细%s,不是以销订购类型，无法修改", $item["order_item_id"]));
                }
                if (!in_array($frqInfo["status"], [FrqModel::WaitForTheProcureStatus, FrqModel::TransferStatus]) || $frqInfo["procured_qty"] > 0) {
                    throw new InvalidRequestException(sprintf("商品明细%s,已发起采购，无法修改", $item["order_item_id"]));
                }
                
                // 检查是否为已转让或转让后的需求，这些需求不允许修改采购员
                $purchase_uid = arrayGet($item, "purchase_uid", "", "intval");
                $purchase_name = arrayGet($item, "purchase_name", "", "trim");
                
                // 如果是已转让或转让后的需求，跳过采购员字段的修改
                $skipPurchaseFields = false;
                if (($frqInfo["status"] == FrqModel::TransferStatus) || 
                    (!empty($frqInfo["frq_pid"]) && $frqInfo["frq_pid"] > 0)) {
                    $skipPurchaseFields = true;
                    $purchase_uid = 0;
                    $purchase_name = "";
                }
                
                $updataData = [];
                $supp = [];
                $date_code = arrayGet($item, "date_code", "", "trim");
                $supp["supplier_id"] = arrayGet($item, "supplier_id", "", "trim");
                $supp["supplier_name"] = arrayGet($item, "supplier_name", "", "trim");
                $supp["supp_brand_name"] = arrayGet($item, "supp_brand_name", "", "trim");
                $supp["supp_goods_sn"] = arrayGet($item, "supp_goods_sn", "", "trim");


                foreach ($supp as $field => $val) {
                    //                    if ($val) {
                    $updataData[$field] = $val ?: "";
                    //                    }
                }

                if ($date_code) {
                    $updataData["date_code"] = $item["date_code"];
                }
                // 只有在不跳过采购员字段时才更新采购员信息
                if (!$skipPurchaseFields && $purchase_uid) {
                    $updataData["purchase_uid"] = $purchase_uid;
                }
                if (!$skipPurchaseFields && $purchase_name) {
                    $updataData["purchase_name"] = $purchase_name;
                }

                if (isset($item["com_name"])) {
                    $updataData["com_name"] = $item["com_name"] ?? "";
                }
                if (isset($item['performance_level'])) {
                    $updataData['performance_level'] = $item['performance_level'] ?? "";
                }

                $updataData['is_delete'] = FrqModel::IS_DELETE_NO;
                if (empty($updataData)) {
                    throw new InvalidRequestException("参数错误，没有发现要修改的参数");
                }
                FrqModel::updateFrqInfo([
                    "order_item_id" => intval($item["order_item_id"])
                ], $updataData);
            }

            $res = (new ThirdErpService)->push(ThirdErpService::PUSH_TYPE_SOAP, 'updatesaleorder', $erpParams);
            if (empty($res)) {
                throw new InvalidRequestException("请求erp操作失败");
            }
            if ($res['code'] != 0) {
                throw new InvalidRequestException($res['msg']);
            }
            if (!empty($supp["supplier_name"]) && in_array($supp["supplier_name"], config("config.maoze_supname"))) {
                array_push($syncReqMaoZe, $item["order_item_id"]);
            }
        } catch (\Throwable $e) {
            $this->rollBackTransaction();
            throw new InvalidRequestException($e->getMessage());
        }

        $this->commitTransaction();
        foreach ($syncReqMaoZe as $syncOrderItemIdy) {
            $this->syncReqMaoZeISCanAddOrder($syncOrderItemIdy, 1, 1);
        }
    }

    public function checkOrderItesmInScmOrder($orderItemId)
    {
        $purchaseItemIds = PurchaseItemsModel::where("order_item_id", $orderItemId)->where("status", PurchaseItemsModel::STATUS_ENABLE)->pluck("purchase_item_id")->toArray();
        $purchaseItemIds = $purchaseItemIds ?: [];
        if (ScmOrderModel::whereIn("purchase_item_id", $purchaseItemIds)->count("scm_order_item_id")) {
            throw new InvalidRequestException(sprintf("商品明细%s,采购单明细已生成委托单明细，请联系采购员删除委托单后再修改", $orderItemId));
        }
    }

    /*
     *采购-接收修改商品型号
     */
    public function editFrqByGoodsName($requestData)
    {
        $orderItemsList = arrayGet($requestData, "items", "");
        $erpParams = arrayGet($requestData, "erp_params", "");
        $operator = arrayGet($requestData, "operator", "");
        if (empty($erpParams)) {
            throw new InvalidRequestException(sprintf("参数错误，erp_params为空"));
        }

        //记录日志
        $logs = [];
        $i = 0;
        foreach ($orderItemsList as $item) {
            $i++;
            //数据量不大 一般也就一条数据
            $purItemList = PurchaseItemsModel::getPurItemListByOrderItemId([$item["order_item_id"]]);
            foreach ($purItemList as $purItem) {
                $i++;
                $logs[$i]["purchase_id"] = $purItem["purchase_id"];
                $logs[$i]["message"] = sprintf("销售%s,修改明细,原始型号:%s,原始编码:%s,原始品牌:%s", $operator, $purItem["goods_name"], $purItem["goods_sn"], $purItem["brand_name"]);
            }
        }

        $order_item_id_arr = Arr::pluck($orderItemsList, "order_item_id");
        $frqList = FrqModel::getFrqInfoByOrderItemIds($order_item_id_arr);
        $frqList = arrayChangeKeyByField($frqList, "order_item_id");
        $syncReqMaoZe = [];
        $this->startTransaction();
        $purIdAllArrIds = [];
        foreach ($orderItemsList as $item) {
            if (!$item["order_item_id"]) {
                throw new InvalidRequestException(sprintf("参数明细id是必须的"));
            }
            $frqInfo = $frqList[$item["order_item_id"]] ?? [];
            $supplier_name = $frqInfo["supplier_name"] ?? "";
            if (!empty($supplier_name) && in_array($supplier_name, config("config.maoze_supname"))) {
                array_push($syncReqMaoZe, $item["order_item_id"]);
            }

            //是否存在发货通知单且发货单没有同步到erp
            //数据量不大 一般也就一条数据
            $isExists = StockInItemModel::checkIsExistsOrderItemIdAndNoSyncErp($item["order_item_id"]);
            if ($isExists) {
                //存在发货通知单，并且同步到了erp 则不能修改了
                $this->rollBackTransaction();
                throw new InvalidRequestException(sprintf("商品明细%s,已生成发货通知单并且同步到erp了，无法修改", $item["order_item_id"]));
            }

            //采购单明细已生成委托单明细，销售就不可修改型号；
            self::checkOrderItesmInScmOrder($item["order_item_id"]);


            //数据量不大 一般也就一条数据
            $purItemIdArr = PurchaseItemsModel::getItemsIdByOrderItemId($item["order_item_id"]);
            //数据量不大 一般也就一条数据
            $purIdArr = PurchaseItemsModel::getItemsIdByOrderId($item["order_item_id"]);
            foreach ($purIdArr as $purId) {
                array_push($purIdAllArrIds, $purId);
            }
            $updataData = [];
            $updataData["update_time"] = time();
            $updataData["goods_id"] = arrayGet($item, "goods_id", 0, "intval");
            $updataData["goods_sn"] = arrayGet($item, "goods_sn", "", "trim");
            $updataData["goods_name"] = arrayGet($item, "goods_name", "", "trim");
            $brand_id = arrayGet($item, "brand_id", 0, "intval");
            $brand_name = arrayGet($item, "brand_name", "", "trim");
            $goods_class_first = arrayGet($item, "goods_class_first", "", "trim");
            $updataData["goods_unit"] = arrayGet($item, "goods_unit", "", "trim");

            if ($brand_id) {
                $updataData["brand_id"] = $brand_id;
            }

            if ($brand_name) {
                $updataData["brand_name"] = $brand_name;
            }
            if ($goods_class_first) {
                $updataData["goods_class_first"] = $goods_class_first;
            }
            if (!empty(arrayGet($item, "brand_area", "", "trim"))) {
                $updataData["brand_area"] = arrayGet($item, "brand_area", "", "trim");
            }
            //修改需求单信息
            FrqModel::updateFrqInfo([
                "order_item_id" => intval($item["order_item_id"])
            ], $updataData);
            PurchaseItemsModel::updatePurItemInfo([
                "order_item_id" => intval($item["order_item_id"])
            ], $updataData);

            $updataPaymentItemsData = $updataData;
            unset($updataPaymentItemsData["update_time"]);
            unset($updataPaymentItemsData["goods_class_first"]);
            //            PaymentItemsModel::whereIn("purchase_item_id", $purItemIdArr)->update($updataPaymentItemsData);
            $paymentItemsWhere = [];
            $purItemIdArr = $purItemIdArr ? $purItemIdArr->toArray() : [];
            $paymentItemsWhere[] = ["purchase_item_id", "in", $purItemIdArr];
            unset($updataPaymentItemsData['brand_area']);
            PaymentItemsModel::buildSimpleQueryByUpdate($paymentItemsWhere, $updataPaymentItemsData);


            //修改入库单型号等信息
            $isExistsStockItem = StockInItemModel::checkIsExistsOrderItemId($item["order_item_id"]);
            if ($isExistsStockItem) {
                StockInItemModel::updateStockItemInfo(["order_item_id" => $item["order_item_id"]], $updataData);
            }

            //修改委托单明细关联的数据
            $scmData = [];
            foreach ($purItemIdArr as $purItemid) {
                $tempScmArr = $updataData;
                $tempScmArr["purchase_item_id"] = $purItemid;
                $scmData[] = $tempScmArr;
            }
            ScmOrderService::batchUpdateScmGoods($scmData);
        }


        $res = (new ThirdErpService)->push(2, 'updatesaleorder', $erpParams);
        if ($res['code'] != 0) {
            $this->rollBackTransaction();
            throw new InvalidRequestException($res['msg']);
        }

        $this->commitTransaction();
        foreach ($syncReqMaoZe as $syncOrderItemId) {
            $this->syncReqMaoZeISCanAddOrder($syncOrderItemId, 1, 1);
        }

        //记录修改日志
        foreach ($logs as $log) {
            ActionLogService::addLog(ActionLogService::TYPE_ACTION_PURCHASE_DISABLE, $log["purchase_id"], ["message" => $log["message"]]);
        }
    }

    /*
     * 修改商品数量
     */
    public function editFrqByFrqQqty($requestData)
    {
        $orderItemsList = arrayGet($requestData, "items", "");
        $erpParams = arrayGet($requestData, "erp_params", "");
        if (empty($erpParams)) {
            throw new InvalidRequestException(sprintf("参数错误，erp_params为空"));
        }
        $orderItemIdArr = Arr::pluck($orderItemsList, "order_item_id");
        $orderItemIdArr = array_filter_unique($orderItemIdArr);
        if (empty($orderItemIdArr)) {
            throw new InvalidRequestException(sprintf("参数错误"));
        }

        //查询商品明细的采购数量
        $orderItemQty = PurchaseItemsModel::getPurQtyByOrderItemId($orderItemIdArr);
        $this->startTransaction();
        $orderItemQty = arrayChangeKeyByField($orderItemQty, "order_item_id");

        $order_item_id_arr = array_keys($orderItemQty);


        //获取对应的采购需求信息
        $frqList = FrqModel::getFrqInfoByOrderItemidArr($orderItemIdArr);
        $frqList = arrayChangeKeyByField($frqList, "order_item_id");
        //        dump($orderItemsList);

        //记录日志
        $logs = [];
        $i = 0;
        foreach ($orderItemsList as $item) {
            $i++;
            //基本上只有一条数据
            $purItemList = PurchaseItemsModel::getPurItemListByOrderItemId([$item["order_item_id"]]);
            foreach ($purItemList as $purItem) {
                $i++;
                $logs[$i]["purchase_id"] = $purItem["purchase_id"];
                $logs[$i]["message"] = sprintf("销售修改了数量,型号：%s,编码:%s,原始数量:%s", $purItem["goods_name"], $purItem["goods_sn"], $purItem["frq_qty"]);
            }
        }
        //        dd($logs);

        foreach ($orderItemsList as $item) {
            $order_item_id = arrayGet($item, "order_item_id", 0, "intval");
            $frq_qty = arrayGet($item, "frq_qty", 0, "intval");
            if (!$order_item_id) {
                $this->rollBackTransaction();
                throw new InvalidRequestException(sprintf("参数明细id是必须的"));
            }

            $after_sales_qty = 0; //售后数量
            $total_pur_qty = 0;   //已采购数量
            $total_return_qty = 0;//采购退货数量
            if (isset($orderItemQty[$order_item_id])) {
                $after_sales_qty = $frqList[$order_item_id]["after_sales_qty"];
                $total_pur_qty = $orderItemQty[$order_item_id]["total_pur_qty"];
                $total_return_qty = $orderItemQty[$order_item_id]["total_return_qty"];
            }


            $alreayPurNums = $total_pur_qty - $total_return_qty + $after_sales_qty;
            if (isset($orderItemQty[$order_item_id]) && $frq_qty < $alreayPurNums) {
                $this->rollBackTransaction();
                throw new InvalidRequestException(sprintf("商品明细%s,修改数量(%s)不能小于采购已订货数量和售后数量之和(%s)，无法修改", $order_item_id, $frq_qty, $alreayPurNums));
            }
            $updataData["frq_qty"] = $frq_qty;
            //修改需求单信息
            FrqModel::updateFrqInfo([
                "order_item_id" => intval($item["order_item_id"])
            ], $updataData);
            //修改采购明细的采购需求数量
            PurchaseItemsModel::updatePurItemInfo([
                "order_item_id" => intval($item["order_item_id"])
            ], ["frq_qty" => $frq_qty - $after_sales_qty]);
            //            FrqService::syncPurItemQtyToFrqByFrqId($detail["frq_id"]);

        }

        $frqList = FrqModel::getFrqInfoByOrderItemidArr($order_item_id_arr);
        $frqIdArr = Arr::pluck($frqList, "frq_id");//需求单id
        $purOrderService = new PurOrderService;
        $emailNoticeArr = [];
        foreach ($frqList as $frqItem) {
            $oldQty = $frqItem["frq_qty"];
            $updateFrq = [
                "frq_qty" => $frqItem["frq_qty"],
            ];
            if ($frqItem["procured_qty"] - $frqItem["return_qty"] >= $frqItem["frq_qty"] - $frqItem["after_sales_qty"]) {
                $updateFrq["status"] = 3;//完全采购
            } else {
                if ($frqItem["procured_qty"] > 0 && $frqItem["procured_qty"] - $frqItem["return_qty"] < $frqItem["frq_qty"] - $frqItem["after_sales_qty"]) {
                    $updateFrq["status"] = 2;//部分采购
                } else {
                    $updateFrq["status"] = 1;//待采购
                }
            }
            //如果是完全采购变成了部分采购，需要发送邮件通知
            if ($frqItem["status"] == 3 && $updateFrq["status"] == 2) {
                $emailNoticeArr[] = [
                    "frq_info" => $frqItem,
                    "old_qty"  => $oldQty,
                    "new_qty"  => $updateFrq['frq_qty'],
                ];
            }

            //修改采购需求单信息
            FrqModel::updateFrqInfo([
                "frq_id" => $frqItem["frq_id"]
            ], $updateFrq);
            //同步该需求到 分享和被分享得需求
            $purOrderService->syncFrqToFrq($frqItem["frq_id"]);
        }


        \Log::channel("purFrqSyncLog")->info(sprintf("--------销售修改数量开始请求erp--%s-----------", time()));
        \Log::channel("purFrqSyncLog")->info(sprintf("--------请求erp参数:--%s-----------", json_encode($erpParams)));
        $res = (new ThirdErpService)->push(2, 'updatesaleorder', $erpParams);
        \Log::channel("purFrqSyncLog")->info(sprintf("--------销售修改数量请求erp完成--%s-----------", time()));

        if ($res['code'] != 0) {
            $this->rollBackTransaction();
            throw new InvalidRequestException('同步商品数量等到ERP失败，原因：' . $res['msg']);
        }

        //记录修改日志
        foreach ($logs as $log) {
            ActionLogService::addLog(ActionLogService::TYPE_ACTION_PURCHASE_DISABLE, $log["purchase_id"], ["message" => $log["message"]]);
        }


        $this->commitTransaction();
        self::sendEmailByFrqQtyChange($emailNoticeArr);
    }

    public static function sendEmailByFrqQtyChange($emailNoticeArr)
    {
        //你有采购需求单已经完全采购，销售修改了订单数量，采购需求更改成了"部分采购"，请尽快前往【采购需求管理】页面进行确认；（可继续下单采购）
        //
        //关联销售单号：{{data.order_sn}}；型号：{{data.goods_sn}}；品牌：{{data.brand_name}}；数量：{{data.new_qty}}（原需求数量为：{{data.old_qty}}），；可对新加的{{data.add_qty}}个需求数量进行下单采购操作；
        foreach ($emailNoticeArr as $value) {
            $emailParams = [
                "order_sn"   => $value['frq_info']['source_sn'],
                "goods_name" => $value['frq_info']['goods_name'] ?? "",
                "brand_name" => $value['frq_info']['brand_name'] ?? "",
                "new_qty"    => $value['new_qty'],
                "old_qty"    => $value['old_qty'],
                "add_qty"    => $value['new_qty'] - $value['old_qty'],
            ];
            //获取采购的邮箱
            $sale_info = CmsUserInfoModel::getUserInfoById($value['frq_info']['purchase_uid']); // 业务员信息
            $toUserEmail = $sale_info['email'];
            // 发送邮件
            $template = 'pur_frq_qty_change';                                                   //
            MessageService::sendMail($template, $emailParams, $toUserEmail);
        }
    }

    public static function sendEmailByFrqPriceChange($emailNoticeArr)
    {
        //获取采购单号
        $purOrderList = PurchaseOrderModel::getPurOrderListByIdArr(array_column($emailNoticeArr, "purchase_id"));
        $purOrderSnArr = Arr::pluck($purOrderList, null, "purchase_id");//获取采购单号
        //获取销售订单号
        $saleOrderList = FrqModel::getFrqListByIds(array_column($emailNoticeArr, "frq_id"));
        $saleOrderSnArr = Arr::pluck($saleOrderList, "source_sn", "frq_id");//获取销售订单号

        foreach ($emailNoticeArr as $value) {
            $emailParams = [
                "goods_name"  => $value['purItem']['goods_name'] ?? "",
                "brand_name"  => $value['purItem']['brand_name'] ?? "",
                "new_price"   => $value['new_sales_price'],
                "old_price"   => $value['old_sales_price'],
                "purchase_sn" => $purOrderSnArr[$value['purchase_id']]['purchase_sn'] ?? "",
                "order_sn"    => $saleOrderSnArr[$value['frq_id']] ?? "",
            ];
            //获取采购的邮箱
            $sale_info = CmsUserInfoModel::getUserInfoById($purOrderSnArr[$value['purchase_id']]['purchase_uid']); // 业务员信息
            $toUserEmail = $sale_info['email'];
            // 发送邮件
            $template = 'pur_frq_price_change';
            MessageService::sendMail($template, $emailParams, $toUserEmail);
        }
    }

    /*
     * 修改价格和供应商 物料编码
     */
    public function editFrqByFrqPrice($requestData)
    {
        $orderItemsList = arrayGet($requestData, "items", "");
        $erpParams = arrayGet($requestData, "erp_params", "");
        if (empty($erpParams) || empty($orderItemsList)) {
            throw new InvalidRequestException(sprintf("参数错误，erp_params为空"));
        }
        $orderItemIdArr = Arr::pluck($orderItemsList, "order_item_id");
        $orderItemIdArr = array_filter_unique($orderItemIdArr);
        if (empty($orderItemIdArr)) {
            throw new InvalidRequestException(sprintf("参数错误"));
        }

        //记录日志
        $logs = [];
        $i = 0;
        $emailNoticeArr = [];
        foreach ($orderItemsList as $item) {
            $i++;
            //基本上只有一条数据
            $purItemList = PurchaseItemsModel::getPurItemListByOrderItemId([$item["order_item_id"]]);
            foreach ($purItemList as $purItem) {
                $i++;
                $logs[$i]["purchase_id"] = $purItem["purchase_id"];
                $logs[$i]["message"] = sprintf("销售修改了销售价格,型号：%s,编码:%s,原始含税销售价格:%s", $purItem["goods_name"], $purItem["goods_sn"], $purItem["sales_price"]);
                if ($item['sales_price'] < $purItem['sales_price']) {
                    $emailNoticeArr[] = [
                        "purItem"         => $purItem,
                        "new_sales_price" => $item['sales_price'],
                        "old_sales_price" => $purItem['sales_price'],
                        "purchase_id"     => $purItem['purchase_id'],
                        "order_item_id"   => $purItem['order_item_id'],
                        "frq_id"          => $purItem['frq_id'],
                    ];
                }
            }
        }

        $purOrderService = new PurOrderService();

        $this->startTransaction();
        foreach ($orderItemsList as $item) {
            $order_item_id = arrayGet($item, "order_item_id", 0, "intval");
            $sales_price = arrayGet($item, "sales_price", "", "trim");
            $sales_without_tax_price = arrayGet($item, "sales_without_tax_price", "", "trim");


            if (!$order_item_id) {
                $this->rollBackTransaction();
                throw new InvalidRequestException(sprintf("参数销售明细id是必须的"));
            }

            if (!$sales_price || !$sales_without_tax_price) {
                throw new InvalidRequestException(sprintf("价格参数是必须的"));
            }

            //修改价格
            $sales_price = decimal_number_format($sales_price, DIGITS_SIX);
            $sales_without_tax_price = decimal_number_format($sales_without_tax_price, DIGITS_SIX);
            if ($sales_price < 0.0********* || $sales_without_tax_price < 0.0*********) {
                //                throw new InvalidRequestException(sprintf("参数价格不能小于0"));
            }
            //是否存在发货通知单
            $purItemIdArr = PurchaseItemsModel::getItemsIdByOrderItemId($item["order_item_id"]);
            $purItemIdArr = $purItemIdArr ? $purItemIdArr->toArray() : [];
            $purItemIdArr = array_filter_unique($purItemIdArr);
            //判断是否有生成付款单
            FrqModel::updateFrqInfo([
                "order_item_id" => intval($item["order_item_id"])
            ], [
                "sales_price"             => $sales_price,
                "sales_without_tax_price" => $sales_without_tax_price,
            ]);


            //修改采购明细的采购需求数量
            PurchaseItemsModel::updatePurItemInfo([
                "order_item_id" => intval($item["order_item_id"])
            ], [
                "sales_price"             => $sales_price,
                "sales_without_tax_price" => $sales_without_tax_price,
            ]);

            $purIdArr = PurchaseItemsModel::getItemsIdByOrderId($item["order_item_id"]);
            $purIdArr = $purIdArr ? $purIdArr->toArray() : [];
            $purIdArr = array_filter_unique($purIdArr);


            //重新计算明细毛利和订单毛利

            //计算采购明细毛利 毛利率 税额
            foreach ($purItemIdArr as $_purchase_item_id) {
                $purOrderService->updatePurOrderItemAboutAmount($_purchase_item_id);
            }

            //更新采购单总金额  毛利 毛利率
            foreach ($purIdArr as $_purId) {
                $purOrderService->updatePurOrderAboutAmount($_purId);
            }
        }


        \Log::channel("purFrqSyncLog")->info(sprintf("--------销售修改价格和供应商开始请求erp--%s-----------", time()));
        \Log::channel("purFrqSyncLog")->info(sprintf("--------请求erp参数:--%s-----------", json_encode($erpParams)));
        $res = (new ThirdErpService)->push(ThirdErpService::PUSH_TYPE_SOAP, 'updatesaleorder', $erpParams);
        \Log::channel("purFrqSyncLog")->info(sprintf("--------销售修改价格和供应商请求erp完成--%s-----------", time()));

        if ($res['code'] != 0) {
            $this->rollBackTransaction();
            throw new InvalidRequestException('同步明细价格到ERP失败，原因：' . $res['msg']);
        }
        //        var_dump($emailNoticeArr);die;
        //发送价格变动邮件
        self::sendEmailByFrqPriceChange($emailNoticeArr);
        $this->commitTransaction();
        //记录修改日志
        foreach ($logs as $log) {
            ActionLogService::addLog(ActionLogService::TYPE_ACTION_PURCHASE_DISABLE, $log["purchase_id"], ["message" => $log["message"]]);
        }
    }

    /**
     * Notes:修改价格和数量  以前是单独修改价格和数量 现在要合并起来
     * @param $requestData
     */
    public function editFrqByFrqPriceAndQty($requestData)
    {
        $orderItemsList = arrayGet($requestData, "items", "");
        $erpParams = arrayGet($requestData, "erp_params", "");
        // if (empty($erpParams) || empty($orderItemsList)) {
        //     throw new InvalidRequestException(sprintf("参数错误，erp_params为空"));
        // }
        $orderItemIdArr = Arr::pluck($orderItemsList, "order_item_id");
        $orderItemIdArr = array_filter_unique($orderItemIdArr);
        if (empty($orderItemIdArr)) {
            throw new InvalidRequestException(sprintf("参数错误"));
        }

        //记录日志
        $logs = [];
        $i = 0;
        $emailNoticeArr = [];
        foreach ($orderItemsList as $item) {
            $i++;
            //基本上只有一条数据
            $purItemList = PurchaseItemsModel::getPurItemListByOrderItemId([$item["order_item_id"]]);
            foreach ($purItemList as $purItem) {
                $i++;
                $logs[$i]["purchase_id"] = $purItem["purchase_id"];
                $logs[$i]["message"] = sprintf("销售修改了销售价格,型号：%s,编码:%s,原始含税销售价格:%s", $purItem["goods_name"], $purItem["goods_sn"], $purItem["sales_price"]);
                if ($item['sales_price'] < $purItem['sales_price']) {
                    $emailNoticeArr[] = [
                        "purItem"         => $purItem,
                        "new_sales_price" => $item['sales_price'],
                        "old_sales_price" => $purItem['sales_price'],
                        "purchase_id"     => $purItem['purchase_id'],
                        "order_item_id"   => $purItem['order_item_id'],
                        "frq_id"          => $purItem['frq_id'],
                    ];
                }
            }
        }
        $purOrderService = new PurOrderService();


        //查询商品明细的采购数量
        $orderItemQty = PurchaseItemsModel::getPurQtyByOrderItemId($orderItemIdArr);
        $orderItemQty = arrayChangeKeyByField($orderItemQty, "order_item_id");
        $order_item_id_arr = array_keys($orderItemQty);
        //获取对应的采购需求信息
        $frqList = FrqModel::getFrqInfoByOrderItemidArr($orderItemIdArr);
        $frqList = arrayChangeKeyByField($frqList, "order_item_id");
        //记录日志
        foreach ($orderItemsList as $item) {
            $i++;
            //基本上只有一条数据
            $purItemList = PurchaseItemsModel::getPurItemListByOrderItemId([$item["order_item_id"]]);
            foreach ($purItemList as $purItem) {
                $i++;
                $logs[$i]["purchase_id"] = $purItem["purchase_id"];
                $logs[$i]["message"] = sprintf("销售修改了数量,型号：%s,编码:%s,原始数量:%s", $purItem["goods_name"], $purItem["goods_sn"], $purItem["frq_qty"]);
            }
        }
        $this->startTransaction();
        try {
            //修改价格
            foreach ($orderItemsList as $item) {
                $order_item_id = arrayGet($item, "order_item_id", 0, "intval");
                $sales_price = arrayGet($item, "sales_price", "", "trim");
                $sales_without_tax_price = arrayGet($item, "sales_without_tax_price", "", "trim");
                if (!$order_item_id) {
                    throw new InvalidRequestException(sprintf("参数销售明细id是必须的"));
                }
                if (!$sales_price || !$sales_without_tax_price) {
                    throw new InvalidRequestException(sprintf("价格参数是必须的"));
                }
                //修改价格
                $sales_price = decimal_number_format($sales_price, DIGITS_SIX);
                $sales_without_tax_price = decimal_number_format($sales_without_tax_price, DIGITS_SIX);
                if (bccomp($sales_price, "0", 6) == -1 || bccomp($sales_without_tax_price, "0", 6) == -1) {
                    //如果两个数相等，则返回0
                    //如果左操作数比右操作数小，则返回 -1
                    //如果左操作数比右操作数大，则返回 1
                    //                    throw new InvalidRequestException(sprintf("修改的价格不能小于0"));
                }
                //是否存在发货通知单
                $purItemIdArr = PurchaseItemsModel::getItemsIdByOrderItemId($item["order_item_id"]);
                $purItemIdArr = $purItemIdArr ? $purItemIdArr->toArray() : [];
                $purItemIdArr = array_filter_unique($purItemIdArr);
                //判断是否有生成付款单
                FrqModel::updateFrqInfo([
                    "order_item_id" => intval($item["order_item_id"])
                ], [
                    "sales_price"             => $sales_price,
                    "sales_without_tax_price" => $sales_without_tax_price,
                ]);

                //修改采购明细的采购需求明细价格
                PurchaseItemsModel::updatePurItemInfo([
                    "order_item_id" => intval($item["order_item_id"])
                ], [
                    "sales_price"             => $sales_price,
                    "sales_without_tax_price" => $sales_without_tax_price,
                ]);

                $purIdArr = PurchaseItemsModel::getItemsIdByOrderId($item["order_item_id"]);
                $purIdArr = $purIdArr ? $purIdArr->toArray() : [];
                $purIdArr = array_filter_unique($purIdArr);

                //计算采购明细毛利 毛利率 税额
                foreach ($purItemIdArr as $_purchase_item_id) {
                    $purOrderService->updatePurOrderItemAboutAmount($_purchase_item_id);
                }

                //更新采购单总金额  毛利 毛利率
                foreach ($purIdArr as $_purId) {
                    $purOrderService->updatePurOrderAboutAmount($_purId);
                }
            }

            //修改数量
            foreach ($orderItemsList as $item) {
                $order_item_id = arrayGet($item, "order_item_id", 0, "intval");
                $frq_qty = arrayGet($item, "frq_qty", 0, "intval");
                if (!$order_item_id) {
                    throw new InvalidRequestException(sprintf("参数明细id是必须的"));
                }
                $after_sales_qty = 0; //售后数量
                $total_pur_qty = 0;   //已采购数量
                $total_return_qty = 0;//采购退货数量
                if (isset($orderItemQty[$order_item_id])) {
                    $after_sales_qty = $frqList[$order_item_id]["after_sales_qty"] ?? 0;
                    $total_pur_qty = $orderItemQty[$order_item_id]["total_pur_qty"];
                    $total_return_qty = $orderItemQty[$order_item_id]["total_return_qty"];//退货数量
                }

                $alreayPurNums = $total_pur_qty - $total_return_qty + $after_sales_qty;
                if (isset($orderItemQty[$order_item_id]) && $frq_qty < $alreayPurNums) {
                    throw new InvalidRequestException(sprintf("商品明细%s,修改数量(%s)不能小于采购已订货数量和售后数量之和(%s)，无法修改", $order_item_id, $frq_qty, $alreayPurNums));
                }
                $updataData["frq_qty"] = $frq_qty;
                //修改需求单信息
                FrqModel::updateFrqInfo([
                    "order_item_id" => intval($item["order_item_id"])
                ], $updataData);
                //修改采购明细的采购需求数量
                PurchaseItemsModel::updatePurItemInfo([
                    "order_item_id" => intval($item["order_item_id"])
                ], ["frq_qty" => $frq_qty - $after_sales_qty]);
            }
            $frqList = FrqModel::getFrqInfoByOrderItemidArr($order_item_id_arr);
            $frqIdArr = Arr::pluck($frqList, "frq_id");//需求单id
            $purOrderService = new PurOrderService;
            $emailNoticeArr = [];
            foreach ($frqList as $frqItem) {
                $oldQty = $frqItem["frq_qty"];
                $updateFrq = [
                    "frq_qty" => $frqItem["frq_qty"],
                ];
                if (($frqItem["procured_qty"] - $frqItem["return_qty"]) >= $frqItem["frq_qty"] - $frqItem["after_sales_qty"]) {
                    $updateFrq["status"] = 3;//完全采购
                } else {
                    if ($frqItem["procured_qty"] > 0 && ($frqItem["procured_qty"] - $frqItem["return_qty"]) < $frqItem["frq_qty"] - $frqItem["after_sales_qty"]) {
                        $updateFrq["status"] = 2;//部分采购
                    } else {
                        $updateFrq["status"] = 1;//待采购
                    }
                }
                //如果是完全采购变成了部分采购，需要发送邮件通知
                if ($frqItem["status"] == 3 && $updateFrq["status"] == 2) {
                    $emailNoticeArr[] = [
                        "frq_info" => $frqItem,
                        "old_qty"  => $oldQty,
                        "new_qty"  => $updateFrq['frq_qty'],
                    ];
                }

                //修改采购需求单信息
                FrqModel::updateFrqInfo([
                    "frq_id" => $frqItem["frq_id"]
                ], $updateFrq);
                //同步该需求到 分享和被分享得需求
                $purOrderService->syncFrqToFrq($frqItem["frq_id"]);
            }


            \Log::channel("purFrqSyncLog")->info(sprintf("--------销售修改价格和数量开始请求erp--%s-----------", time()));
            \Log::channel("purFrqSyncLog")->info(sprintf("--------请求erp参数:--%s-----------", json_encode($erpParams)));
            if(!empty($erpParams)){
                $res = (new ThirdErpService)->push(ThirdErpService::PUSH_TYPE_SOAP, 'updatesaleorder', $erpParams);
            }

            \Log::channel("purFrqSyncLog")->info(sprintf("--------销售修改价格和数量请求erp完成--%s-----------", time()));

            if ($res['code'] != 0) {
                throw new InvalidRequestException('同步明细价格到ERP失败，原因：' . $res['msg']);
            }

            $this->commitTransaction();
        } catch (\Throwable $e) {
            // dd((string)$e);
            $this->rollBackTransaction();
            throw new InvalidRequestException($e->getMessage());
        }
        //发送数量变动邮件
        self::sendEmailByFrqQtyChange($emailNoticeArr);

        //发送价格变动邮件
        self::sendEmailByFrqPriceChange($emailNoticeArr);

        //记录修改日志
        foreach ($logs as $log) {
            ActionLogService::addLog(ActionLogService::TYPE_ACTION_PURCHASE_UPDATE, $log["purchase_id"], ["message" => $log["message"]]);
        }
    }

    /*
     * 同步计划备货的状态
     */
    public function syncPurPlanStatus($frq_id_arr)
    {
        if (empty($frq_id_arr)) {
            return;
        }
        $frq_id_arr = array_filter_unique($frq_id_arr);
        $planItemIdArr = FrqModel::getPlanItemIdList($frq_id_arr);                      //通过采购需求明细后去采购计划明细id
        $purPlanId = PurchasePlanItemsModel::getPurPlanIdByPurItemIdArr($planItemIdArr);//通过代采购采购计划明细id获取采购计划id

        $planQty = PurchasePlanItemsModel::getPurPlanQtyLists($purPlanId);
        if (empty($planQty)) {
            return;
        }
        $planQty = arrayChangeKeyByField($planQty, "purchase_plan_id");

        $purPlanItemIdList = PurchasePlanItemsModel::getPurPlanItemIdList($purPlanId);
        //用purchase_plan_id作为键   提取plan_item_id作为数组的元素值
        $newpurPlanItemIdList = flipArrayPluck($purPlanItemIdList, "purchase_plan_id", "plan_item_id");

        //同步采购计划的状态
        foreach ($purPlanId as $purchase_plan_id) {
            //获取采购计划单的总计划备货数量
            $total_plan_qty = $planQty[$purchase_plan_id]["total_plan_qty"] ?? 0;//计划备货数量
            $_planItemIdArr = $newpurPlanItemIdList[$purchase_plan_id] ?? [];
            //查询所有备货明细的已采购数量
            $frqPurQty = FrqModel::getPurQtyByPlanItemId($_planItemIdArr);       //已采购数量
            if ($total_plan_qty > 0 && $frqPurQty >= $total_plan_qty) {
                $status = PurchasePlanModel::STATUS_COMPLETED;
            } else {
                $status = PurchasePlanModel::STATUS_NEED_PURCHASE;
            }
            PurchasePlanModel::updatePurPlan([
                "purchase_plan_id" => $purchase_plan_id
            ], [
                "status"      => $status,
                "update_time" => time()
            ]);
        }
    }


    /*
     * 售后锁定采购需求单
     */
    public function lockOrder($requestParams)
    {
        $lockOrderItemList = arrayGet($requestParams, "items");
        if (empty($lockOrderItemList)) {
            throw new InvalidRequestException("参数不能为空");
        }
        if (!is_array($lockOrderItemList)) {
            throw new InvalidRequestException("参数错误");
        }
        $orderItemIdArr = Arr::pluck($lockOrderItemList, "order_item_id");
        $orderItemIdArr = array_filter_unique($orderItemIdArr);
        if (empty($lockOrderItemList)) {
            throw new InvalidRequestException("参数不能为空");
        }

        //查询所有采购需求明细 包含锁定
        $frqList = FrqModel::getFrqInfoByOrderItemidArr($orderItemIdArr);

        $this->startTransaction();

        foreach ($frqList as $frqItem) {
            foreach ($lockOrderItemList as $lockOrderItem) {
                if ($lockOrderItem["order_item_id"] != $frqItem["order_item_id"]) {
                    continue;
                }
                $updateData = [
                    "after_sales_qty" => $lockOrderItem["op_num"]
                ];
                if ($lockOrderItem["types"] == 1) {                 //加锁
                    $updateData["status"] = FrqModel::InvalidStatus;//4:锁定
                } else {                                                        //解锁
                    $__frq_qty = $frqItem["frq_qty"] - $lockOrderItem["op_num"];//需求数量
                    if ($frqItem["procured_qty"] > 0 && $frqItem["procured_qty"] - $frqItem["return_qty"] >= $__frq_qty) {
                        $updateData["status"] = FrqModel::CompleteProcureStatus;//完全采购
                    } else {
                        if ($frqItem["procured_qty"] > 0 && $frqItem["procured_qty"] - $frqItem["return_qty"] < $__frq_qty) {
                            $updateData["status"] = FrqModel::PartOfTheProcureStatus;//部分采购
                        } else {
                            $updateData["status"] = FrqModel::WaitForTheProcureStatus;//待采购
                        }
                    }
                }
                FrqModel::updateFrq($frqItem["frq_id"], $updateData);
                (new PurOrderService)->syncFrqToFrq($frqItem["frq_id"]);
            }
        }

        $this->commitTransaction();
        return true;
    }

    /*
     * 新增采购单并发货 获取导入的数据
     */
    public function getAddPurOrderAndStockIn($request)
    {
        $file = $request->file("file");
        $comId = $request->input("com_id", 1);
        if (!$file) {
            throw new InvalidRequestException("文件错误");
        }
        ini_set('memory_limit', -1);
        $tables = Excel::toArray(new FrqImportPurOrderAndStock(), $file);
        $rows = isset($tables[0]) ? $tables[0] : [];
        if (!$rows) {
            throw new InvalidRequestException("文件错误");
        }
        if ($rows[0][0] != "*需求ID" || $rows[0][1] != "*订货数量" || $rows[0][6] != "*付款方式") {
            throw new InvalidRequestException("请检查上传的文件是否正确!");
        }

        if (count($rows[0]) != count(FrqImportPurOrderAndStock::$fieldArr)) {
            throw new InvalidRequestException("模板格式错误，可下载最新的模板进行上传!");
        }

        unset($rows[0]);//表头
        unset($rows[1]);//备注
        $loadData = [];
        foreach ($rows as $row) {
            $i = 0;
            $data = [];
            if (empty($row[0])) {
                continue;
            }
            foreach ($row as $val) {
                if (!isset(FrqImportPurOrderAndStock::$fieldArr[$i])) {
                    break;
                }
                $val = !empty($val) ? $val : "";
                $data[FrqImportPurOrderAndStock::$fieldArr[$i]] = trim($val);
                $i++;
            }

            $loadData[] = $data;
        }

        //处理excel时间格式
        //return string
        $handlTime = function ($time_str) {
            $newTime = 0;
            try {
                $estimat_delivery_time = \Carbon\Carbon::instance(\PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($time_str));
                if (is_object($estimat_delivery_time)) {
                    $newTime = $estimat_delivery_time->toDateString();
                } else {
                    $newTime = "";
                }
            } catch (\Exception $e) {
                $patten = '/^\d{4}(\-|\/|.)\d{1,2}\1\d{1,2}$/';
                if (preg_match($patten, trim($time_str))) {
                    $newTime = $time_str;
                }
            }
            $newTime = strtotime($newTime);
            if ($newTime <= strtotime("2021-01-01")) {
                $newTime = 0;
            }
            return $newTime > 0 ? date("Y-m-d", $newTime) : "";
        };

        //获取产地列表
        $orgPlaces = OriginalPlaceModel::getAllOrigins();

        $orgPlaces = arrayChangeKeyByField($orgPlaces, "id");


        $origin = OriginalPlaceModel::getAllOrigins(1); // 获取产地
        $name_cns = array_keys($origin);
        $name_ens = array_values($origin);
        $origin_names = array_merge($name_cns, $name_ens);


        $purchaseItemsFrqIdArr = Arr::pluck($loadData, "frq_id");
        $purchaseItemsFrqIdArr = array_unique(array_filter($purchaseItemsFrqIdArr));
        $purchaseItemsFrqList = FrqModel::getPurOrderListById($purchaseItemsFrqIdArr);
        $purchaseItemsFrqList = arrayChangeKeyByField($purchaseItemsFrqList, "frq_id");

        $purchaseItemsOrderItemIdArr = Arr::pluck($purchaseItemsFrqList, "order_item_id");
        $purchaseItemsOrderItemIdArr = array_unique(array_filter($purchaseItemsOrderItemIdArr));
        $purchaseItemsOrderItemList = OrderItemsModel::getOrderItemsByIds($purchaseItemsOrderItemIdArr,["rec_id","status"],[
            ["status","=",1]
        ]);
        $purchaseItemsOrderItemList = arrayChangeKeyByField($purchaseItemsOrderItemList, "rec_id");

        $frqModel = new FrqModel();
        //验证数据合法性
        foreach ($loadData as $k => $v) {
            $loadData[$k]["check_status"] = 0; //0正常  1错误
            $loadData[$k]["check_errMsg"] = "";//错误信息
            $loadData[$k]["goods_name"] = "";
            $loadData[$k]["brand_name"] = "";
            $loadData[$k]["goods_sn"] = "";

            if (!$v["frq_id"] && !$v["purchase_qty"] && !$v["price"] && !$v["currency"]) {
                continue;
            }

            $log = "";
            if (!$v["frq_id"]) {
                $log .= " 需求id必填, ";
            }
            if (!$v["purchase_qty"]) {
                $log .= " 订货数量必填, ";
            }
            // if (!$v["price"]) {
            //    $log .= " 采购单价必填, ";
            // }
            if (!$v["currency"]) {
                $log .= " 币别编码必填, ";
            }
            if (!$v["supplier_sn"]) {
                $log .= " 供应商编码必填, ";
            }
            if (!$v["warehouse_id"]) {
                $log .= " 交货仓必填, ";
            }
            if (!$v["pay_id"]) {
                $log .= " 付款方式必填, ";
            }


            //必填物流公司和物流单号
            if (!$v['shipping_id']) {
                $log .= " 物流公司不能为空, ";
            }
            if (!in_array($v['shipping_id'], [10, 100])) {//10-自提,100-供应商配送
                if (!$v['shipment_number']) {
                    $log .= " 物流单号不能为空, ";
                }
            }
            //判断采购单价是否合法
            if (floatval(strval($v["price"])) <= 0.00000001) {
                $loadData[$k]["price"] = 0.000000;
            }
            //判断币别是否合法
            if (!in_array(strtoupper($v["currency"]), config("field.CurrencyEn"))) {
                $log .= " 币别编码错误, ";
            }


            if (intval($v["purchase_qty"]) < 1) {
                $log .= " 订货数量不能必须大于0, ";
            }

            $loadData[$k]["pay_name"] = Arr::get(PurchaseOrderModel::$PAY_TYPE, intval($v["pay_id"]), "");
            if (!$loadData[$k]["pay_name"]) {
                $log .= " 没找到对应的付款方式, ";
            }


            $loadData[$k]["shipping_name"] = "";
            if ($v["shipping_id"]) {
                $loadData[$k]["shipping_name"] = \Arr::get(config("field.ShippingList"), intval($v["shipping_id"]), "");
            }


            $loadData[$k]["shipment_number"] = $v["shipment_number"] = LogisticsApiService::handleShippingNumber($v["shipment_number"]);
            try {
                LogisticsApiService::checkShippingNumber($v["shipment_number"]);
            } catch (\Exception $e) {
                $log .= sprintf("%s,", $e->getMessage());
            }


            $loadData[$k]["warehouse_name"] = \Arr::get(config("field.Warehouse"), intval($v["warehouse_id"]), "");
            if (!$loadData[$k]["warehouse_name"]) {
                $log .= " 没找到对应交货仓, ";
            }

            if (isset($v["delivery_time"]) && $v["delivery_time"]) {
                $loadData[$k]["delivery_time"] = $handlTime(trim($v["delivery_time"]));
            }


            if (isset($v["estimate_arrival_time"]) && $v["estimate_arrival_time"]) {
                $loadData[$k]["estimate_arrival_time"] = $handlTime(trim($v["estimate_arrival_time"]));
            }


            //判断需求id是否有权限操作
            // $getOwnFrqId = FrqModel::getOwnFrqId(intval($v["frq_id"]));
            $getOwnFrqId = $frqModel->checkHasoOperationPerm(intval($v["frq_id"]));

            if (!$getOwnFrqId) {
                $log .= " 您没有操作该需求权限操作, ";
            } else {
                $getOwnFrqId = $purchaseItemsFrqList[$v["frq_id"]] ?? [];
                $loadData[$k]["goods_name"] = $getOwnFrqId["goods_name"];
                $loadData[$k]["brand_name"] = $getOwnFrqId["brand_name"];
                $loadData[$k]["goods_sn"] = $getOwnFrqId["goods_sn"];
                $loadData[$k]["purchase_uid"] = $getOwnFrqId["purchase_uid"];
                $loadData[$k]["purchase_name"] = $getOwnFrqId["purchase_name"];
                $loadData[$k]["frq_type"] = $getOwnFrqId["frq_type"];
                if ($v["purchase_qty"] > ($getOwnFrqId["frq_qty"] - $getOwnFrqId["procured_qty"] - $getOwnFrqId["after_sales_qty"] + $getOwnFrqId["return_qty"])) {
                    $log .= " 订货数量不能大于需求数量, ";
                }
                if ($getOwnFrqId['frq_type'] == FrqModel::FRQ_TYPE_MRO) {
                    $log .= "MRO需求不允许批量导入";
                }
                if ($getOwnFrqId["status"] == FrqModel::TransferStatus) {//如果是分享的需求
                    $log .= "已转让需求不允许批量导入";
                }

                if (empty($purchaseItemsOrderItemList[$getOwnFrqId["order_item_id"]]) || $purchaseItemsOrderItemList[$getOwnFrqId["order_item_id"]]["status"] != OrderItemsModel::OrderItemStatusNormal) {
                    $log .= "关联的销售订单明细不存在或已取消";
                }
            }


            try {
                // var_dump($getOwnFrqId, $v['date_code']);
                PurOrderApiService::checkDateCode($getOwnFrqId, $v['date_code']);
            } catch (\Throwable $exception) {
                $log .= sprintf(" %s, ", $exception->getMessage());
            }
            try {
                //验证dc和产地
                PurOrderApiService::checkPlaceOfOrigin($getOwnFrqId, $v["place_of_origin"]);
            } catch (\Throwable $exception) {
                $log .= sprintf(" %s, ", $exception->getMessage());
            }

            /*
                填写了产地时，需要校验一下入仓单号
                1、入仓单号为B单，FB单，产地必须填写中文，选择英文导入报错；
                2、导入的产地不是通过下拉列表选择的数据，报错提示：产地错误，请选择下拉框中的数据进行填写；
                3、填写了产地，没有填写"产地展示"，则报错提示：请填写"产地展示"字段；
            */ //            if (!empty($v["place_of_origin"]) && !empty($v["warehouse_receipt_sn"])) {
            //                if (!in_array($v["place_of_origin"], $origin_names)) {
            //                    $log .= " 产地错误，请选择下拉框中的数据进行填写, ";
            //                }
            //
            //                $type = WarehouseReceiptSnService::getWarehouseReceiptSnType(trim($v["warehouse_receipt_sn"]));
            //                if (in_array($type, [WarehouseReceiptSnService::TYPE_B, WarehouseReceiptSnService::TYPE_FB])
            //                    && !preg_match('/^[\x{4e00}-\x{9fa5}]+$/u', $v["place_of_origin"])) {
            //                    $log .= " 入仓单号为B单、FB单，产地必须填写中文, ";
            //                }
            //            }

            $loadData[$k]["check_errMsg"] = $log;
            if ($loadData[$k]["check_errMsg"]) {
                $loadData[$k]["check_status"] = 1;//表示有错误
            }
        }

        //判断供应商
        $suppSn = Arr::pluck($loadData, "supplier_sn");
        if (!empty($suppSn)) {
            $suppSn = array_filter_unique($suppSn);
            foreach ($suppSn as &$spuVal) {
                $spuVal = strtoupper($spuVal);
            }
        }
        $supplierMap = SupplierService::getSupplierMapListBySn($suppSn);

        $frqIds = \Arr::pluck($loadData, "frq_id");
        $frqList = FrqModel::getFrqListByAddPur($frqIds);
        $frqList = arrayChangeKeyByField($frqList, "frq_id");


        foreach ($supplierMap as $supplierSn => $supInfo) {
            try {
                EarlyWarningApiService::checkSupplierEarlyWarning($supInfo);
                PurOrderService::checkSupplierIsCanAddOrder($comId, $supInfo["supplier_name"]);
            } catch (\Exception $e) {
                $supplierMap[$supplierSn]["error_msg"] = $e->getMessage();
            }
        }
        $signCompanyMap = SignCompanyService::getSignCompanyMap(100);
        foreach ($loadData as $k => $v) {
            $loadData[$k]["supplier_id"] = 0;
            $loadData[$k]["supplier_name"] = "";
            if (isset($supplierMap[$v["supplier_sn"]])) {
                $loadData[$k]["supplier_name"] = $supplierMap[$v["supplier_sn"]]["supplier_name"];
                $loadData[$k]["sign_company_id"] = $supplierMap[$v["supplier_sn"]]["sign_com_id"] ?? 0;
                $loadData[$k]["sign_company_name"] = $signCompanyMap[$loadData[$k]["sign_company_id"]]["com_name"];
                $loadData[$k]["supplier_id"] = $supplierMap[$v["supplier_sn"]]["supplier_id"];
                $loadData[$k]["is_qc"] = (int)SupplierService::checkSupplierIsQc($supplierMap[$v["supplier_sn"]]["supplier_group"], $supplierMap[$v["supplier_sn"]]["supplier_code"]);
            } else {
                $loadData[$k]["check_errMsg"] .= " 没找到对应的供应商, ";
                $loadData[$k]["check_status"] = 1;//表示有错误
            }

            if(!isset($loadData[$k]["sign_company_id"])){
                $loadData[$k]["check_errMsg"] .= " 没找到对应的供应商的签约公司, ";
                $loadData[$k]["check_status"] = 1;//表示有错误
            }

            $supplierCode = $supplierMap[$v["supplier_sn"]]["supplier_code"] ?? "";
            $daiGouSUP = config("config.DaiGouSUP");//代购供应商编码列表
            if (empty($v["estimate_arrival_time"]) && !in_array(strtolower($supplierCode), $daiGouSUP)) {
                $loadData[$k]["check_errMsg"] .= " 预计到货时间必填, ";
                $loadData[$k]["check_status"] = 1;//表示有错误
            }

            if (!empty($v["warehouse_receipt_sn"])) {
                //入仓单号不为空 需要判断合法性
                try {
                    WarehouseReceiptSnService::checkWarehouseReceiptSnCondition($loadData[$k]["sign_company_id"], trim($v["warehouse_receipt_sn"]), $getOwnFrqId["sales_currency"], $getOwnFrqId["frq_type"]);
                } catch (\Throwable $e) {
                    $loadData[$k]["check_errMsg"] .= sprintf(" %s, ", $e->getMessage());
                    $loadData[$k]["check_status"] = 1;//表示有错误
                }

                $type = WarehouseReceiptSnService::getWarehouseReceiptSnType(trim($v["warehouse_receipt_sn"]));
                if ($type == WarehouseReceiptSnService::TYPE_FB && empty($v["place_of_origin"])) {
                    $loadData[$k]["check_errMsg"] .= " FB单必填产地, ";
                    $loadData[$k]["check_status"] = 1;//表示有错误
                }


                //采购组织为深茂,交货地为香港,入仓单号为C单时 仓库必须为香港仓库(107)
                if ($comId == PurchaseOrderModel::SHENMAO && $v["warehouse_id"] != 107 && in_array(substr(trim($v["warehouse_receipt_sn"]), 0, 1), [
                        "C",
                        "c"
                    ])) {
                    $loadData[$k]["check_errMsg"] .= " 采购组织为深茂,交货地为香港,入仓单号为C单时 仓库必须为香港仓库(107), ";
                    $loadData[$k]["check_status"] = 1;//表示有错误
                }
                if (in_array($type, [WarehouseReceiptSnService::TYPE_FB, WarehouseReceiptSnService::TYPE_B])) {
                    $loadData[$k]["check_errMsg"] .= " B单不支持自动发货, ";
                    $loadData[$k]["check_status"] = 1;//表示有错误
                }
            }

            if (empty($supInfo["supplier_address"])) {
                $loadData[$k]["check_errMsg"] .= " 供应商注册地址为空，请补充供应商注册地址后再下单, ";
                $loadData[$k]["check_status"] = 1;//表示有错误
            }

            if (!empty($supplierMap[$v["supplier_sn"]]["error_msg"])) {
                $loadData[$k]["check_errMsg"] .= $supplierMap[$v["supplier_sn"]]["error_msg"];
                $loadData[$k]["check_status"] = 1;//表示有错误
            }

            $loadData[$k]["company_id"] = $frqList[$v["frq_id"]]["company_id"] ?? 0;
            //            if($loadData[$k]["company_id"] != $comId){
            if ($comId == 3 && $loadData[$k]["company_id"] != 3) {
                $loadData[$k]["check_errMsg"] .= " 销售组织不一致, ";
                $loadData[$k]["check_status"] = 1;//表示有错误
            }

            $placeOfOrigin = !empty($v["place_of_origin"]) ? $v["place_of_origin"] : "";
            $loadData[$k]["map_coo"] = "";
            $loadData[$k]["upload_coo"] = $placeOfOrigin;
            if (!empty($placeOfOrigin)) {
                $mapCooId = SupplierDeliveryService::getMapCoo($orgPlaces, $placeOfOrigin);
                if ($placeOfOrigin && $mapCooId && !empty($orgPlaces[$mapCooId])) {
                    $loadData[$k]["map_coo"] = sprintf("%s", $orgPlaces[$mapCooId]["name_cn"] ?? "");
                }
            }

            $loadData[$k]["currency"] = $loadData[0]["currency"];
        }

        return $loadData;
    }


    public function getImportFrqPriceStatistics($company_id, $list)
    {
        $arr = [
            "tax_money"         => 0,
            "order_total_money" => 0,
            "total_money"       => 0,
        ];
        //        $tax_rate = config("field.TaxRate");//税率 默认1.13   如果是美金单 税率等于1
        //        $tax_rate = in_array($company_id,PurCommonEnum::$TAX_COMPANY)  ? $tax_rate : 1;

        $orgCurrencyRateAndTaxInfo = CommonService::getOrgCurrencyRateAndTaxInfo($company_id);
        $tax_rate = (float)$orgCurrencyRateAndTaxInfo["tax_rate"];

        foreach ($list as $item) {
            if ($item["check_status"] == "1") {
                continue;
            }
            if (in_array($company_id, PurCommonEnum::$TAX_COMPANY)) {//猎芯科技 只支持人明币
                $price_in_tax = $item["price"];                      //含税采购单价
                $price_in_tax = decimal_number_format($price_in_tax, DIGITS_SIX);
                $price_without_tax = decimal_number_format($price_in_tax / $tax_rate, DIGITS_SIX);//未税采购单价
            } else {                                //深茂电子
                $tax_rate = 1;                      //美金单税率为1
                $price_without_tax = $item["price"];//含税采购单价
                $price_without_tax = decimal_number_format($price_without_tax, DIGITS_SIX);
                $price_in_tax = $price_without_tax;//美金,港币单含税等于未税
            }
            $purchaseQty = (int)$item["purchase_qty"];

            $currencyId = array_search($item["currency"], config("field.CurrencyEn"));
            $exchangeRate = $orgCurrencyRateAndTaxInfo["exchange_rate_list"][$currencyId] ?? 0;
            if (empty($exchangeRate)) {
                throw new InvalidRequestException(sprintf("没找到币种%s对应的汇率信息", $item["currency"]));
            }

            $arr["tax_money"] += round(($price_in_tax - $price_without_tax) * $purchaseQty * $exchangeRate, 2);
            $arr["order_total_money"] += round($price_without_tax * $purchaseQty * $exchangeRate, 2);
            $arr["total_money"] += round($price_in_tax * $purchaseQty * $exchangeRate, 2);
        }


        $standCurrency = arrayGet(PurCommonEnum::$standardCurrency, $company_id);
        $arr["tax_money"] = decimal_number_format($arr["tax_money"], DIGITS_TWO, $standCurrency);
        $arr["order_total_money"] = decimal_number_format($arr["order_total_money"], DIGITS_TWO, $standCurrency);
        $arr["total_money"] = decimal_number_format($arr["total_money"], DIGITS_TWO, $standCurrency);
        return $arr;
    }


    /*
     * 新增采购单并发货  提交按钮
     * 批量采购单并发货
     */
    public function addFrqToPurOrderAndStockIn($requestParams)
    {
        //        $list = $this->addPurOrderAndStockIn($request);
        //        $list = $request->input("list", "");
        $list = arrayGet($requestParams, "list", "");
        $type = arrayGet($requestParams, "type", "0", "intval");
        if (empty($list)) {
            throw new InvalidRequestException("参数错误");
        }
        try {
            $list = json_decode($list, true);
        } catch (\Throwable $e) {
            $list = [];
        }

        //        $type = $request->input("type", 0);
        if (!$type || !in_array($type, [1, 2])) {
            throw new InvalidRequestException("请选择一单一品,还是一单多品?");
        }
        $sucNum = 0;
        $failNum = 0;
        $errorMsg = [];
        switch ($type) {
            case self::ASingleProduct://一单一品：一条明细生成一个采购单  一条明细生成一个发货通知单
                list($sucNum, $failNum, $errorMsg) = (new PurOrderService())->singleProduct($requestParams, $list);

                break;
            case self::MoreSingleProduce://一单多品   所有明细生成一个采购单  所有明细生成一个发货通知单(发货单目前一单一品)
                try {
                    (new PurOrderService())->moreThanSingleProduct($requestParams, $list);
                    $sucNum = 1;
                } catch (\Exception $e) {
                    $failNum = 1;
                    \Log::channel("purFrqSyncLog")->info(sprintf("导入采购单并生成发货通知单,一单多品  失败：%s", $e->getMessage()));
                    array_push($errorMsg, $e->getMessage());
                }
                break;
        }
        PurLockService::delLock();
        return [$sucNum, $failNum, $errorMsg];
    }


    /*
     * 作废采购单-》释放采购数量到需求单  更改需求单状态
     * 删除采购单-》释放采购数量到需求单  更改需求单状态
     * params $data  采购明细 s数组格式
     */
    public function changeProcuredQtyAndStatus($frqIdArr)
    {
        $frqIdArr = array_filter_unique($frqIdArr);
        foreach ($frqIdArr as $frq_id) {
            if (empty($frq_id)) {
                continue;
            }
            //同步采购明细的采购数量到需求数量
            self::syncPurItemQtyToFrqByFrqId($frq_id);
            //同步该采购需求到分享和被分享的需求
            (new PurOrderService)->syncFrqToFrq($frq_id);
        }
    }

    public static function confirmOrder($frqIds)
    {
        $frqList = FrqModel::getFrqListByIds($frqIds);
        $orderItemIds = [];
        $orderItemLogArr = [];
        foreach ($frqList as $frq) {
            if ($frq["frq_type"] != FrqModel::FRQ_TYPE_ORDER) {
                continue;
            }
            if ($frq["status"] > FrqModel::PartOfTheProcureStatus) {
                continue;
            }
            FrqModel::updateFrq($frq['frq_id'], [
                "handle_time" => time(),
            ]);
            $orderItemLogArr[] = ["id" => $frq['order_item_id'], "message" => "采购员确认可以采购"];
            $orderItemIds[] = $frq["order_item_id"];
        }
        $orderItemList = OrderItemsModel::getOrderItemsByIds($orderItemIds);
        $logArr = [];
        foreach ($orderItemList as $orderItem) {
            $logArr[] = [
                "action_type" => ActionLogService::TYPE_ACTION_ORDER_PUR_CONFIRM,
                "obj_id"      => $orderItem["order_id"],
                "log_data"    => ["message" => "采购员确认采购。商品编号:{$orderItem['goods_name']}"],
            ];
        }

        //        if ($orderItemIds) {
        //            DeliveryLogSyncOrderService::purConfirm($orderItemIds);
        //        }
        ActionLogService::addMultiLogs($logArr);
        DeliveryLogSyncOrderService::addLogByLogDataArr($orderItemLogArr, "采购需求确认", "采购系统-" . request()->user->name);
    }


    /**
     * Notes:判断贸泽的销售单是否可以直接采购
     * User: sl
     * Date: 2023-02-23 15:50
     */
    public function syncReqMaoZeISCanAddOrder($order_item_id, $channel, $is_compulsion)
    {
        //判断贸泽的销售单是否可以直接采购
        $pushService = new ThirdErpService();
        $pushService->setQueueName("lie_queue_pur")->push(ThirdErpService::PUSH_TYPE_SYNC_HTTP, '/sync/syncReqMaoZeISCanAddOrder', [
            "order_item_id" => $order_item_id,
            "channel"       => $channel ?: 1,
            "is_compulsion" => $is_compulsion ?: 0,
        ], (string)$order_item_id);
    }

    public function reqMaoZeISCanAddOrder($requestParams)
    {
        $order_item_id = arrayGet($requestParams, "order_item_id", 0, "intval");
        $is_compulsion = arrayGet($requestParams, "is_compulsion", 0, "intval");//是否强制请求第三方接口 1强制
        $channel = arrayGet($requestParams, "channel", 1, "intval");            //1贸泽
        $frqInfo = FrqModel::getFrqInfoByOrderItemId($order_item_id);
        if (empty($frqInfo) || empty($frqInfo["supplier_name"])) {
            throw new InvalidRequestException(sprintf("没有找到对应的需求单据:order_item_id:%s", $order_item_id));
        }

        if (!in_array($frqInfo["supplier_name"], config("config.maoze_supname"))) {
            return;
        }

        if (!$is_compulsion && $frqInfo["is_add_order"] != FrqModel::$IS_ADD_ORDER["IS_ADD_ORDER_PENDING"]) {
            //已经请求过了或者有默认值跳过
            return;
        }
        switch ($channel) {
            case 1:
                $this->requestMaoZeSkuInfo($order_item_id, $frqInfo["goods_name"]);
                //                $this->requestMaoZeSkuInfo($order_item_id,"NCP303150DMNTWG");
                break;
        }
    }

    public function requestMaoZeSkuInfo($order_item_id, $keyword = "")
    {
        try {
            $url = sprintf("%s/mouser/a?k=%s&flag=5", config("config.search_url"), $keyword);
            $res = Http::timeout(60)->get($url);
            $res = $res->body();
            $result = json_decode($res, true);
        } catch (\Throwable $e) {
            throw new InvalidRequestException(sprintf("请求第三方接口失败:%s", $e->getMessage()));
        }

        if (empty($result["SearchResults"]["Parts"])) {
            throw new InvalidRequestException("暂无查询结果");
        }
        $parts = $result["SearchResults"]["Parts"];
        $isAddOrder = FrqModel::$IS_ADD_ORDER["IS_ADD_ORDER_PENDING"];
        foreach ($parts as $item) {
            if ($item["ManufacturerPartNumber"] == $keyword && isset($item["RestrictionMessage"]) && $item["RestrictionMessage"]) {
                $isAddOrder = FrqModel::$IS_ADD_ORDER["IS_ADD_ORDER_NO"];
                break;
            } elseif ($item["ManufacturerPartNumber"] == $keyword && !isset($item["RestrictionMessage"])) {
                $productCompliance = $item["ProductCompliance"] ?? [];
                if (!empty($productCompliance)) {
                    $productCompliance = arrayChangeKeyByField($productCompliance, "ComplianceName");
                    $complianceValue = $productCompliance["ECCN"]["ComplianceValue"] ?? "";
                    if ($complianceValue == "EAR99") {
                        $isAddOrder = FrqModel::$IS_ADD_ORDER["IS_ADD_ORDER_OK"];
                        break;
                    }
                }
            }
        }
        FrqModel::where("order_item_id", $order_item_id)->update([
            "is_add_order" => $isAddOrder,
            "update_time"  => time(),
        ]);
    }

    /**
     *
     */
    public static function syncReturnQtyToFrq($rmaIds = [])
    {
        \Log::channel("purFrqSyncLog")->info(sprintf("退货单完成：退货单id：%s", json_encode($rmaIds)));
        $purchaseItemIds = ReturnMaterialItemModel::whereIn("rma_id", $rmaIds)->whereHasIn("return_material", function ($q) {
            $q->where("rma_type", 2)->whereIn("status", [2, 3]);
        })->pluck("purchase_item_id")->toArray();
        if (empty($purchaseItemIds)) {
            return;
        }

        $frqIds = PurchaseItemsModel::whereIn("purchase_item_id", $purchaseItemIds)->pluck("frq_id")->toArray();
        if (empty($frqIds)) {
            return;
        }

        $frqList = FrqModel::whereIn("frq_id", $frqIds)->get()->toArray();
        $frqList = arrayChangeKeyByField($frqList, "frq_id");
        $returnQty = self::getReturnQty($frqIds);
        try {
            $purOrderService = new PurOrderService;
            foreach ($frqList as $frqId => $frqInfo) {
                $rQty = $returnQty[$frqId] ?? 0;
                if ($rQty <= 0) {
                    continue;
                }
                //更新采购需求 退货数量
                FrqModel::where("frq_id", $frqId)->update([
                    "return_qty" => $rQty
                ]);
                //更新采购需求状态 采购数量
                self::syncPurItemQtyToFrqByFrqId($frqId);
                //同步该采购需求到分享和被分享的需求
                $purOrderService->syncFrqToFrq($frqId);
            }
        } catch (\Exception $e) {
            throw new InvalidRequestException(sprintf("更新采购需求退货数量失败:%s", $e->getMessage()));
        }
    }

    public static function getSupplierMapBySkuIdArr($skuIdArr)
    {
        if (empty($skuIdArr)) {
            return [];
        }

        $goodsList = MROSyncService::requestGoodsServer($skuIdArr);
        $supplierCodeArr = array_column($goodsList, "canal");
        $supplierList = SupplierChannelModel::getSupplierListBySupplierCodeArr($supplierCodeArr);
        $supplierList = arrayChangeKeyByField($supplierList, "supplier_code");
        $map = [];
        foreach ($skuIdArr as $skuId) {
            $goodsInfo = $goodsList[$skuId] ?? [];
            $supplierCode = $goodsInfo["canal"] ?? "";
            $supplierInfo = $supplierList[$supplierCode] ?? [];
            $map[$skuId] = [
                "supplier_id"   => $supplierInfo["supplier_id"] ?? 0,
                "supplier_name" => $supplierInfo["supplier_name"] ?? "",
            ];
        }
        return $map;
    }

    public function getSameModelBrandSkuList($goodsName, $brandName)
    {
        // Prepare request data
        $requestData = [
            "goods_name_origin/eq"   => $goodsName,
            "standard_brand_name/eq" => $brandName,
            "status/eq"              => 1, // 1 for not expired
            "goods_status/eq"        => 1, // 1 for listed
            "offset"                 => 1000,
            "p"                      => 1,
        ];
        // API endpoint
        $url = config('website.footstone_new_url') . '/open/searchAbSku';

        // Make the API request
        $response = Http::asJson()->post($url, $requestData);
        $body = $response->body();
        $res = json_decode($body, true);
        $data = $res['data'] ?? [];
        //根据履约程度ability_level排序
        $data = arraySort($data, "ability_level", SORT_DESC);
        foreach ($data as $k=>$v){
            if (empty($v['encoded_userid'])){
                unset($data[$k]);
            }
        }
        $data = array_values($data);
        // Process and return the data
        return [
            'list'         => $data,
            'requestData'  => $requestData,
            "responseData" => $body,
            'url'          => $url,
        ];
    }

    public static function updateGoodsClassFirst($goodsName,$goodsClassFirst)
    {
        DB::transaction(function ()use($goodsName,$goodsClassFirst){
            FrqModel::updateGoodsClassFirst($goodsName, $goodsClassFirst);
            PurchaseItemsModel::updateGoodsClassFirst($goodsName, $goodsClassFirst);
        });

    }


    public static function editFrqSaler($sale_id,$sale_name,$rec_id_arr)
    {

        FrqModel::whereIn("order_item_id",$rec_id_arr)->update([
            "seller_uid"=>$sale_id,
            "seller_name"=>$sale_name,
        ]);
    }

}
