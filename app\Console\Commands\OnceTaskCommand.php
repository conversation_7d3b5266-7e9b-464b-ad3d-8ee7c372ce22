<?php

namespace App\Console\Commands;


use App\Exceptions\InvalidRequestException;
use App\Http\Models\CmsUserDepartmentModel;
use App\Http\Models\CmsUserInfoModel;
use App\Http\Models\FrqModel;
use App\Http\Models\Order\GoodsModel;
use App\Http\Models\PaymentItemsModel;
use App\Http\Models\PaymentModel;
use App\Http\Models\PurchaseApproveModel;
use App\Http\Models\PurchaseContractModel;
use App\Http\Models\PurchaseItemsModel;
use App\Http\Models\PurchaseOrderEndCustExtendModel;
use App\Http\Models\PurchaseOrderModel;
use App\Http\Models\Qc\QcModel;
use App\Http\Models\ReturnMaterialItemModel;
use App\Http\Models\ReturnMaterialModel;
use App\Http\Models\ScmOrderModel;
use App\Http\Models\ScmOrderTemporaryModel;
use App\Http\Models\StockInItemModel;
use App\Http\Models\StockInModel;
use App\Http\Models\Wms\WmsStockInItemModel;
use App\Http\Queue\RabbitQueueModel;
use App\Http\Services\ActionLogService;
use App\Http\Services\ApproveService;
use App\Http\Services\EndCustService;
use App\Http\Services\FrqService;
use App\Http\Services\PaymentService;
use App\Http\Services\PurOrderService;
use App\Http\Services\ScmOrderService;
use App\Http\Services\SupplierDeliveryService;
use App\Http\Services\SupplierService;
use App\Http\Services\Sync\DeliveryLogSyncOrderService;
use App\Http\Services\Sync\PurchaseOrderSyncService;
use App\Http\Services\Sync\ScmOrderSyncService;
use App\Http\Services\Sync\StockInSyncWMSService;
use App\Http\Services\ThirdErpService;
use App\Http\Services\WarehouseReceiptSnService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;

class OnceTaskCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:once:task';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '临时一次性任务处理';


    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle($arguments = [])
    {
        ini_set('memory_limit', '512M');
        // echo "请输入入库明细id:\n";
        // $str = fgets(STDIN);
        // $str = trim($str);
        // if (!$str) {
        //     throw new InvalidRequestException('入库明细id不能为空');
        // }
        request()->offsetSet("user", (object)["userId" => 1000, "name" => "admin"]);
        // SupplierDeliveryService::batchCancelSupDelivery(explode(",",$str), "技术修复数据",false);
        $this->gross_profit();
    }

    public function gross_profit()
    {

        $purOrderService = new PurOrderService();
        \DB::connection("mysql")->table('lie_purchase_order')->select("*")
            ->where("status",">",-3)
            ->where("create_time",">",1748707200)
            ->orderBy("purchase_id","asc")
            ->chunk(10, function ($purorderinfo) use($purOrderService){
                foreach($purorderinfo as $purorder){
                    dump($purorder->purchase_sn);
                    $puritems = PurchaseItemsModel::where("purchase_id",$purorder->purchase_id)->where("status",1)->get()->toArray();
                    foreach($puritems as $puritem){
                        //修改采购明细的毛利 毛利率 税额
                        $purOrderService->updatePurOrderItemAboutAmount($puritem["purchase_item_id"]);
                    }
                    //更新采购单总金额  毛利 毛利率
                    $purOrderService->updatePurOrderAboutAmount($purorder->purchase_id);  
                }
            });


    }

    public function syncReturnQtyToFrq()
    {

        ReturnMaterialModel::where("rma_type",2)
            ->whereIn("status",[2,3])
            ->select("rma_id","rma_sn")->orderBy("rma_id")->chunk(100, function ($rmaList) {
                foreach($rmaList as $item){
                    FrqService::syncReturnQtyToFrq([
                        $item->rma_id
                    ]);
                }
            });

    }

    public function initPaymentKingDeeSn($id)
    {
        $payment_info = PaymentModel::where("payment_id", "=", $id)->first();
        // 如果金额大于0，那说明是付款的，付款的同步到金蝶那个节点后，还要金蝶审核，还有后续的流程
        // 如果金额小于0，那说明是退款的，退款的走原来的流程，也就是同步到金蝶的那一刻就结束了，审核完成
        if ($payment_info['payment_amount_total'] > 0) {
            $QueueModel = new RabbitQueueModel();
            $QueueModel->insertQueue("/queue/payment/asyncToErp", [
                "payment_id" => $payment_info['payment_id'],
            ]);
        } else {
            $QueueModel = new RabbitQueueModel();
            $QueueModel->insertQueue("/queue/payment/asyncToErp", [
                "payment_id" => $payment_info['payment_id'],
            ]);
            PaymentService::auditPass($payment_info['payment_id']);
        }
        var_dump($payment_info);
    }


    public function initFrqDeliveryPlace()
    {
        \DB::connection("mysql")->table('lie_frq')->select("*")
            ->whereIn("frq_type",[1,3,4])
            ->orderBy("frq_id","desc")
            ->chunk(500, function ($frqList) {
                foreach($frqList as $frq){
                  dump($frq->goods_name);
                    if($frq->sales_currency == 1){
                        FrqModel::where("frq_id",$frq->frq_id)->update([
                            "delivery_place"=>1
                        ]);
                    }
                    if($frq->sales_currency == 2){
                        FrqModel::where("frq_id",$frq->frq_id)->update([
                            "delivery_place"=>2
                        ]);
                    }
                    if($frq->sales_currency == 12){
                        FrqModel::where("frq_id",$frq->frq_id)->update([
                            "delivery_place"=>12
                        ]);
                    }

                    if($frq->sales_currency == 13){
                        FrqModel::where("frq_id",$frq->frq_id)->update([
                            "delivery_place"=>13
                        ]);
                    }

                }
            });

    }
}

