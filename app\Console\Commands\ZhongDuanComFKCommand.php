<?php

namespace App\Console\Commands;


use App\Exceptions\InvalidRequestException;
use App\Exceptions\PurException;
use App\Http\IdSender\IdSender;
use App\Http\Models\EndCustInfoModel;
use App\Http\Models\PurchaseItemsModel;
use App\Http\Models\PurchaseOrderModel;
use App\Http\Models\PurchasePlanModel;
use App\Http\Models\ScmBrandModel;
use App\Http\Models\ScmOrderModel;
use App\Http\Models\ScmOrderTemporaryModel;
use App\Http\Models\StockInItemModel;
use App\Http\Models\StockInModel;
use App\Http\Services\EndCustInfoService;
use App\Http\Services\EndCustService;
use App\Http\Services\PurOrderService;
use App\Http\Services\SupplierDeliveryService;
use App\Http\Services\Sync\ScmOrderSyncService;
use App\Http\Services\Sync\StockInSyncService;
use App\Http\Services\Sync\StockInSyncWMSService;
use App\Imports\ErpStockListImport;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Http;
use function Hprose\Future\error;

class ZhongDuanComFKCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:zhongduan_com_fongkong';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '终端公司风控管理';


    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle($arguments = [])
    {

        self::fengkong();
    }

    public static function fengkong()
    {
        \DB::connection("mysql")->table('lie_end_cust_info')->select("*")
            ->where("end_cust_status",1)
            ->where("entity_list_status",0)
            ->orderBy("end_cust_id","desc")
            ->chunk(50, function ($list)  {
                foreach($list as $item){
                    try{
                        $requestData = $tycInfo = EndCustService::getTYCCompanyInfo($item->end_cust_cn);
                        \Log::error(sprintf("终端公司脚本，天眼查,公司名称:%s,返回:%s",$item->end_cust_cn,json_encode($requestData)));
                        $requestData['endCustCn'] = $item->end_cust_cn;
                        $requestData['endCustEn'] = $item->end_cust_en;
                        dump($tycInfo);

                        if(!empty($tycInfo["com_addr"])){
                            $updateEndCustInfo = [
                                "com_addr"=>$tycInfo["com_addr"] ?? "",
                                "registered_capital"=>$tycInfo["registered_capital"]?? "",
                                "establishment_time"=>$tycInfo["establishment_time"] ?? 0,
                                "legal_representative"=>$tycInfo["legal_representative"] ?? ""
                            ];
                            EndCustInfoModel::where("end_cust_id",$item->end_cust_id)->update($updateEndCustInfo);
                        }
                        if($item->create_uid && $item->create_name){
                            request()->offsetSet("user", (object)["userId" => $item->create_uid, "name" => $item->create_name]);
                        }else{
                            request()->offsetSet("user", (object)["userId" => 1000, "name" => "admin"]);
                        }

                        //获取实体名单状态
                        $addressData = [];
                        if(!empty($tycInfo["com_addr"])){
                            $addressData = [
                                [
                                    "source_id"=>$item->end_cust_id,
                                    "source_code"=>$item->end_cust_id,
                                    "address"=>$tycInfo["com_addr"] ?? "",
                                    "address_type"=>"采购终端地址",
                                ]
                            ];
                        }
                        $requestData["address_data"]    =   $addressData;
                        $endCustInfo = (new EndCustInfoService())->getCompanyEntityListStatus($requestData);
                        dump($endCustInfo);
                        //英文
                        $endCustInfoEn = (new EndCustInfoService())->getCompanyEntityListStatus([
                            'endCustCn'=>$item->end_cust_en,
                            'endCustEn'=>$item->end_cust_cn ?? '',
                            'com_addr'=> $tycInfo["com_addr"] ?? "",
                            'address_data'=> $addressData ,
                        ]);
                        dump($endCustInfoEn);

//                        dump($endCustInfoEn);
                        $endCustInfo['result'] = EndCustInfoService::compareEntityResult($endCustInfo['result'],$endCustInfoEn['result']);

                        \Log::error(sprintf("终端公司脚本，天眼查,公司名称:%s,result:%s",$item->end_cust_cn,$endCustInfo['result'] ));


                        if(in_array($endCustInfo['result'],[-3,-2,-1,0,1])){
                            EndCustInfoModel::where("end_cust_id",$item->end_cust_id)->update([
                                'entity_list_status'   => $endCustInfo['result'],
                            ]);
                        }


                    }catch (\Throwable $e){
                        \Log::error(sprintf("%s%s","终端请求风控异常:",$e->getMessage()));
                    }

                }
            });

    }

}
