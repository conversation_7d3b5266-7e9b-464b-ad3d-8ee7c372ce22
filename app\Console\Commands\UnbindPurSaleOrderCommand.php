<?php

namespace App\Console\Commands;


use App\Exceptions\InvalidRequestException;
use App\Exceptions\PurException;
use App\Http\IdSender\IdSender;
use App\Http\Models\PaymentItemsModel;
use App\Http\Models\PurchaseItemsModel;
use App\Http\Models\PurchaseOrderModel;
use App\Http\Models\PurchasePlanModel;
use App\Http\Models\ScmBrandModel;
use App\Http\Models\ScmOrderModel;
use App\Http\Models\ScmOrderTemporaryModel;
use App\Http\Models\StockInItemModel;
use App\Http\Models\StockInModel;
use App\Http\Services\PurOrderService;
use App\Http\Services\SupplierDeliveryService;
use App\Http\Services\Sync\ScmOrderSyncService;
use App\Http\Services\Sync\StockInSyncService;
use App\Http\Services\Sync\StockInSyncWMSService;
use App\Imports\ErpStockListImport;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Http;

class UnbindPurSaleOrderCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:unbind_pur_saleOrder';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '采销解绑，自动作废采购明细';


    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle($arguments = [])
    {
        try{
            DB::beginTransaction();
            ini_set('memory_limit', '512M');
            self::invalidStockIn();
            DB::commit();
        }catch (\Throwable $e){
            DB::rollBack();
            \Log::info((string)$e);
        }

    }

    public static function invalidStockIn()
    {
        $purOderServiceObj = new PurOrderService;
//        DB::connection("mysql")->table('lie_purchase_items')->where("approve_status",2)->where("frq_id",0)->where("status",1)
        DB::connection("mysql")->table('lie_purchase_items')->where("approve_status",2)->where("frq_id",0)->where("status",1)->orderBy("purchase_item_id")->chunk(50, function ($purOrderItems) use($purOderServiceObj)  {
            $purItemIds = [];
            foreach($purOrderItems as $purOrderItem){
                array_push($purItemIds,$purOrderItem->purchase_item_id);
            }

            //获取已付金额
            $amountPaid = PaymentItemsModel::getAmountPaidFromPurItems($purItemIds);
            $amountPaid = arrayChangeKeyByField($amountPaid, "purchase_item_id");//purchase_item_id,total_amount_paid

            //付款退款金额
            $amountPaidReturn = PaymentItemsModel::getPayAmountRefundFromPurItem($purItemIds);
            $amountPaidReturn = arrayChangeKeyByField($amountPaidReturn,
                "purchase_item_id");

//            dump($amountPaid);
//            dump($amountPaidReturn);

            foreach($purOrderItems as $purOrderItem){
                $purOrderItem = collect($purOrderItem)->toArray();
                $itemAmountPaid = $amountPaid[$purOrderItem["purchase_item_id"]]["total_amount_paid"] ?? 0;
                $itemAmountPaidReturn = $amountPaidReturn[$purOrderItem["purchase_item_id"]]["total_amount_paid_return"] ?? 0;
                if($itemAmountPaid > 0 && $itemAmountPaid == abs($itemAmountPaidReturn)){
                    //已付款  已付金额等于退款金额
                    //作废该明细
                    try{
                        dump($purOrderItem['purchase_id']);
//                        $purOderServiceObj->deletePurOrderDetailItemsList([
//                            "purchase_item_ids"=>$purOrderItem["purchase_item_id"],
//                            "type"=>2,
//                            "reason"=>"采购解绑，系统自动作废"
//                        ]);
                    }catch (\Throwable $e){
                        \Log::info((string)$e);
                        $this->sendText(sprintf("异步脚本 采销解绑失败:%s",(string)$e));
                    }

                }
            }
        });

    }
    /**
     * 发送文本类型的消息
     *
     * @param $content string 消息内容
     * @param array $atMobiles 被@人的手机号
     * @param bool $isAtAll 是否 @ 所有人
     * @throws SendErrorException
     */
    public function sendText($content, $atMobiles = [], $isAtAll = false)
    {
        $params = [
            'msgtype' => 'text',
            'text' => [
                'content' => $content,
            ],
            'at' => [
                'atMobiles' => $atMobiles,
                'isAtAll' => $isAtAll
            ]
        ];
        $this->send($params);
    }

    /**
     * 发送
     * @param array $params 请求需要的参数
     * @throws SendErrorException
     */
    private function send($params = [])
    {
        if (!config('monitorDing.enabled')) {
            \Log::info('~~ Monitor Ding ~~');
            \Log::info($params);
        } else {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, config("monitorDing.webhook"));
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
            curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json;charset=utf-8'));
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($params));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            if (config()) {
                curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
            }
            $data = json_decode(curl_exec($ch), true);
            curl_close($ch);

            if ($data['errcode']) {
//                throw new SendErrorException($data['errmsg']);
            }
        }

    }

}
